using System.ComponentModel.DataAnnotations.Schema;

namespace CollaboratorsGS.Domain.Entities
{
    public class CandidateEmploymentHistory
    {
        public Guid HistoryId { get; set; }
        public Guid CandidateId { get; set; }
        public string CompanyName { get; set; } = string.Empty;
        public string Position { get; set; } = string.Empty;
        public DateTime StartDate { get; set; }
        public DateTime? EndDate { get; set; }
        public string? ReasonForLeaving { get; set; }

        // Navigation properties
        public Candidate? Candidate { get; set; }
    }
}
