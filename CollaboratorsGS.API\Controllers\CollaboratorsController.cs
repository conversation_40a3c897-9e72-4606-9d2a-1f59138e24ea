using CollaboratorsGS.Application.Constants;
using CollaboratorsGS.Application.DTOs;
using CollaboratorsGS.Application.DTOs.Collaborator;
using CollaboratorsGS.Application.Interfaces;
using CollaboratorsGS.Infrastructure.Utilities;
using CollaboratorsGS.Infrastructure.Utils;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Security.Claims;

namespace CollaboratorsGS.API.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class CollaboratorsController : ControllerBase
    {
        private readonly ICollaboratorService _collaboratorService;
        private readonly IActivityLogService _activityLogService;
        private readonly IFileStorageService _fileStorageService;
        private readonly ILogger<CollaboratorsController> _logger;

        public CollaboratorsController(
            ICollaboratorService collaboratorService,
            IActivityLogService activityLogService,
            IFileStorageService fileStorageService,
            ILogger<CollaboratorsController> logger)
        {
            _collaboratorService = collaboratorService;
            _activityLogService = activityLogService;
            _fileStorageService = fileStorageService;
            _logger = logger;
        }

        // GET: api/Collaborators
        [HttpGet]
        [Authorize(Roles = RolesUser.AdminManager)]
        public async Task<IActionResult> GetAll()
        {
            try
            {
                var collaborators = await _collaboratorService.GetAllAsync();
                return Ok(ApiResponse<object>.SuccessResponse(
                    MessageCodes.SC2000,
                    "Get all collaborators successfully",
                    collaborators));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting all collaborators");
                return StatusCode(500, ApiResponse<object>.ErrorResponse(
                    MessageCodes.ER5000,
                    "Internal server error",
                    500));
            }
        }
        // GET: api/Collaborator/GetByUserId
        [HttpGet("user/{userId}")]
        [Authorize(Roles = RolesUser.AdminManagerRecruiterCollaborator)]
        public async Task<IActionResult> GetByUserId(Guid userId)
        {
            var collaborators = await _collaboratorService.GetByUserIdAsync(userId);
            try
            {
                if (collaborators == null)
                {
                    return NotFound(ApiResponse<object>.ErrorResponse(
                        MessageCodes.ER4004,
                        $"No collaborator found for user ID: {userId}",
                        404));
                }
                return Ok(ApiResponse<object>.SuccessResponse(
                    MessageCodes.SC2000,
                    "Get collaborator by user ID successfully",
                    collaborators));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting all collaborators");
                return StatusCode(500, ApiResponse<object>.ErrorResponse(
                    MessageCodes.ER5000,
                    "Internal server error",
                    500));
            }
        }

        // GET: api/Collaborators/5
        [HttpGet("{id}")]
        [Authorize(Roles = RolesUser.AdminManagerRecruiterCollaborator)]
        public async Task<IActionResult> GetById(Guid id)
        {
            try
            {
                var collaborator = await _collaboratorService.GetByIdAsync(id);
                if (collaborator == null)
                {
                    return NotFound(ApiResponse<object>.ErrorResponse(
                         MessageCodes.ER4004,
                         "Collaborator not found",
                         404));
                }
                return Ok(ApiResponse<object>.SuccessResponse(
                    MessageCodes.SC2000,
                    "Get collaborator successfully",
                    collaborator));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting collaborator by ID: {Id}", id);
                return StatusCode(500, ApiResponse<object>.ErrorResponse(
                    MessageCodes.ER5000,
                    "Internal server error",
                    500));
            }
        }

        // GET: api/Collaborators/status/Active
        [HttpGet("status/{status}")]
        [Authorize(Roles = RolesUser.AdminManager)]
        public async Task<IActionResult> GetByStatus(string status)
        {
            try
            {
                var collaborators = await _collaboratorService.GetByStatusAsync(status);
                return Ok(ApiResponse<object>.SuccessResponse(
                    MessageCodes.SC2000,
                    "Get collaborators by status successfully",
                    collaborators));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting collaborators by status: {Status}", status);
                return StatusCode(500, ApiResponse<object>.ErrorResponse(
                    MessageCodes.ER5000,
                    "Internal server error",
                    500));
            }
        }

        // POST: api/Collaborators
        [HttpPost]
        [Authorize]
        public async Task<IActionResult> Create([FromBody] CreateCollaboratorRequest request)
        {
            try
            {
                // Get the current user's ID from the token (stored in 'sub' claim)
                var userIdClaim = User.FindFirst(System.Security.Claims.ClaimTypes.NameIdentifier)
                    ?? User.FindFirst("sub");

                if (userIdClaim == null || !Guid.TryParse(userIdClaim.Value, out var userId))
                {
                    return BadRequest(ApiResponse<object>.ErrorResponse(
                        MessageCodes.ER4001,
                        "User ID not found in token or invalid",
                        400,
                        new List<ErrorDetail>
                        {
                            new ErrorDetail
                            {
                                Field = "token",
                                ErrorCode = MessageCodes.ER4001,
                                Message = "Invalid user ID in token"
                            }
                        }));
                }

                var collaboratorId = await _collaboratorService.CreateCollaboratorAsync(userId, request);
                var createdCollaborator = await _collaboratorService.GetCreatedCollaboratorAsync(collaboratorId);

                if (createdCollaborator == null)
                    return StatusCode(500, ApiResponse<object>.ErrorResponse(
                        MessageCodes.ER5000,
                        "Failed to retrieve created collaborator",
                        500));

                return CreatedAtAction(nameof(GetById), new { id = collaboratorId },
                    ApiResponse<object>.SuccessResponse(
                        MessageCodes.SC2001,
                        "Collaborator created successfully",
                        createdCollaborator,
                        201));
            }
            catch (InvalidOperationException ex)
            {
                _logger.LogWarning(ex, "Validation error creating collaborator");
                return BadRequest(ApiResponse<object>.ErrorResponse(
                    MessageCodes.ER4005,
                    ex.Message,
                    400,
                    new List<ErrorDetail>
                    {
                        new ErrorDetail
                        {
                            Field = "request",
                            ErrorCode = MessageCodes.ER4005,
                            Message = ex.Message
                        }
                    }));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating collaborator");
                return StatusCode(500, ApiResponse<object>.ErrorResponse(
                    MessageCodes.ER5000,
                    "Internal server error",
                    500));
            }
        }

        // PUT: api/Collaborators/update/information
        [HttpPut("update/information")]
        [Authorize(Roles = "Collaborator")]
        public async Task<IActionResult> UpdateInformation([FromBody] UpdateInformationRequest request)
        {
            try
            {
                // Validate model state
                if (!ModelState.IsValid)
                {
                    return ValidationHelper.CreateValidationErrorResponse(ModelState);
                }

                // Get the current user's ID from the token
                var userIdClaim = User.FindFirst(System.Security.Claims.ClaimTypes.NameIdentifier)
                    ?? User.FindFirst("sub");

                if (userIdClaim == null || !Guid.TryParse(userIdClaim.Value, out var userId))
                {
                    return BadRequest(ApiResponse<object>.ErrorResponse(
                        MessageCodes.ER4001,
                        "User ID not found in token or invalid",
                        400,
                        new List<ErrorDetail>
                        {
                            new ErrorDetail
                            {
                                Field = "token",
                                ErrorCode = MessageCodes.ER4001,
                                Message = "Invalid user ID in token"
                            }
                        }));
                }

                // Get the collaborator for this user
                var collaborator = await _collaboratorService.GetByUserIdAsync(userId);
                if (collaborator == null)
                {
                    return BadRequest(ApiResponse<object>.ErrorResponse(
                       MessageCodes.ER4004,
                       "No collaborator found for the current user",
                       400));
                }

                var updatedCollaborator = await _collaboratorService.UpdateInformationAsync(collaborator.CollaboratorId, request);
                if (updatedCollaborator == null)
                {
                    return NotFound(ApiResponse<object>.ErrorResponse(
                        MessageCodes.ER4004,
                        "Collaborator not found",
                        404));
                }
                return Ok(ApiResponse<object>.SuccessResponse(
                    MessageCodes.SC2002,
                    "Collaborator information updated successfully",
                    updatedCollaborator));
            }
            catch (InvalidOperationException ex)
            {
                _logger.LogWarning(ex, "Validation error updating collaborator");
                return BadRequest(ApiResponse<object>.ErrorResponse(
                   MessageCodes.ER4005,
                   ex.Message,
                   400,
                   new List<ErrorDetail>
                   {
                        new ErrorDetail
                        {
                            Field = "request",
                            ErrorCode = MessageCodes.ER4005,
                            Message = ex.Message
                        }
                   }));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating collaborator");
                return StatusCode(500, ApiResponse<object>.ErrorResponse(
                    MessageCodes.ER5000,
                    "Internal server error",
                    500));
            }
        }

        // PUT: api/Collaborators/profile
        [HttpPut("profile")]
        [Authorize(Roles = "Collaborator")]
        public async Task<IActionResult> UpdateProfile([FromBody] UpdateCollaboratorProfileRequest request)
        {
            try
            {
                // Validate model state
                if (!ModelState.IsValid)
                {
                    return ValidationHelper.CreateValidationErrorResponse(ModelState);
                }

                // Get the current user's ID from the token
                var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier)
                    ?? User.FindFirst("sub");

                if (userIdClaim == null || !Guid.TryParse(userIdClaim.Value, out var userId))
                {
                    return BadRequest(ApiResponse<object>.ErrorResponse(
                        MessageCodes.ER4001,
                        "User ID not found in token or invalid",
                        400,
                        new List<ErrorDetail>
                        {
                            new ErrorDetail
                            {
                                Field = "token",
                                ErrorCode = MessageCodes.ER4001,
                                Message = "Invalid user ID in token"
                            }
                        }));
                }

                // Get the collaborator ID for this user
                var collaborator = await _collaboratorService.GetByUserIdAsync(userId);
                if (collaborator == null)
                {
                    // Log detailed information for debugging
                    _logger.LogWarning("No collaborator found for UserId: {UserId}", userId);
                    return BadRequest(ApiResponse<object>.ErrorResponse(
                        MessageCodes.ER4004,
                        $"No collaborator found for the current user. UserId: {userId}",
                        400));
                }

                // Log the collaborator information for debugging
                _logger.LogInformation("Processing profile update for CollaboratorId: {CollaboratorId}, UserId: {UserId}, Email: {Email}",
                    collaborator.CollaboratorId, userId, collaborator.Email);

                // Use the collaborator ID from the authenticated user
                // This ensures the user can only update their own profile
                var result = await _collaboratorService.UpdateCollaboratorProfileAsync(collaborator.CollaboratorId, request);
                if (result == null)
                {
                    return NotFound(ApiResponse<object>.ErrorResponse(
                        MessageCodes.ER4004,
                        "Profile not found",
                        404));
                }
                return Ok(ApiResponse<object>.SuccessResponse(
                    MessageCodes.SC2002,
                    "Profile updated successfully",
                    result));
            }
            catch (InvalidOperationException ex)
            {
                _logger.LogWarning(ex, "Validation error updating collaborator profile");
                return BadRequest(ApiResponse<object>.ErrorResponse(
                    MessageCodes.ER4005,
                    ex.Message,
                    400,
                    new List<ErrorDetail>
                    {
                        new ErrorDetail
                        {
                            Field = "request",
                            ErrorCode = MessageCodes.ER4005,
                            Message = ex.Message
                        }
                    }));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating collaborator profile");
                return StatusCode(500, ApiResponse<object>.ErrorResponse(
                    MessageCodes.ER5000,
                    "Internal server error",
                    500));
            }
        }

        // PUT: api/Collaborators/profile/from-extraction
        [HttpPut("profile/from-extraction")]
        [Authorize(Roles = "Collaborator")]
        public async Task<IActionResult> UpdateProfileFromExtraction([FromBody] UpdateProfileFromExtractionRequest request)
        {
            try
            {
                // Validate model state
                if (!ModelState.IsValid)
                {
                    return ValidationHelper.CreateValidationErrorResponse(ModelState);
                }

                // Get the current user's ID from the token
                var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier) ?? User.FindFirst("sub");
                if (userIdClaim == null || !Guid.TryParse(userIdClaim.Value, out var userId))
                {
                    return BadRequest(ApiResponse<object>.ErrorResponse(
                        MessageCodes.ER4001,
                        "Invalid user ID in token",
                        400,
                        new List<ErrorDetail>
                        {
                            new ErrorDetail
                            {
                                Field = "token",
                                ErrorCode = MessageCodes.ER4001,
                                Message = "Invalid user ID in token"
                            }
                        }));
                }

                // Get the collaborator ID for this user
                var collaborator = await _collaboratorService.GetByUserIdAsync(userId);
                if (collaborator == null)
                {
                    _logger.LogWarning("No collaborator found for UserId: {UserId}", userId);
                    return BadRequest(ApiResponse<object>.ErrorResponse(
                        MessageCodes.ER4004,
                        $"No collaborator found for the current user. UserId: {userId}",
                        400));
                }

                _logger.LogInformation("Processing profile update from extraction for CollaboratorId: {CollaboratorId}", collaborator.CollaboratorId);

                // Update profile with extracted data
                var result = await _collaboratorService.UpdateProfileFromExtractionAsync(collaborator.CollaboratorId, request);
                if (result == null)
                {
                    return BadRequest(ApiResponse<object>.ErrorResponse(
                        MessageCodes.ER4005,
                        "Failed to update profile with extraction data",
                        400));
                }

                return Ok(ApiResponse<object>.SuccessResponse(
                    MessageCodes.SC2002,
                    "Profile updated from extraction successfully",
                    result));
            }
            catch (InvalidOperationException ex)
            {
                _logger.LogWarning(ex, "Validation error updating collaborator profile from extraction");
                return BadRequest(ApiResponse<object>.ErrorResponse(
                    MessageCodes.ER4005,
                    ex.Message,
                    400,
                    new List<ErrorDetail>
                    {
                        new ErrorDetail
                        {
                            Field = "request",
                            ErrorCode = MessageCodes.ER4005,
                            Message = ex.Message
                        }
                    }));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating collaborator profile from extraction");
                return StatusCode(500, ApiResponse<object>.ErrorResponse(
                    MessageCodes.ER5000,
                    "Internal server error",
                    500));
            }
        }

        // POST: api/Collaborators/debug/test-extraction
        [HttpPost("debug/test-extraction")]
        [Authorize(Roles = "Collaborator")]
        public async Task<IActionResult> TestExtraction([FromBody] UpdateProfileFromExtractionRequest request)
        {
            try
            {
                _logger.LogInformation("Received extraction request: {@Request}", request);

                // Get the current user's ID from the token
                var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier) ?? User.FindFirst("sub");
                if (userIdClaim == null || !Guid.TryParse(userIdClaim.Value, out var userId))
                {
                    return BadRequest(ApiResponse<object>.ErrorResponse(
                        MessageCodes.ER4001,
                        "Invalid user ID in token",
                        400,
                        new List<ErrorDetail>
                        {
                            new ErrorDetail
                            {
                                Field = "token",
                                ErrorCode = MessageCodes.ER4001,
                                Message = "Invalid user ID in token"
                            }
                        }));
                }

                // Get the collaborator ID for this user
                var collaborator = await _collaboratorService.GetByUserIdAsync(userId);
                if (collaborator == null)
                {
                    return BadRequest(ApiResponse<object>.ErrorResponse(
                        MessageCodes.ER4004,
                        $"No collaborator found for the current user. UserId: {userId}",
                        400));
                }

                _logger.LogInformation("Found collaborator: {@Collaborator}", collaborator);
                _logger.LogInformation("Extraction info: {@Info}", request.Info);

                var debugData = new
                {
                    Message = "Debug successful",
                    UserId = userId,
                    CollaboratorId = collaborator.CollaboratorId,
                    ExtractionData = request.Info
                };
                return Ok(ApiResponse<object>.SuccessResponse(
                    MessageCodes.SC2000,
                    "Test extraction successful",
                    debugData));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in test extraction");
                return StatusCode(500, ApiResponse<object>.ErrorResponse(
                    MessageCodes.ER5000,
                    ex.Message,
                    500));
            }
        }

        // POST: api/Collaborators/upload/avatar
        [HttpPost("upload/avatar")]
        [Authorize(Roles = "Collaborator")]
        public async Task<IActionResult> UploadAvatar(IFormFile file)
        {
            try
            {
                if (file == null || file.Length == 0)
                {
                    return BadRequest(ApiResponse<object>.ErrorResponse(
                        MessageCodes.ER4002,
                        "No file uploaded",
                        400,
                        new List<ErrorDetail>
                        {
                            new ErrorDetail
                            {
                                Field = "file",
                                ErrorCode = MessageCodes.ER4002,
                                Message = "File is required"
                            }
                        }));
                }

                // Validate file type

                if (!FileUtils.IsImage(file))
                {
                    return BadRequest(ApiResponse<object>.ErrorResponse(
                        MessageCodes.ER4003,
                        "Only image files (JPEG, PNG, GIF) are allowed",
                        400,
                        new List<ErrorDetail>
                        {
                            new ErrorDetail
                            {
                                Field = "file",
                                ErrorCode = MessageCodes.ER4003,
                                Message = "Invalid file type"
                            }
                        }));
                }

                // Validate file size (max 5MB)
                if (!FileUtils.IsAcceptableSize(file))
                {
                    return BadRequest(ApiResponse<object>.ErrorResponse(
                        MessageCodes.ER4003,
                        "File size cannot exceed 5MB",
                        400,
                        new List<ErrorDetail>
                        {
                            new ErrorDetail
                            {
                                Field = "file",
                                ErrorCode = MessageCodes.ER4003,
                                Message = "File size too large"
                            }
                        }));
                }

                // Get current user
                var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier) ?? User.FindFirst("sub");
                if (userIdClaim == null || !Guid.TryParse(userIdClaim.Value, out var userId))
                {
                    return BadRequest(ApiResponse<object>.ErrorResponse(
                        MessageCodes.ER4001,
                        "Invalid user ID in token",
                        400,
                        new List<ErrorDetail>
                        {
                            new ErrorDetail
                            {
                                Field = "token",
                                ErrorCode = MessageCodes.ER4001,
                                Message = "Invalid user ID in token"
                            }
                        }));
                }

                var collaborator = await _collaboratorService.GetByUserIdAsync(userId);
                if (collaborator == null)
                {
                    return BadRequest(ApiResponse<object>.ErrorResponse(
                        MessageCodes.ER4004,
                        "No collaborator found for the current user",
                        400));
                }
                // Upload file using existing file storage service
                var fileName = await _fileStorageService.UploadFileAsync(file);

                string fileType = FileUtils.GetFileExtension(file);
                // Generate file URL using the existing pattern
                // var fileUrl = $"{Request.Scheme}://{Request.Host}/api/files/{Path.GetFileNameWithoutExtension(fileName)}";

                // Update collaborator profile with avatar URL
                var fileUrl = await _fileStorageService.GetTemporaryUrlAsync(fileName);
                await _collaboratorService.UpdateAvatarAsync(collaborator.CollaboratorId, fileName);

                var responseData = new
                {
                    AvatarUrl = fileName,
                    Message = "Avatar uploaded successfully"
                };
                return Ok(ApiResponse<object>.SuccessResponse(
                                    MessageCodes.SC2001,
                                    responseData.Message,
                                    fileUrl));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error uploading avatar");
                return StatusCode(500, ApiResponse<object>.ErrorResponse(
                   MessageCodes.ER5000,
                   "Internal server error",
                   500));
            }
        }

        // GET: api/Collaborators/debug/current-user
        [HttpGet("debug/current-user")]
        [Authorize(Roles = "Collaborator")]
        public async Task<IActionResult> GetCurrentUserDebugInfo()
        {
            try
            {
                // Get user ID from JWT claims
                var userIdClaim = User.FindFirst("sub") ?? User.FindFirst("userId") ?? User.FindFirst(ClaimTypes.NameIdentifier);
                if (userIdClaim == null || !Guid.TryParse(userIdClaim.Value, out var userId))
                {
                    return BadRequest(ApiResponse<object>.ErrorResponse(
                        MessageCodes.ER4001,
                        "Invalid user ID in token",
                        400,
                        new List<ErrorDetail>
                        {
                            new ErrorDetail
                            {
                                Field = "token",
                                ErrorCode = MessageCodes.ER4001,
                                Message = "Invalid user ID in token"
                            }
                        }));
                }

                var collaborator = await _collaboratorService.GetByUserIdAsync(userId);

                var debugInfo = new
                {
                    UserId = userId,
                    UserClaims = User.Claims.Select(c => new { c.Type, c.Value }).ToList(),
                    Collaborator = collaborator != null ? new
                    {
                        collaborator.CollaboratorId,
                        collaborator.FullName,
                        collaborator.Email,
                        collaborator.PhoneNumber,
                        collaborator.Status
                    } : null
                };

                return Ok(ApiResponse<object>.SuccessResponse(
                    MessageCodes.SC2000,
                    "Debug info retrieved successfully",
                    debugInfo));
            }
            catch (Exception ex)
            {
                return BadRequest(ApiResponse<object>.ErrorResponse(
                    MessageCodes.ER5000,
                    $"Debug error: {ex.Message}",
                    400));
            }
        }

        // POST: api/Collaborators/approve
        [HttpPost("approve")]
        [Authorize(Roles = "Admin,Manager")]
        public async Task<IActionResult> Approve([FromBody] ApproveCollaboratorRequest request)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    var errors = ModelState
                        .Where(x => x.Value?.Errors.Count > 0)
                        .SelectMany(x => x.Value!.Errors.Select(e => new ErrorDetail
                        {
                            Field = x.Key,
                            ErrorCode = MessageCodes.VE6001,
                            Message = e.ErrorMessage
                        }))
                        .ToList();

                    return BadRequest(ApiResponse<object>.ErrorResponse(
                        MessageCodes.VE6001,
                        "Validation failed",
                        400,
                        errors));
                }
                var approvedCollaborator = await _collaboratorService.ApproveCollaboratorAsync(request);
                if (approvedCollaborator == null)
                {
                    return NotFound(ApiResponse<object>.ErrorResponse(
                        MessageCodes.ER4004,
                        "Collaborator not found",
                        404));
                }
                return Ok(ApiResponse<object>.SuccessResponse(
                    MessageCodes.SC2002,
                    "Collaborator approved successfully",
                    approvedCollaborator));
            }
            catch (InvalidOperationException ex)
            {
                _logger.LogWarning(ex, "Validation error approving collaborator");
                return BadRequest(ApiResponse<object>.ErrorResponse(
                    MessageCodes.ER4005,
                    ex.Message,
                    400,
                    new List<ErrorDetail>
                    {
                        new ErrorDetail
                        {
                            Field = "request",
                            ErrorCode = MessageCodes.ER4005,
                            Message = ex.Message
                        }
                    }));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error approving collaborator");
                return StatusCode(500, ApiResponse<object>.ErrorResponse(
                    MessageCodes.ER5000,
                    "Internal server error",
                    500));
            }
        }

        // DELETE: api/Collaborators/5
        [HttpDelete("{id}")]
        [Authorize(Roles = "Admin")]
        public async Task<IActionResult> Delete(Guid id)
        {
            try
            {
                var result = await _collaboratorService.DeleteCollaboratorAsync(id);
                if (!result)
                {
                    return NotFound(ApiResponse<object>.ErrorResponse(
                        MessageCodes.ER4004,
                        "Collaborator not found",
                        404));
                }
                return Ok(ApiResponse<object>.SuccessResponse(
                    MessageCodes.SC2003,
                    "Collaborator deleted successfully",
                    true));
            }
            catch (InvalidOperationException ex)
            {
                _logger.LogWarning(ex, "Validation error deleting collaborator");
                return BadRequest(ApiResponse<object>.ErrorResponse(
                    MessageCodes.ER4005,
                    ex.Message,
                    400,
                    new List<ErrorDetail>
                    {
                        new ErrorDetail
                        {
                            Field = "id",
                            ErrorCode = MessageCodes.ER4005,
                            Message = ex.Message
                        }
                    }));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting collaborator");
                return StatusCode(500, ApiResponse<object>.ErrorResponse(
                    MessageCodes.ER5000,
                    "Internal server error",
                    500));
            }
        }

        // GET: api/Collaborators/activities?limit=10
        [HttpGet("activities")]
        [Authorize(Roles = RolesUser.AdminManagerRecruiterCollaborator)]
        public async Task<IActionResult> GetRecentActivities([FromQuery] int limit = 10)
        {
            try
            {
                // Get userId from token claims
                var userIdClaim = User.FindFirst(System.Security.Claims.ClaimTypes.NameIdentifier)
                    ?? User.FindFirst("sub");

                if (userIdClaim == null || !Guid.TryParse(userIdClaim.Value, out var userId))
                {
                    return BadRequest(ApiResponse<object>.ErrorResponse(
                        MessageCodes.ER4001,
                        "UserID not found in token or invalid",
                        400,
                        new List<ErrorDetail>
                        {
                            new ErrorDetail
                            {
                                Field = "token",
                                ErrorCode = MessageCodes.ER4001,
                                Message = "Invalid userID in token"
                            }
                        }));
                }

                // Find collaborator by userId
                var collaborator = await _collaboratorService.GetByUserIdAsync(userId);
                if (collaborator == null)
                {
                    return NotFound(ApiResponse<object>.ErrorResponse(
                        MessageCodes.ER4004,
                        "Collaborator not found for current user",
                        404));
                }

                var activities = await _activityLogService.GetRecentActivitiesAsync(collaborator.CollaboratorId, limit);
                return Ok(ApiResponse<object>.SuccessResponse(
                    MessageCodes.SC2000,
                    "Get activity successfully",
                    activities));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting recent activities for user {UserId}", User.FindFirst("sub")?.Value);
                return StatusCode(500, ApiResponse<object>.ErrorResponse(
                    MessageCodes.ER5000,
                    "Internal server error",
                    500));
            }
        }

        [HttpGet("referal-history")]
        [Authorize(Roles = RolesUser.AdminManagerRecruiterCollaborator)]
        public async Task<IActionResult> GetReferalHistory()
        {
            try
            {
                // Get userId from token claims
                var userIdClaim = User.FindFirst(System.Security.Claims.ClaimTypes.NameIdentifier)
                    ?? User.FindFirst("sub");

                if (userIdClaim == null || !Guid.TryParse(userIdClaim.Value, out var userId))
                {
                    return BadRequest(ApiResponse<object>.ErrorResponse(
                        MessageCodes.ER4001,
                        "UserID not found in token or invalid",
                        400,
                        new List<ErrorDetail>
                        {
                            new ErrorDetail
                            {
                                Field = "token",
                                ErrorCode = MessageCodes.ER4001,
                                Message = "Invalid userID in token"
                            }
                        }));
                }

                var referalHistory = await _collaboratorService.GetReferalHistoryAsync(userId);
                if (referalHistory == null)
                {
                    return NotFound(ApiResponse<object>.ErrorResponse(
                        MessageCodes.ER4004,
                        "Referal history not found for current user",
                        404));
                }
                return Ok(ApiResponse<object>.SuccessResponse(
                    MessageCodes.SC2000,
                    "Level detail retrieved successfully",
                    referalHistory));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting referal history for user {UserId}", User.FindFirst("sub")?.Value);
                return StatusCode(500, ApiResponse<object>.ErrorResponse(
                    MessageCodes.ER5000,
                    "Internal server error",
                    500));
            }
        }

            // GET: api/collaborators/level-detail
            [HttpGet("level-detail")]
            [Authorize(Roles = RolesUser.AdminManagerRecruiterCollaborator)]
            public async Task<IActionResult> GetLevelDetail()
            {
                try
                {
                    // Get userId from token claims
                    var userIdClaim = User.FindFirst(System.Security.Claims.ClaimTypes.NameIdentifier)
                        ?? User.FindFirst("sub");

                    if (userIdClaim == null || !Guid.TryParse(userIdClaim.Value, out var userId))
                    {
                        return BadRequest(ApiResponse<object>.ErrorResponse(
                            MessageCodes.ER4001,
                            "UserID not found in token or invalid",
                            400,
                            new List<ErrorDetail>
                            {
                            new ErrorDetail
                            {
                                Field = "token",
                                ErrorCode = MessageCodes.ER4001,
                                Message = "Invalid userID in token"
                            }
                            }));
                    }

                    var levelDetail = await _collaboratorService.GetLevelDetailByUserIdAsync(userId);
                    return Ok(ApiResponse<object>.SuccessResponse(
                        MessageCodes.SC2000,
                        "Level detail retrieved successfully",
                        levelDetail));
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error getting level detail for user {UserId}", User.FindFirst("sub")?.Value);
                    return StatusCode(500, ApiResponse<object>.ErrorResponse(
                        MessageCodes.ER5000,
                        "Internal server error",
                        500));
                }
            }
        
        // POST: api/collaborators/check-level-upgrade
        [HttpPost("check-level-upgrade")]
        [Authorize(Roles = RolesUser.AdminManagerRecruiterCollaborator)]
        public async Task<IActionResult> CheckLevelUpgrade()
        {
            try
            {
                // Get userId from token claims
                var userIdClaim = User.FindFirst(System.Security.Claims.ClaimTypes.NameIdentifier)
                    ?? User.FindFirst("sub");

                if (userIdClaim == null || !Guid.TryParse(userIdClaim.Value, out var userId))
                {
                    return BadRequest(ApiResponse<object>.ErrorResponse(
                        MessageCodes.ER4001,
                        "UserID not found in token or invalid",
                        400,
                        new List<ErrorDetail>
                        {
                            new ErrorDetail
                            {
                                Field = "token",
                                ErrorCode = MessageCodes.ER4001,
                                Message = "Invalid userID in token"
                            }
                        }));
                }

                var upgradeCheck = await _collaboratorService.CheckLevelUpgradeEligibilityAsync(userId);
                return Ok(ApiResponse<object>.SuccessResponse(
                    MessageCodes.SC2000,
                    "Level detail retrieved successfully",
                    upgradeCheck));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking level upgrade for user {UserId}", User.FindFirst("sub")?.Value);
                return StatusCode(500, ApiResponse<object>.ErrorResponse(
                    MessageCodes.ER5000,
                    "Internal server error" + ex.Message,
                    500));
            }
        }

    }
}
