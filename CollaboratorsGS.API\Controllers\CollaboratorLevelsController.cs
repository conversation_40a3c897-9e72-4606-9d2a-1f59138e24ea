using CollaboratorsGS.Application.Constants;
using CollaboratorsGS.Application.DTOs;
using CollaboratorsGS.Application.DTOs.CollaboratorLevel;
using CollaboratorsGS.Application.Interfaces;
using CollaboratorsGS.Infrastructure.Utilities;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace CollaboratorsGS.API.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class CollaboratorLevelsController : ControllerBase
    {
        private readonly ICollaboratorLevelService _CollaboratorLevelService;
        private readonly ILogger<CollaboratorLevelsController> _logger;

        public CollaboratorLevelsController(
            ICollaboratorLevelService CollaboratorLevelService,
            ILogger<CollaboratorLevelsController> logger)
        {
            _CollaboratorLevelService = CollaboratorLevelService;
            _logger = logger;
        }

        // GET: api/CtvLevels
        [HttpGet]
        [Authorize(Roles = RolesUser.AdminManagerRecruiterCollaborator)]
        public async Task<IActionResult> GetAll()
        {
            try
            {
                var levels = await _CollaboratorLevelService.GetAllAsync();
                return Ok(ApiResponse<object>.SuccessResponse(
                    MessageCodes.SC2000,
                    "Successfully retrieved all CTV levels",
                    levels
                ));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting all CTV levels");
                return StatusCode(500, ApiResponse<object>.ErrorResponse(
                    MessageCodes.ER5000,
                    "Internal server error",
                    500));
            }
        }

        // GET: api/CtvLevels/5
        [HttpGet("{id}")]
        [Authorize(Roles = RolesUser.AdminManagerRecruiterCollaborator)]
        public async Task<IActionResult> GetById(Guid id)
        {
            try
            {
                var level = await _CollaboratorLevelService.GetByIdAsync(id);
                if (level == null)
                {
                     return NotFound(ApiResponse<object>.ErrorResponse(
                        MessageCodes.ER4004,
                        "Collaborator level not found",
                        404));
                }
                return Ok(ApiResponse<object>.SuccessResponse(
                                       MessageCodes.SC2000,
                                       "Get collaborator level successfully",
                                       level));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting CTV level by ID: {Id}", id);
                return StatusCode(500, "Internal server error");
            }
        }

        // POST: api/CtvLevels
        [HttpPost]
        [Authorize(Roles = RolesUser.Admin)]
        public async Task<IActionResult> Create([FromBody] CreateCollaboratorLevelRequest request)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return ValidationHelper.CreateValidationErrorResponse(ModelState);
                }
                var levelId = await _CollaboratorLevelService.CreateCollaboratorLevelAsync(request);
                var createdLevel = await _CollaboratorLevelService.GetCreatedCollaboratorLevelAsync(levelId);

                if (createdLevel == null)
                    return StatusCode(500, ApiResponse<object>.ErrorResponse(
                        MessageCodes.ER5000,
                        "Failed to retrieve created CTV level",
                        500));

                return CreatedAtAction(nameof(GetById), new { id = levelId },
                    ApiResponse<object>.SuccessResponse(
                        MessageCodes.SC2001,
                        "Collaborator level created successfully",
                        createdLevel,
                        201));
            }
            catch (InvalidOperationException ex)
            {
                _logger.LogWarning(ex, "Validation error creating CTV level");
                return BadRequest(ApiResponse<object>.ErrorResponse(
                    MessageCodes.ER4005,
                    ex.Message,
                    400,
                    new List<ErrorDetail>
                    {
                        new ErrorDetail
                        {
                            Field = "request",
                            ErrorCode = MessageCodes.ER4005,
                            Message = ex.Message
                        }
                    }));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating CTV level");
                return StatusCode(500, ApiResponse<object>.ErrorResponse(
                    MessageCodes.ER5000,
                    ex.Message,
                    500));
            }
        }

        // PUT: api/CtvLevels
        [HttpPut]
        [Authorize(Roles = RolesUser.Admin)]
        public async Task<IActionResult> Update([FromBody] UpdateCollaboratorLevelRequest request)
        {
            try
            {
                var updatedLevel = await _CollaboratorLevelService.UpdateCollaboratorLevelAsync(request);
                if (updatedLevel == null)
                {
                    return NotFound(ApiResponse<object>.ErrorResponse(
                        MessageCodes.ER4004,
                        "Collaborator not found to upgrade level",
                        404));
                } 
                return Ok(ApiResponse<object>.SuccessResponse(
                                       MessageCodes.SC2000,
                                       "Updated collaborator level successfully",
                                       updatedLevel));
            }
            catch (InvalidOperationException ex)
            {
                _logger.LogWarning(ex, "Validation error updating CTV level");
                return BadRequest(ApiResponse<object>.ErrorResponse(
                    MessageCodes.ER4005,
                    ex.Message,
                    400,
                    new List<ErrorDetail>
                    {
                        new ErrorDetail
                        {
                            Field = "request",
                            ErrorCode = MessageCodes.ER4005,
                            Message = ex.Message
                        }
                    }));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating CTV level");
                return StatusCode(500, ApiResponse<object>.ErrorResponse(
                    MessageCodes.ER5000,
                    ex.Message,
                    500));
            }
        }

        // DELETE: api/CtvLevels/5
        [HttpDelete("{id}")]
        [Authorize(Roles = RolesUser.Admin)]
        public async Task<IActionResult> Delete(Guid id)
        {
            try
            {
                var result = await _CollaboratorLevelService.DeleteCollaboratorLevelAsync(id);
                if (!result)
                {
                    return BadRequest(ApiResponse<object>.ErrorResponse(
                        MessageCodes.ER4005,
                        "Cannot delete level because it is being used by collaborators",
                        400));
                }
                   
                return NoContent();
            }
            catch (InvalidOperationException ex)
            {
                _logger.LogWarning(ex, "Validation error deleting CTV level");
                return BadRequest(ApiResponse<object>.ErrorResponse(
                    MessageCodes.ER4005,
                    ex.Message,
                    400,
                    new List<ErrorDetail>
                    {
                        new ErrorDetail
                        {
                            Field = "request",
                            ErrorCode = MessageCodes.ER4005,
                            Message = ex.Message
                        }
                    }));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting CTV level");
                return StatusCode(500, ApiResponse<object>.ErrorResponse(
                    MessageCodes.ER5000,
                    ex.Message,
                    500));
            }
        }
    }
}
