using CollaboratorsGS.Application.DTOs.Department;

namespace CollaboratorsGS.Application.Interfaces
{
    public interface IDepartmentService
    {
        Task<DepartmentDto?> GetByIdAsync(Guid departmentId);
        Task<IEnumerable<DepartmentDto>> GetAllAsync();
        Task<Guid> CreateDepartmentAsync(CreateDepartmentRequest request);
        Task<DepartmentDto?> GetCreatedDepartmentAsync(Guid departmentId);
        Task<DepartmentDto?> UpdateDepartmentAsync(UpdateDepartmentRequest request);
        Task<bool> DeleteDepartmentAsync(Guid departmentId);
    }
}
