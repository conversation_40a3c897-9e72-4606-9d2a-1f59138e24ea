using CollaboratorsGS.Domain.Repositories;
using CollaboratorsGS.Infrastructure.Data;
using Dapper;
using System.Data;

namespace CollaboratorsGS.Infrastructure.Repositories
{
    public class DeviceTokenRepository : IDeviceTokenRepository
    {
        private readonly IConnectionFactory _connectionFactory;

        public DeviceTokenRepository(IConnectionFactory connectionFactory)
        {
            _connectionFactory = connectionFactory;
        }

        public async Task<bool> SaveTokenAsync(string token, string userId, string deviceType)
        {
            using var connection = _connectionFactory.CreateConnection();

            // Check if token already exists
            var exists = await TokenExistsAsync(token);

            if (exists)
            {
                // Update existing token
                var updateQuery = @"
                    UPDATE device_tokens
                    SET User_Id = @UserId,
                        DeviceType = @DeviceType,
                        UpdatedAt = @UpdatedAt
                    WHERE Token = @Token";

                var updateResult = await connection.ExecuteAsync(updateQuery, new
                {
                    UserId = userId,
                    DeviceType = deviceType,
                    UpdatedAt = DateTime.UtcNow,
                    Token = token
                });

                return updateResult > 0;
            }
            else
            {
                // Insert new token
                var insertQuery = @"
                    INSERT INTO device_tokens (token, user_id, device_type, created_at)
                    VALUES (@Token, @UserId, @DeviceType, @CreatedAt)";

                var insertResult = await connection.ExecuteAsync(insertQuery, new
                {
                    Token = token,
                    UserId = userId,
                    DeviceType = deviceType,
                    CreatedAt = DateTime.UtcNow
                });

                return insertResult > 0;
            }
        }

        public async Task<bool> RemoveTokenAsync(string token)
        {
            using var connection = _connectionFactory.CreateConnection();

            var query = "DELETE FROM device_tokens WHERE Token = @Token";

            var result = await connection.ExecuteAsync(query, new { Token = token });

            return result > 0;
        }

        public async Task<List<string>> GetUserTokensAsync(string userId)
        {
            using var connection = _connectionFactory.CreateConnection();

            var query = "SELECT Token FROM device_tokens WHERE user_id = @UserId";

            var tokens = await connection.QueryAsync<string>(query, new { UserId = userId });

            return tokens.ToList();
        }

        public async Task<bool> TokenExistsAsync(string token)
        {
            using var connection = _connectionFactory.CreateConnection();

            var query = "SELECT COUNT(1) FROM device_tokens WHERE Token = @Token";

            var count = await connection.ExecuteScalarAsync<int>(query, new { Token = token });

            return count > 0;
        }
    }
}
