using CollaboratorsGS.Application.DTOs;

namespace CollaboratorsGS.Application.Interfaces
{
    public interface IPermissionService
    {
        Task<IEnumerable<PermissionDto>> GetAllPermissionsAsync();
        Task<PermissionDto?> GetPermissionByIdAsync(Guid permissionId);
        Task<Guid> CreatePermissionAsync(CreatePermissionRequest request);
        Task<bool> UpdatePermissionAsync(PermissionDto permissionDto);
        Task<bool> DeletePermissionAsync(Guid permissionId);
    }
}
