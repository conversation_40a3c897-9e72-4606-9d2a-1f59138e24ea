using CollaboratorsGS.Application.Constants;
using CollaboratorsGS.Application.DTOs;
using CollaboratorsGS.Application.DTOs.Department;
using CollaboratorsGS.Application.Interfaces;
using CollaboratorsGS.Infrastructure.Utilities;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace CollaboratorsGS.API.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    [Authorize]
    public class DepartmentsController : ControllerBase
    {
        private readonly IDepartmentService _departmentService;
        private readonly ILogger<DepartmentsController> _logger;

        public DepartmentsController(
            IDepartmentService departmentService,
            ILogger<DepartmentsController> logger)
        {
            _departmentService = departmentService;
            _logger = logger;
        }

        // GET: api/Departments
        [HttpGet]
        public async Task<IActionResult> GetAll()
        {
            try
            {
                var departments = await _departmentService.GetAllAsync();
                return Ok(ApiResponse<object>.SuccessResponse(
                    MessageCodes.SC2000,
                    "Get departmanet successfully",
                    departments));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting all departments");
                return StatusCode(500, ApiResponse<object>.ErrorResponse(
                    MessageCodes.ER5000,
                    "Internal server error",
                    500));
            }
        }

        // GET: api/Departments/{id}
        [HttpGet("{id}")]
        public async Task<IActionResult> GetById(Guid id)
        {
            try
            {
                var department = await _departmentService.GetByIdAsync(id);

                if (department == null)
                    return NotFound(ApiResponse<object>.ErrorResponse(
                         MessageCodes.ER4004,
                         "Department not found",
                         404));

                return Ok(ApiResponse<object>.SuccessResponse(
                    MessageCodes.SC2000,
                    "Get department by id successfully",
                    department));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting department with ID {DepartmentId}", id);
                return StatusCode(500, ApiResponse<object>.ErrorResponse(
                    MessageCodes.ER5000,
                    "Internal server error",
                    500));
            }
        }

        // POST: api/Departments
        [HttpPost]
        [Authorize(Roles = RolesUser.AdminManager)]
        public async Task<IActionResult> Create([FromBody] CreateDepartmentRequest request)
        {
            try
            {
                var departmentId = await _departmentService.CreateDepartmentAsync(request);
                var createdDepartment = await _departmentService.GetCreatedDepartmentAsync(departmentId);

                if (createdDepartment == null)
                    return StatusCode(500, ApiResponse<object>.ErrorResponse(
                        MessageCodes.ER5000,
                        "Failed to retrieve created department",
                        500));

                return CreatedAtAction(nameof(GetById), new { id = departmentId }, 
                ApiResponse<object>.SuccessResponse(
                        MessageCodes.SC2001,
                        "Department created successfully",
                        createdDepartment,
                        201));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating department");
                return StatusCode(500, ApiResponse<object>.ErrorResponse(
                    MessageCodes.ER5000,
                    "Internal server error",
                    500));
            }
        }

        // PUT: api/Departments/{id}
        [HttpPut("{id}")]
        [Authorize(Roles = RolesUser.AdminManager)]
        public async Task<IActionResult> Update(Guid id, [FromBody] UpdateDepartmentRequest request)
        {
            try
            {
                if (id != request.DepartmentId)
                    return StatusCode(400, ApiResponse<object>.ErrorResponse(
                    MessageCodes.ER4005,
                    "Internal server error",
                    400));

                var updatedDepartment = await _departmentService.UpdateDepartmentAsync(request);

                if (updatedDepartment == null)
                    return BadRequest(ApiResponse<object>.ErrorResponse(
                       MessageCodes.ER4004,
                       "No department found with the provided ID",
                       400));

                return Ok(ApiResponse<object>.SuccessResponse(
                    MessageCodes.SC2002,
                    "Department updated successfully",
                    updatedDepartment));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating department with ID {DepartmentId}", id);
                return StatusCode(500, ApiResponse<object>.ErrorResponse(
                    MessageCodes.ER5000,
                    "Internal server error",
                    500));
            }
        }

        // DELETE: api/Departments/{id}
        [HttpDelete("{id}")]
        [Authorize(Roles = RolesUser.Admin)]
        public async Task<IActionResult> Delete(Guid id)
        {
            try
            {
                var result = await _departmentService.DeleteDepartmentAsync(id);

                if (!result)
                    return NotFound(ApiResponse<object>.ErrorResponse(
                        MessageCodes.ER4004,
                        "Department not found",
                        404));

                return Ok(ApiResponse<object>.SuccessResponse(
                    MessageCodes.SC2003,
                    "Deleted department successfully",
                    result));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting department with ID {DepartmentId}", id);
                return StatusCode(500, ApiResponse<object>.ErrorResponse(
                    MessageCodes.ER5000,
                    "Internal server error",
                    500));
            }
        }
    }
}
