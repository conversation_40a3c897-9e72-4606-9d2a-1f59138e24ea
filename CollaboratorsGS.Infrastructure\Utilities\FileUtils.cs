using Microsoft.AspNetCore.Http;
using System.IO;
using System.Linq;

namespace CollaboratorsGS.Infrastructure.Utils
{
    public static class FileUtils
    {
        private static readonly string[] AllowedImageTypes =
            { "image/jpeg", "image/jpg", "image/png", "image/gif" };

        private const long MaxImageSizeInBytes = 5 * 1024 * 1024; // 5MB

        public static bool IsImage(IFormFile file)
        {
            if (file == null) return false;
            return AllowedImageTypes.Contains(file.ContentType.ToLower());
        }

        public static bool IsAcceptableSize(IFormFile file, long maxSizeInBytes = MaxImageSizeInBytes)
        {
            return file?.Length <= maxSizeInBytes;
        }

        public static string GetFileExtension(IFormFile file)
        {
            var ext = Path.GetExtension(file?.FileName);
            return string.IsNullOrWhiteSpace(ext) ? "unknown" : ext.TrimStart('.').ToLower();
        }
        public static string GetFileName(string fileName)
        {
             return Path.GetFileNameWithoutExtension(fileName);
        }
    }
}
