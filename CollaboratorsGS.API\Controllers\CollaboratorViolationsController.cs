using CollaboratorsGS.Application.Constants;
using CollaboratorsGS.Application.DTOs;
using CollaboratorsGS.Application.DTOs.CollaboratorViolation;
using CollaboratorsGS.Application.Interfaces;
using CollaboratorsGS.Infrastructure.Utilities;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace CollaboratorsGS.API.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    [Authorize(Roles = RolesUser.AdminManager)]
    public class CollaboratorViolationsController : ControllerBase
    {
        private readonly ICollaboratorViolationService _violationService;
        private readonly ILogger<CollaboratorViolationsController> _logger;

        public CollaboratorViolationsController(
            ICollaboratorViolationService violationService,
            ILogger<CollaboratorViolationsController> logger)
        {
            _violationService = violationService;
            _logger = logger;
        }

        // GET: api/CollaboratorViolations
        [HttpGet]
        public async Task<IActionResult> GetAll()
        {
            try
            {
                var violations = await _violationService.GetAllAsync();
                return Ok(ApiResponse<object>.SuccessResponse(
                    MessageCodes.SC2000,
                    "Get all violations successfully",
                    violations));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting all collaborator violations");
                return StatusCode(500, ApiResponse<object>.ErrorResponse(
                    MessageCodes.ER5000,
                    "Internal server error" + ex.Message,
                    500));
            }
        }

        // GET: api/CollaboratorViolations/{id}
        [HttpGet("{id}")]
        public async Task<IActionResult> GetById(Guid id)
        {
            try
            {
                var violation = await _violationService.GetByIdAsync(id);
                
                if (violation == null)
                    return NotFound(ApiResponse<object>.ErrorResponse(
                        MessageCodes.ER4004,
                        "Violation not found for current user",
                        404));
                
                return Ok(ApiResponse<object>.SuccessResponse(
                    MessageCodes.SC2000,
                    "Get violation successfully",
                    violation));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting collaborator violation with ID {ViolationId}", id);
                return StatusCode(500, ApiResponse<object>.ErrorResponse(
                    MessageCodes.ER5000,
                    "Internal server error " + ex.Message,
                    500));
            }
        }

        // GET: api/CollaboratorViolations/collaborator/{ctvId}
        [HttpGet("collaborator/{collaboratorId}")]
        public async Task<IActionResult> GetByCollaborator(Guid collaboratorId)
        {
            try
            {
                var violations = await _violationService.GetByCollaboratorIdAsync(collaboratorId);
                return Ok(ApiResponse<object>.SuccessResponse(
                    MessageCodes.SC2000,
                    "Get violation by collaborator id successfully",
                    violations));
            }
            catch (InvalidOperationException ex)
            {
                _logger.LogWarning(ex, "Invalid operation: {Message}", ex.Message);
                return BadRequest(ApiResponse<object>.ErrorResponse(
                        MessageCodes.ER4001,
                        "Get violation by collaborator id successfully",
                        400,
                        new List<ErrorDetail>
                        {
                            new ErrorDetail
                            {
                                Field = "collaboratorId",
                                ErrorCode = MessageCodes.ER4001,
                                Message = ex.Message
                            }
                        }));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting violations for collaborator {CtvId}", collaboratorId   );
                return StatusCode(500, ApiResponse<object>.ErrorResponse(
                        MessageCodes.ER5000,
                        "Internal server error",
                        500));
            }
        }

        // GET: api/CollaboratorViolations/type/{violationType}
        [HttpGet("type/{violationType}")]
        public async Task<IActionResult> GetByType(string violationType)
        {
            try
            {
                var violations = await _violationService.GetByTypeAsync(violationType);
                return Ok(ApiResponse<object>.SuccessResponse(
                        MessageCodes.SC2000,
                        "Get by violationType successfully",
                        violations));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting violations with type {ViolationType}", violationType);
                                    return StatusCode(500, ApiResponse<object>.ErrorResponse(
                        MessageCodes.ER5000,
                        "Internal server error",
                        500));
            }
        }

        // POST: api/CollaboratorViolations
        [HttpPost]
        public async Task<IActionResult> Create([FromBody] CreateCollaboratorViolationRequest request)
        {
            try
            {
                var violationId = await _violationService.CreateViolationAsync(request);
                return CreatedAtAction(nameof(GetById), new { id = violationId },
                ApiResponse<object>.SuccessResponse(
                        MessageCodes.SC2001,
                        "Collaborator created successfully",
                        violationId,
                        201));
            }
            catch (InvalidOperationException ex)
            {
                _logger.LogWarning(ex, "Invalid operation: {Message}", ex.Message);
                return BadRequest(ApiResponse<object>.ErrorResponse(
                    MessageCodes.ER4005,
                    ex.Message,
                    400,
                    new List<ErrorDetail>
                    {
                        new ErrorDetail
                        {
                            Field = "request",
                            ErrorCode = MessageCodes.ER4005,
                            Message = ex.Message
                        }
                    }));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating collaborator violation");
                return StatusCode(500, ApiResponse<object>.ErrorResponse(
                    MessageCodes.ER5000,
                    "Internal server error",
                    500));
            }
        }

        // PUT: api/CollaboratorViolations/{id}
        [HttpPut("{id}")]
        public async Task<IActionResult> Update(Guid id, [FromBody] UpdateCollaboratorViolationRequest request)
        {
            try
            {
                if (id != request.ViolationId)
                    return BadRequest(ApiResponse<object>.ErrorResponse(
                       MessageCodes.ER4004,
                       "Id in request body does not match the ID in the URL",
                       400));
                
                var result = await _violationService.UpdateViolationAsync(request);
                
                if (!result)
                   return NotFound(ApiResponse<object>.ErrorResponse(
                        MessageCodes.ER4004,
                        "Collaborator violation not found",
                        404));
                
                return Ok(ApiResponse<object>.SuccessResponse(
                    MessageCodes.SC2002,
                    "Update collaborator violation updated successfully",
                    result));
            }
            catch (InvalidOperationException ex)
            {
                _logger.LogWarning(ex, "Invalid operation: {Message}", ex.Message);
                return BadRequest(ApiResponse<object>.ErrorResponse(
                   MessageCodes.ER4005,
                   ex.Message,
                   400,
                   new List<ErrorDetail>
                   {
                        new ErrorDetail
                        {
                            Field = "request",
                            ErrorCode = MessageCodes.ER4005,
                            Message = ex.Message
                        }
                   }));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating collaborator violation with ID {ViolationId}", id);
                return StatusCode(500, ApiResponse<object>.ErrorResponse(
                    MessageCodes.ER5000,
                    "Internal server error",
                    500));
            }
        }

        // DELETE: api/CollaboratorViolations/{id}
        [HttpDelete("{id}")]
        [Authorize(Roles = RolesUser.Admin)]
        public async Task<IActionResult> Delete(Guid id)
        {
            try
            {
                var result = await _violationService.DeleteViolationAsync(id);
                
                if (!result)
                    return NotFound(ApiResponse<object>.ErrorResponse(
                        MessageCodes.ER4004,
                        "Collaborator violation not found",
                        404));
                
                return Ok(ApiResponse<object>.SuccessResponse(
                    MessageCodes.SC2002,
                    "Collaborator violation deleted successfully",
                    result));
            }
            catch (InvalidOperationException ex)
            {
                _logger.LogWarning(ex, "Invalid operation: {Message}", ex.Message);
                return BadRequest(ApiResponse<object>.ErrorResponse(
                   MessageCodes.ER4005,
                   ex.Message,
                   400,
                   new List<ErrorDetail>
                   {
                        new ErrorDetail
                        {
                            Field = "Id",
                            ErrorCode = MessageCodes.ER4005,
                            Message = ex.Message
                        }
                   }));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting collaborator violation with ID {ViolationId}", id);
                return StatusCode(500, ApiResponse<object>.ErrorResponse(
                    MessageCodes.ER5000,
                    "Internal server error",
                    500));
            }
        }
    }
}
