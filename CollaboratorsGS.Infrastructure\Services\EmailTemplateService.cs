using CollaboratorsGS.Application.Interfaces;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;

namespace CollaboratorsGS.Infrastructure.Services
{
    public class EmailTemplateService : IEmailTemplateService
    {
        private readonly IEmailService _emailService;
        private readonly IConfiguration _configuration;
        private readonly IHostEnvironment _hostEnvironment;
        private readonly ILogger<EmailTemplateService> _logger;

        public EmailTemplateService(
            IEmailService emailService,
            IConfiguration configuration,
            IHostEnvironment hostEnvironment,
            ILogger<EmailTemplateService> logger)
        {
            _emailService = emailService;
            _configuration = configuration;
            _hostEnvironment = hostEnvironment;
            _logger = logger;
        }

        public async Task<bool> SendPasswordResetEmailAsync(string toEmail, string userName, string resetToken, string resetCode)
        {
            try
            {
                var resetUrl = $"{_configuration["AppSettings:FrontendUrl"]}/reset-password?token={resetToken}";
                var expirationMinutes = _configuration["Jwt:ExpiryInMinutes"] ?? "60";

                var placeholders = new Dictionary<string, string>
                {
                    { "{{UserName}}", userName },
                    { "{{ResetPasswordUrl}}", resetUrl },
                    { "{{ExpirationMinutes}}", expirationMinutes }
                };

                var emailBody = await LoadTemplateAsync("password-reset.html", placeholders);
                var subject = "🔐 Đặt lại mật khẩu - Collaborator System";

                return await _emailService.SendEmailAsync(toEmail, subject, emailBody);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error sending password reset email to {Email}", toEmail);
                return false;
            }
        }

        public async Task<bool> SendCollaboratorApprovalEmailAsync(string toEmail, string collaboratorName, string email, string phoneNumber, string approvedBy)
        {
            try
            {
                var loginUrl = $"{_configuration["AppSettings:FrontendUrl"]}/login";
                var approvalDate = DateTime.Now.ToString("dd/MM/yyyy HH:mm");

                var placeholders = new Dictionary<string, string>
                {
                    { "{{CollaboratorName}}", collaboratorName },
                    { "{{Email}}", email },
                    { "{{PhoneNumber}}", phoneNumber },
                    { "{{ApprovalDate}}", approvalDate },
                    { "{{ApprovedBy}}", approvedBy },
                    { "{{LoginUrl}}", loginUrl }
                };

                var emailBody = await LoadTemplateAsync("collaborator-approval.html", placeholders);
                var subject = "🎉 Chúc mừng! Tài khoản của bạn đã được phê duyệt";

                return await _emailService.SendEmailAsync(toEmail, subject, emailBody);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error sending collaborator approval email to {Email}", toEmail);
                return false;
            }
        }

        // public async Task<bool> SendRecruitmentNotificationEmailAsync(string toEmail, string collaboratorName, object jobDetails)
        // {
        //     try
        //     {
        //         // Implement recruitment notification logic here
        //         // This is a placeholder for future implementation
        //         _logger.LogInformation("Recruitment notification email feature not yet implemented");
        //         return true;
        //     }
        //     catch (Exception ex)
        //     {
        //         _logger.LogError(ex, "Error sending recruitment notification email to {Email}", toEmail);
        //         return false;
        //     }
        // }

        public async Task<string> LoadTemplateAsync(string templateName, Dictionary<string, string> placeholders)
        {
            try
            {
                var templatePath = Path.Combine(_hostEnvironment.ContentRootPath, "wwwroot", "email-templates", templateName);
                
                if (!File.Exists(templatePath))
                {
                    _logger.LogError("Email template not found: {TemplatePath}", templatePath);
                    throw new FileNotFoundException($"Email template not found: {templateName}");
                }

                var templateContent = await File.ReadAllTextAsync(templatePath);

                // Replace all placeholders
                foreach (var placeholder in placeholders)
                {
                    templateContent = templateContent.Replace(placeholder.Key, placeholder.Value);
                }

                return templateContent;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading email template: {TemplateName}", templateName);
                throw;
            }
        }
    }
}
