-- Stored Procedures for CollaboratorReward operations

-- Get CollaboratorReward by ID (with joined data)
IF EXISTS (SELECT * FROM sys.objects WHERE type = 'P' AND name = 'sp_GetCollaboratorRewardById')
    DROP PROCEDURE sp_GetCollaboratorRewardById
GO

CREATE PROCEDURE sp_GetCollaboratorRewardById
    @RewardId UNIQUEIDENTIFIER
AS
BEGIN
    SELECT 
        cr.reward_id,
        cr.collaborator_id,
        c.full_name as collaborator_name,
        cr.application_id,
        cr.reward_type,
        cr.amount,
        cr.level_id,
        cl.level_name,
        cr.reward_date,
        cr.scheduled_payment_date,
        cr.status
    FROM collaborator_rewards cr
    INNER JOIN collaborators c ON cr.collaborator_id = c.collaborator_id
    INNER JOIN collaborator_levels cl ON cr.level_id = cl.level_id
    WHERE cr.reward_id = @RewardId
END
GO

-- Get All collaborator_rewards (with joined data)
IF EXISTS (SELECT * FROM sys.objects WHERE type = 'P' AND name = 'sp_GetAllCollaboratorRewards')
    DROP PROCEDURE sp_GetAllCollaboratorRewards
GO

CREATE PROCEDURE sp_GetAllCollaboratorRewards
AS
BEGIN
    SELECT 
        cr.reward_id,
        cr.collaborator_id,
        c.full_name as collaborator_name,
        cr.application_id,
        cr.reward_type,
        cr.amount,
        cr.level_id,
        cl.level_name,
        cr.reward_date,
        cr.scheduled_payment_date,
        cr.status
    FROM collaborator_rewards cr
    INNER JOIN collaborators c ON cr.collaborator_id = c.collaborator_id
    INNER JOIN collaborator_levels cl ON cr.level_id = cl.level_id
    ORDER BY cr.reward_date DESC
END
GO

-- Get collaborator_rewards by CollaboratorId
IF EXISTS (SELECT * FROM sys.objects WHERE type = 'P' AND name = 'sp_GetCollaboratorRewardsByCollaboratorId')
    DROP PROCEDURE sp_GetCollaboratorRewardsByCollaboratorId
GO

CREATE PROCEDURE sp_GetCollaboratorRewardsByCollaboratorId
    @CollaboratorId UNIQUEIDENTIFIER
AS
BEGIN
    SELECT 
        cr.reward_id,
        cr.collaborator_id,
        c.full_name as collaborator_name,
        cr.application_id,
        cr.reward_type,
        cr.amount,
        cr.level_id,
        cl.level_name,
        cr.reward_date,
        cr.scheduled_payment_date,
        cr.status
    FROM collaborator_rewards cr
    INNER JOIN collaborators c ON cr.collaborator_id = c.collaborator_id
    INNER JOIN collaborator_levels cl ON cr.level_id = cl.level_id
    WHERE cr.collaborator_id = @CollaboratorId
    ORDER BY cr.reward_date DESC
END
GO

-- Get collaborator_rewards by Status
IF EXISTS (SELECT * FROM sys.objects WHERE type = 'P' AND name = 'sp_GetCollaboratorRewardsByStatus')
    DROP PROCEDURE sp_GetCollaboratorRewardsByStatus
GO

CREATE PROCEDURE sp_GetCollaboratorRewardsByStatus
    @Status NVARCHAR(50)
AS
BEGIN
    SELECT 
        cr.reward_id,
        cr.collaborator_id,
        c.full_name as collaborator_name,
        cr.application_id,
        cr.reward_type,
        cr.amount,
        cr.level_id,
        cl.level_name,
        cr.reward_date,
        cr.scheduled_payment_date,
        cr.status
    FROM collaborator_rewards cr
    INNER JOIN collaborators c ON cr.collaborator_id = c.collaborator_id
    INNER JOIN collaborator_levels cl ON cr.level_id = cl.level_id
    WHERE cr.status = @Status
    ORDER BY cr.reward_date DESC
END
GO

-- Create CollaboratorReward
IF EXISTS (SELECT * FROM sys.objects WHERE type = 'P' AND name = 'sp_CreateCollaboratorReward')
    DROP PROCEDURE sp_CreateCollaboratorReward
GO

CREATE PROCEDURE sp_CreateCollaboratorReward
    @RewardId UNIQUEIDENTIFIER,
    @CollaboratorId UNIQUEIDENTIFIER,
    @ApplicationId UNIQUEIDENTIFIER,
    @RewardType NVARCHAR(50),
    @Amount DECIMAL(18, 2),
    @LevelId UNIQUEIDENTIFIER,
    @RewardDate DATETIME,
    @ScheduledPaymentDate DATETIME,
    @Status NVARCHAR(50)
AS
BEGIN
    INSERT INTO collaborator_rewards (
        reward_id,
        collaborator_id,
        application_id,
        reward_type,
        amount,
        level_id,
        reward_date,
        scheduled_payment_date,
        status
    )
    VALUES (
        @RewardId,
        @CollaboratorId,
        @ApplicationId,
        @RewardType,
        @Amount,
        @LevelId,
        @RewardDate,
        @ScheduledPaymentDate,
        @Status
    )
END
GO

-- Update CollaboratorReward
IF EXISTS (SELECT * FROM sys.objects WHERE type = 'P' AND name = 'sp_UpdateCollaboratorReward')
    DROP PROCEDURE sp_UpdateCollaboratorReward
GO

CREATE PROCEDURE sp_UpdateCollaboratorReward
    @RewardId UNIQUEIDENTIFIER,
    @Amount DECIMAL(18, 2),
    @ScheduledPaymentDate DATETIME,
    @Status NVARCHAR(50)
AS
BEGIN
    UPDATE collaborator_rewards
    SET
        amount = @Amount,
        scheduled_payment_date = @ScheduledPaymentDate,
        status = @Status
    WHERE reward_id = @RewardId
END
GO

-- Delete CollaboratorReward
IF EXISTS (SELECT * FROM sys.objects WHERE type = 'P' AND name = 'sp_DeleteCollaboratorReward')
    DROP PROCEDURE sp_DeleteCollaboratorReward
GO

CREATE PROCEDURE sp_DeleteCollaboratorReward
    @RewardId UNIQUEIDENTIFIER
AS
BEGIN
    DELETE FROM collaborator_rewards
    WHERE reward_id = @RewardId
END
GO

-- Get collaborator_reward_history by RewardId
IF EXISTS (SELECT * FROM sys.objects WHERE type = 'P' AND name = 'sp_GetCollaboratorRewardHistoryByRewardId')
    DROP PROCEDURE sp_GetCollaboratorRewardHistoryByRewardId
GO

CREATE PROCEDURE sp_GetCollaboratorRewardHistoryByRewardId
    @RewardId UNIQUEIDENTIFIER
AS
BEGIN
    SELECT
        crh.history_id,
        crh.collaborator_id,
        c.full_name as collaborator_name,
        crh.reward_id,
        crh.amount,
        crh.payment_date,
        crh.payment_method,
        crh.status
    FROM collaborator_reward_history crh
    INNER JOIN collaborators c ON crh.collaborator_id = c.collaborator_id
    WHERE crh.reward_id = @RewardId
    ORDER BY crh.payment_date DESC
END
GO

-- Create collaborator_reward_history
IF EXISTS (SELECT * FROM sys.objects WHERE type = 'P' AND name = 'sp_CreateCollaboratorRewardHistory')
    DROP PROCEDURE sp_CreateCollaboratorRewardHistory
GO

CREATE PROCEDURE sp_CreateCollaboratorRewardHistory
    @HistoryId UNIQUEIDENTIFIER,
    @CollaboratorId UNIQUEIDENTIFIER,
    @RewardId UNIQUEIDENTIFIER,
    @Amount DECIMAL(18, 2),
    @PaymentDate DATETIME,
    @PaymentMethod NVARCHAR(50),
    @Status NVARCHAR(50)
AS
BEGIN
    INSERT INTO collaborator_reward_history (
        history_id,
        collaborator_id,
        reward_id,
        amount,
        payment_date,
        payment_method,
        status
    )
    VALUES (
        @HistoryId,
        @CollaboratorId,
        @RewardId,
        @Amount,
        @PaymentDate,
        @PaymentMethod,
        @Status
    )
END
GO

-- Get collaborator_rewards by ApplicationId
IF EXISTS (SELECT * FROM sys.objects WHERE type = 'P' AND name = 'sp_GetCollaboratorRewardsByApplicationId')
    DROP PROCEDURE sp_GetCollaboratorRewardsByApplicationId
GO

CREATE PROCEDURE sp_GetCollaboratorRewardsByApplicationId
    @ApplicationId UNIQUEIDENTIFIER
AS
BEGIN
    SELECT
        cr.reward_id,
        cr.collaborator_id,
        c.full_name as collaborator_name,
        cr.application_id,
        cr.reward_type,
        cr.amount,
        cr.level_id,
        cl.level_name,
        cr.reward_date,
        cr.scheduled_payment_date,
        cr.status
    FROM collaborator_rewards cr
    INNER JOIN collaborators c ON cr.collaborator_id = c.collaborator_id
    INNER JOIN collaborator_levels cl ON cr.level_id = cl.level_id
    WHERE cr.application_id = @ApplicationId
    ORDER BY cr.reward_date DESC
END
GO

-- Get collaborator_rewards by LevelId
IF EXISTS (SELECT * FROM sys.objects WHERE type = 'P' AND name = 'sp_GetCollaboratorRewardsByLevelId')
    DROP PROCEDURE sp_GetCollaboratorRewardsByLevelId
GO

CREATE PROCEDURE sp_GetCollaboratorRewardsByLevelId
    @LevelId UNIQUEIDENTIFIER
AS
BEGIN
    SELECT
        cr.reward_id,
        cr.collaborator_id,
        c.full_name as collaborator_name,
        cr.application_id,
        cr.reward_type,
        cr.amount,
        cr.level_id,
        cl.level_name,
        cr.reward_date,
        cr.scheduled_payment_date,
        cr.status
    FROM collaborator_rewards cr
    INNER JOIN collaborators c ON cr.collaborator_id = c.collaborator_id
    INNER JOIN collaborator_levels cl ON cr.level_id = cl.level_id
    WHERE cr.level_id = @LevelId
    ORDER BY cr.reward_date DESC
END
GO

-- Get collaborator_rewards by RewardType
IF EXISTS (SELECT * FROM sys.objects WHERE type = 'P' AND name = 'sp_GetCollaboratorRewardsByRewardType')
    DROP PROCEDURE sp_GetCollaboratorRewardsByRewardType
GO

CREATE PROCEDURE sp_GetCollaboratorRewardsByRewardType
    @RewardType NVARCHAR(50)
AS
BEGIN
    SELECT
        cr.reward_id,
        cr.collaborator_id,
        c.full_name as collaborator_name,
        cr.application_id,
        cr.reward_type,
        cr.amount,
        cr.level_id,
        cl.level_name,
        cr.reward_date,
        cr.scheduled_payment_date,
        cr.status
    FROM collaborator_rewards cr
    INNER JOIN collaborators c ON cr.collaborator_id = c.collaborator_id
    INNER JOIN collaborator_levels cl ON cr.level_id = cl.level_id
    WHERE cr.reward_type = @RewardType
    ORDER BY cr.reward_date DESC
END
GO

-- Get collaborator_rewards by Date Range
IF EXISTS (SELECT * FROM sys.objects WHERE type = 'P' AND name = 'sp_GetCollaboratorRewardsByDateRange')
    DROP PROCEDURE sp_GetCollaboratorRewardsByDateRange
GO

CREATE PROCEDURE sp_GetCollaboratorRewardsByDateRange
    @StartDate DATETIME,
    @EndDate DATETIME
AS
BEGIN
    SELECT
        cr.reward_id,
        cr.collaborator_id,
        c.full_name as collaborator_name,
        cr.application_id,
        cr.reward_type,
        cr.amount,
        cr.level_id,
        cl.level_name,
        cr.reward_date,
        cr.scheduled_payment_date,
        cr.status
    FROM collaborator_rewards cr
    INNER JOIN collaborators c ON cr.collaborator_id = c.collaborator_id
    INNER JOIN collaborator_levels cl ON cr.level_id = cl.level_id
    WHERE cr.reward_date >= @StartDate AND cr.reward_date <= @EndDate
    ORDER BY cr.reward_date DESC
END
GO

-- Get CollaboratorReward Statistics by CollaboratorId
IF EXISTS (SELECT * FROM sys.objects WHERE type = 'P' AND name = 'sp_GetCollaboratorRewardStatsByCollaboratorId')
    DROP PROCEDURE sp_GetCollaboratorRewardStatsByCollaboratorId
GO

CREATE PROCEDURE sp_GetCollaboratorRewardStatsByCollaboratorId
    @CollaboratorId UNIQUEIDENTIFIER
AS
BEGIN
    SELECT
        COUNT(*) as total_rewards,
        SUM(amount) as total_amount,
        AVG(amount) as average_amount,
        MIN(amount) as min_amount,
        MAX(amount) as max_amount,
        COUNT(CASE WHEN status = 'Pending' THEN 1 END) as pending_count,
        COUNT(CASE WHEN status = 'Paid' THEN 1 END) as paid_count,
        COUNT(CASE WHEN status = 'Cancelled' THEN 1 END) as cancelled_count,
        SUM(CASE WHEN status = 'Paid' THEN amount ELSE 0 END) as total_paid_amount,
        SUM(CASE WHEN status = 'Pending' THEN amount ELSE 0 END) as total_pending_amount
    FROM collaborator_rewards
    WHERE collaborator_id = @CollaboratorId
END
GO

-- Get Overall CollaboratorReward Statistics
IF EXISTS (SELECT * FROM sys.objects WHERE type = 'P' AND name = 'sp_GetOverallCollaboratorRewardStats')
    DROP PROCEDURE sp_GetOverallCollaboratorRewardStats
GO

CREATE PROCEDURE sp_GetOverallCollaboratorRewardStats
AS
BEGIN
    SELECT
        COUNT(*) as total_rewards,
        SUM(amount) as total_amount,
        AVG(amount) as average_amount,
        COUNT(DISTINCT collaborator_id) as unique_collaborators,
        COUNT(CASE WHEN status = 'Pending' THEN 1 END) as pending_count,
        COUNT(CASE WHEN status = 'Paid' THEN 1 END) as paid_count,
        COUNT(CASE WHEN status = 'Cancelled' THEN 1 END) as cancelled_count,
        SUM(CASE WHEN status = 'Paid' THEN amount ELSE 0 END) as total_paid_amount,
        SUM(CASE WHEN status = 'Pending' THEN amount ELSE 0 END) as total_pending_amount
    FROM collaborator_rewards
END
GO
