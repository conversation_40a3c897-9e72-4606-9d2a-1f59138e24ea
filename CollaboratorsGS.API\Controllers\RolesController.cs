using CollaboratorsGS.Application.Constants;
using CollaboratorsGS.Application.DTOs;
using CollaboratorsGS.Application.Interfaces;
using CollaboratorsGS.Infrastructure.Utilities;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace CollaboratorsGS.API.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    [Authorize(Roles = RolesUser.Admin)]
    public class RolesController : ControllerBase
    {
        private readonly IRoleService _roleService;

        public RolesController(IRoleService roleService)
        {
            _roleService = roleService;
        }

        [HttpGet]
        public async Task<IActionResult> GetAll()
        {
            var roles = await _roleService.GetAllRolesAsync();
            return Ok(ApiResponse<object>.SuccessResponse(
                MessageCodes.SC2000,
                "Get all roles successfully",
                roles));
        }

        [HttpGet("{id}")]
        public async Task<IActionResult> GetById(Guid id)
        {
            var role = await _roleService.GetRoleByIdAsync(id);
            
            if (role == null)
                return NotFound(ApiResponse<object>.ErrorResponse(
                    MessageCodes.ER4004,
                    "Role not found",
                    404));
            
            return Ok(ApiResponse<object>.SuccessResponse(
                MessageCodes.SC2000,
                "Get role by id successfully",
                role));
        }

        [HttpGet("{id}/permissions")]
        public async Task<IActionResult> GetRoleWithPermissions(Guid id)
        {
            var roleWithPermissions = await _roleService.GetRoleWithPermissionsAsync(id);
            
            if (roleWithPermissions == null)
                return NotFound(ApiResponse<object>.ErrorResponse(
                    MessageCodes.ER4004,
                    "Role not found",
                    404));
            
            return Ok(ApiResponse<object>.SuccessResponse(
                MessageCodes.SC2000,
                "Get role with permissions successfully",
                roleWithPermissions));
        }

        [HttpPost]
        public async Task<IActionResult> Create([FromBody] CreateRoleRequest request)
        {
            var roleId = await _roleService.CreateRoleAsync(request);
            return CreatedAtAction(nameof(GetById), new { id = roleId }, new { RoleId = roleId });
        }

        [HttpPut("{id}")]
        public async Task<IActionResult> Update(Guid id, [FromBody] RoleDto roleDto)
        {
            if (id != roleDto.RoleId)
                return BadRequest(ApiResponse<object>.ErrorResponse(
                    MessageCodes.ER4001,
                    "ID mismatch",
                    400,
                    new List<ErrorDetail>
                    {
                        new ErrorDetail
                        {
                            Field = "id",
                            ErrorCode = MessageCodes.ER4001,
                            Message = "Role ID in URL does not match request body"
                        }
                    }));
            
            var result = await _roleService.UpdateRoleAsync(roleDto);
            
            if (!result)
                return NotFound(ApiResponse<object>.ErrorResponse(
                    MessageCodes.ER4004,
                    "Role not found",
                    404));
            
            return Ok(ApiResponse<object>.SuccessResponse(
                MessageCodes.SC2002,
                "Role updated successfully"));
        }

        [HttpDelete("{id}")]
        public async Task<IActionResult> Delete(Guid id)
        {
            var result = await _roleService.DeleteRoleAsync(id);
            
            if (!result)
                return NotFound(ApiResponse<object>.ErrorResponse(
                    MessageCodes.ER4004,
                    "Role not found",
                    404));
            
            return Ok(ApiResponse<object>.SuccessResponse(
                MessageCodes.SC2003,
                "Role deleted successfully"));
        }

        [HttpPost("{id}/permissions")]
        public async Task<IActionResult> AssignPermissions(Guid id, [FromBody] List<Guid> permissionIds)
        {
            var request = new AssignPermissionsRequest
            {
                RoleId = id,
                PermissionIds = permissionIds
            };
            
            var result = await _roleService.AssignPermissionsToRoleAsync(request);
            
            if (!result)
                return BadRequest(ApiResponse<object>.ErrorResponse(
                    MessageCodes.ER4005,
                    "Role not found",
                    400));
            
            return Ok(ApiResponse<object>.SuccessResponse(
                MessageCodes.SC2002,
                "Permissions assigned successfully"));
        }
    }
}
