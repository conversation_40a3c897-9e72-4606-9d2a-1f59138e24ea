using CollaboratorsGS.Application.DTOs.Common;

namespace CollaboratorsGS.Application.DTOs.Candidate
{
    public class CandidateDto
    {
        public Guid CandidateId { get; set; }
        public string FullName { get; set; } = string.Empty;
        public string PhoneNumber { get; set; } = string.Empty;
        public string? Email { get; set; }
        public string? EducationLevel { get; set; }
        public string? WorkExperience { get; set; }
        public string? Skills { get; set; }

        // Return date as string in dd/MM/yyyy format
        public string? DateOfBirth { get; set; }

        public string? Gender { get; set; }
        public string? Address { get; set; }
        public string? Ward { get; set; }
        public string? District { get; set; }
        public string? Province { get; set; }
        public string? FullAddress { get; set; }
        public string? ProfilePicture { get; set; }
        public string? FullBodyPicture { get; set; }
        public int? HeightCm { get; set; }
        public int? WeightKg { get; set; }
        public string? Level { get; set; }
        public string? Source { get; set; }
        public Guid? CollaboratorId { get; set; }
        public string? CollaboratorName { get; set; }
        public string? CitizenId { get; set; }
        public string? CitizenIdAddress { get; set; }

        // Return date as string in dd/MM/yyyy format
        public string? CitizenIdIssueDate { get; set; }

        public string? CitizenIdIssuePlace { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime? UpdatedAt { get; set; }
    }
}
