-- Stored Procedures for CollaboratorProfile operations

-- Get CollaboratorProfile by ID
IF EXISTS (SELECT * FROM sys.objects WHERE type = 'P' AND name = 'sp_GetCollaboratorProfileById')
    DROP PROCEDURE sp_GetCollaboratorProfileById
GO

CREATE PROCEDURE sp_GetCollaboratorProfileById
    @ProfileId UNIQUEIDENTIFIER
AS
BEGIN
    SELECT *
    FROM collaborator_profiles
    WHERE profile_id = @ProfileId
END
GO

-- Get CollaboratorProfile by CollaboratorId
IF EXISTS (SELECT * FROM sys.objects WHERE type = 'P' AND name = 'sp_GetCollaboratorProfileByCollaboratorId')
    DROP PROCEDURE sp_GetCollaboratorProfileByCollaboratorId
GO

CREATE PROCEDURE sp_GetCollaboratorProfileByCollaboratorId
    @CollaboratorId UNIQUEIDENTIFIER
AS
BEGIN
    SELECT *
    FROM collaborator_profiles
    WHERE collaborator_id = @CollaboratorId
END
GO

-- Create CollaboratorProfile
IF EXISTS (SELECT * FROM sys.objects WHERE type = 'P' AND name = 'sp_CreateCollaboratorProfile')
    DROP PROCEDURE sp_CreateCollaboratorProfile
GO

CREATE PROCEDURE sp_CreateCollaboratorProfile
    @ProfileId UNIQUEIDENTIFIER,
    @CollaboratorId UNIQUEIDENTIFIER,
    @CitizenId VARCHAR(50) = NULL,
    @CitizenIdFront VARCHAR(255) = NULL,
    @CitizenIdBack VARCHAR(255) = NULL,
    @PermanentAddress NVARCHAR(MAX) = NULL,
    @CurrentAddress NVARCHAR(MAX) = NULL,
    @BankName NVARCHAR(255) = NULL,
    @BankAccountNumber VARCHAR(50) = NULL,
    @CreatedAt DATETIME,
    @UpdatedAt DATETIME = NULL
AS
BEGIN
    INSERT INTO collaborator_profiles (
        profile_id, collaborator_id, citizen_id, citizen_id_front, citizen_id_back,
        permanent_address, current_address, bank_name, bank_account_number,
        created_at, updated_at
    )
    VALUES (
        @ProfileId, @CollaboratorId, @CitizenId, @CitizenIdFront, @CitizenIdBack,
        @PermanentAddress, @CurrentAddress, @BankName, @BankAccountNumber,
        @CreatedAt, @UpdatedAt
    )
END
GO

-- Update CollaboratorProfile
IF EXISTS (SELECT * FROM sys.objects WHERE type = 'P' AND name = 'sp_UpdateCollaboratorProfile')
    DROP PROCEDURE sp_UpdateCollaboratorProfile
GO

CREATE PROCEDURE sp_UpdateCollaboratorProfile
    @ProfileId UNIQUEIDENTIFIER = NULL,
    @CollaboratorId UNIQUEIDENTIFIER,
    @CitizenId VARCHAR(50) = NULL,
    @CitizenIdFront VARCHAR(255) = NULL,
    @CitizenIdBack VARCHAR(255) = NULL,
    @PermanentAddress NVARCHAR(MAX) = NULL,
    @CurrentAddress NVARCHAR(MAX) = NULL,
    @BankName NVARCHAR(255) = NULL,
    @BankAccountNumber VARCHAR(50) = NULL,
    @UpdatedAt DATETIME
AS
BEGIN
    -- Update by CollaboratorId (primary use case)
    IF @ProfileId IS NULL
    BEGIN
        UPDATE collaborator_profiles
        SET citizen_id = @CitizenId,
            citizen_id_front = @CitizenIdFront,
            citizen_id_back = @CitizenIdBack,
            permanent_address = @PermanentAddress,
            current_address = @CurrentAddress,
            bank_name = @BankName,
            bank_account_number = @BankAccountNumber,
            updated_at = @UpdatedAt
        WHERE collaborator_id = @CollaboratorId
    END
    ELSE
    BEGIN
        -- Update by ProfileId (fallback)
        UPDATE collaborator_profiles
        SET citizen_id = @CitizenId,
            citizen_id_front = @CitizenIdFront,
            citizen_id_back = @CitizenIdBack,
            permanent_address = @PermanentAddress,
            current_address = @CurrentAddress,
            bank_name = @BankName,
            bank_account_number = @BankAccountNumber,
            updated_at = @UpdatedAt
        WHERE profile_id = @ProfileId
    END
END
GO
