using CollaboratorsGS.Domain.Entities;
using CollaboratorsGS.Domain.Repositories;
using CollaboratorsGS.Infrastructure.Data;
using Dapper;

namespace CollaboratorsGS.Infrastructure.Repositories
{
    public class AddressRepository : IAddressRepository
    {
        private readonly IConnectionFactory _connectionFactory;

        public AddressRepository(IConnectionFactory connectionFactory)
        {
            _connectionFactory = connectionFactory;
        }

        public async Task<IEnumerable<Province>> GetAllProvincesAsync(bool isRestructure)
        {
            using var connection = _connectionFactory.CreateConnection();
            var parameters = new DynamicParameters();
            parameters.Add("@is_restructure", isRestructure ? 1 : 0);

            return await connection.QueryAsync<Province>("sp_GetAllProvinces",
                parameters,
                commandType: System.Data.CommandType.StoredProcedure);
        }

        public async Task<IEnumerable<District>> GetDistrictsByProvinceCodeAsync(string queryCode)
        {
            using var connection = _connectionFactory.CreateConnection();
            var parameters = new DynamicParameters();
            parameters.Add("@query_code", queryCode);

            return await connection.QueryAsync<District>("sp_GetDistrictsByQueryCode",
                parameters,
                commandType: System.Data.CommandType.StoredProcedure);
        }

        public async Task<IEnumerable<Ward>> GetWardsByDistrictCodeAsync(string queryCode, bool isRestructure)
        {
            using var connection = _connectionFactory.CreateConnection();
            {
                var parameters = new DynamicParameters();
                parameters.Add("@query_code", queryCode);
                parameters.Add("@is_restructure", isRestructure ? 1 : 0);

                return await connection.QueryAsync<Ward>("sp_GetWardByQueryCode",
                    parameters,
                    commandType: System.Data.CommandType.StoredProcedure);
            }
        }
    }
}
