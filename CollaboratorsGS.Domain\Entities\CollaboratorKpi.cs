
namespace CollaboratorsGS.Domain.Entities
{
    public class CollaboratorKpi
    {
        public Guid KpiId { get; set; }
        public Guid CollaboratorId { get; set; }
        public string Period { get; set; } = string.Empty;
        public int TotalCandidatesImported { get; set; } = 0;
        public int TotalCandidatesPassedRound1 { get; set; } = 0;
        public int TotalCandidatesPassedRound2 { get; set; } = 0;
        public int TotalCandidatesOnboarded { get; set; } = 0;
        public int TotalCandidatesFailed { get; set; } = 0;
        public int TotalCandidatesOnboardedWarranty { get; set; } = 0;
        public float? SuccessRate { get; set; }
        public DateTime CalculatedAt { get; set; }

        // Navigation properties
        public Collaborator? Collaborator { get; set; }
    }
}
