using CollaboratorsGS.Domain.Entities;
using CollaboratorsGS.Domain.Repositories;
using CollaboratorsGS.Infrastructure.Data;
using Dapper;
using System;
using System.Collections.Generic;
using System.Data;
using System.Threading.Tasks;

namespace CollaboratorsGS.Infrastructure.Repositories
{
    public class CandidateDocumentRepository : ICandidateDocumentRepository
    {
        private readonly IConnectionFactory _connectionFactory;

        public CandidateDocumentRepository(IConnectionFactory connectionFactory)
        {
            _connectionFactory = connectionFactory;
        }

        public async Task<Guid> CreateAsync(CandidateDocument document)
        {
            // Generate a new UUID if not provided
            if (document.DocumentId == Guid.Empty)
            {
                document.DocumentId = Guid.NewGuid();
            }

            using var connection = _connectionFactory.CreateConnection();

            var parameters = new
            {
                document.DocumentId,
                document.CandidateId,
                document.DocumentType,
                document.FilePath,
                document.FileType,
                document.UploadedAt
            };

            await connection.ExecuteAsync(
                "sp_CreateCandidateDocument",
                parameters,
                commandType: CommandType.StoredProcedure
            );

            return document.DocumentId;
        }

        public async Task<CandidateDocument?> GetByIdAsync(Guid documentId)
        {
            using var connection = _connectionFactory.CreateConnection();

            return await connection.QueryFirstOrDefaultAsync<CandidateDocument>(
                "sp_GetCandidateDocumentById",
                new { DocumentId = documentId },
                commandType: CommandType.StoredProcedure
            );
        }

        public async Task<IEnumerable<CandidateDocument>> GetByCandidateIdAsync(Guid candidateId)
        {
            using var connection = _connectionFactory.CreateConnection();

            return await connection.QueryAsync<CandidateDocument>(
                "sp_GetCandidateDocumentsByCandidateId",
                new { CandidateId = candidateId },
                commandType: CommandType.StoredProcedure
            );
        }

        public async Task<bool> DeleteAsync(Guid documentId)
        {
            using var connection = _connectionFactory.CreateConnection();

            var result = await connection.QuerySingleAsync<int>(
                "sp_DeleteCandidateDocument",
                new { DocumentId = documentId },
                commandType: CommandType.StoredProcedure
            );

            return result > 0;
        }
    }
}