stages:
  - build
  - docker-build
  - deploy

variables:
  DOCKER_REGISTRY: "registry.gitlab.com"
  DOCKER_IMAGE: "green-speed/service/main-ctv"
  DOCKER_TAG: "latest"

workflow:
  rules:
    - if: $CI_COMMIT_BRANCH == "dev"

build:
  stage: build
  image: mcr.microsoft.com/dotnet/sdk:8.0
  script:
    - dotnet workload install aspire
    - dotnet restore
    - dotnet build --no-restore
    - dotnet publish -c Release -o publish
  artifacts:
    paths:
      - publish/
    expire_in: 1 week
  rules:
    - if: $CI_COMMIT_BRANCH == "dev"

docker-build:
  stage: docker-build
  image: docker:latest
  services:
    - docker:dind
  before_script:
    - docker login -u $CI_REGISTRY_USER -p $CI_REGISTRY_PASSWORD $CI_REGISTRY
  script:
    - docker build -t $DOCKER_REGISTRY/$DOCKER_IMAGE:$DOCKER_TAG -f deployments/Dockerfile .
    - docker push $DOCKER_REGISTRY/$DOCKER_IMAGE:$DOCKER_TAG
  rules:
    - if: $CI_COMMIT_BRANCH == "dev"
  dependencies:
    - build
  needs:
    - build

deploy:
  stage: deploy
  image: alpine:latest
  before_script:
    - apk add --no-cache openssh-client sshpass rsync
  script:
    - echo "Testing SSH connection..."
    - sshpass -p "$VPS_PASSWORD" ssh -o StrictHostKeyChecking=no $VPS_USER@$VPS_HOST "cd /root/ctv/api && chmod +x deploy.sh && ./deploy.sh $DOCKER_TAG" || (echo "SSH connection failed"; exit 1)
  dependencies:
    - docker-build
  needs:
    - docker-build
  rules:
    - if: $CI_COMMIT_BRANCH == "dev"
