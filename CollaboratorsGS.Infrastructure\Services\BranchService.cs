using AutoMapper;
using CollaboratorsGS.Application.DTOs.Branch;
using CollaboratorsGS.Application.Interfaces;
using CollaboratorsGS.Domain.Entities;
using CollaboratorsGS.Domain.Repositories;
using Microsoft.Extensions.Logging;

namespace CollaboratorsGS.Infrastructure.Services
{
    public class BranchService : IBranchService
    {
        private readonly IBranchRepository _branchRepository;
        private readonly ICompanyRepository _companyRepository;
        private readonly IMapper _mapper;
        private readonly ILogger<BranchService> _logger;

        public BranchService(
            IBranchRepository branchRepository,
            ICompanyRepository companyRepository,
            IMapper mapper,
            ILogger<BranchService> logger)
        {
            _branchRepository = branchRepository;
            _companyRepository = companyRepository;
            _mapper = mapper;
            _logger = logger;
        }

        public async Task<BranchDto?> GetByIdAsync(Guid branchId)
        {
            try
            {
                var branch = await _branchRepository.GetByIdAsync(branchId);
                if (branch == null)
                    return null;

                return _mapper.Map<BranchDto>(branch);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting branch with ID {BranchId}", branchId);
                throw;
            }
        }

        public async Task<IEnumerable<BranchDto>> GetAllAsync()
        {
            try
            {
                var branches = await _branchRepository.GetAllAsync();
                return _mapper.Map<IEnumerable<BranchDto>>(branches);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting all branches");
                throw;
            }
        }

        public async Task<IEnumerable<BranchDto>> GetByCompanyAsync(Guid companyId)
        {
            try
            {
                // Check if company exists
                var company = await _companyRepository.GetByIdAsync(companyId);
                if (company == null)
                {
                    _logger.LogWarning("Company with ID {CompanyId} not found", companyId);
                    return Enumerable.Empty<BranchDto>();
                }

                var branches = await _branchRepository.GetByCompanyAsync(companyId);
                return _mapper.Map<IEnumerable<BranchDto>>(branches);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting branches for company {CompanyId}", companyId);
                throw;
            }
        }

        public async Task<BranchDto> CreateBranchAsync(CreateBranchRequest request)
        {
            try
            {
                // Check if company exists
                var company = await _companyRepository.GetByIdAsync(request.CompanyId);
                if (company == null)
                {
                    _logger.LogWarning("Company with ID {CompanyId} not found", request.CompanyId);
                    throw new InvalidOperationException($"Company with ID {request.CompanyId} not found");
                }

                var branch = _mapper.Map<Branch>(request);
                var branchId = await _branchRepository.CreateAsync(branch);

                // Get the created branch
                var createdBranch = await _branchRepository.GetByIdAsync(branchId);
                if (createdBranch == null)
                {
                    throw new InvalidOperationException($"Failed to retrieve created branch with ID {branchId}");
                }

                return _mapper.Map<BranchDto>(createdBranch);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating branch");
                throw;
            }
        }

        public async Task<BranchDto?> UpdateBranchAsync(UpdateBranchRequest request)
        {
            try
            {
                // Check if branch exists
                var existingBranch = await _branchRepository.GetByIdAsync(request.BranchId);
                if (existingBranch == null)
                {
                    _logger.LogWarning("Branch with ID {BranchId} not found", request.BranchId);
                    return null;
                }

                // Check if company exists
                var company = await _companyRepository.GetByIdAsync(request.CompanyId);
                if (company == null)
                {
                    _logger.LogWarning("Company with ID {CompanyId} not found", request.CompanyId);
                    throw new InvalidOperationException($"Company with ID {request.CompanyId} not found");
                }

                var branch = _mapper.Map<Branch>(request);
                var success = await _branchRepository.UpdateAsync(branch);

                if (!success)
                {
                    return null;
                }

                // Get the updated branch
                var updatedBranch = await _branchRepository.GetByIdAsync(request.BranchId);
                return _mapper.Map<BranchDto>(updatedBranch);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating branch with ID {BranchId}", request.BranchId);
                throw;
            }
        }

        public async Task<bool> DeleteBranchAsync(Guid branchId)
        {
            try
            {
                // Check if branch exists
                var existingBranch = await _branchRepository.GetByIdAsync(branchId);
                if (existingBranch == null)
                {
                    _logger.LogWarning("Branch with ID {BranchId} not found", branchId);
                    return false;
                }

                return await _branchRepository.DeleteAsync(branchId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting branch with ID {BranchId}", branchId);
                throw;
            }
        }
    }
}
