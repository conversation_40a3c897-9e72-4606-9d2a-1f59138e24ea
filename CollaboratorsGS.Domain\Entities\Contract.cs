
namespace CollaboratorsGS.Domain.Entities
{
    public class Contract
    {
        public Guid ContractId { get; set; }
        public Guid CollaboratorId { get; set; }
        public string ContractContent { get; set; } = string.Empty;
        public string Status { get; set; } = string.Empty;
        public DateTime? SignedAt { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime? UpdatedAt { get; set; }
        public string? CollaboratorName { get; set; }

        // Navigation properties
        public Collaborator? Collaborator { get; set; }
    }
}
