using CollaboratorsGS.Domain.Entities;
using CollaboratorsGS.Domain.Repositories;
using CollaboratorsGS.Infrastructure.Data;
using Dapper;
using System.Data;

namespace CollaboratorsGS.Infrastructure.Repositories
{
    public class PositionRepository : IPositionRepository
    {
        private readonly IConnectionFactory _connectionFactory;

        public PositionRepository(IConnectionFactory connectionFactory)
        {
            _connectionFactory = connectionFactory;
        }

        public async Task<Position?> GetByIdAsync(Guid positionId)
        {
            using var connection = _connectionFactory.CreateConnection();

            var parameters = new DynamicParameters();
            parameters.Add("@PositionId", positionId, DbType.Guid);

            return await connection.QuerySingleOrDefaultAsync<Position>(
                "sp_GetPositionById",
                parameters,
                commandType: CommandType.StoredProcedure);
        }

        public async Task<IEnumerable<Position>> GetAllAsync()
        {
            using var connection = _connectionFactory.CreateConnection();

            return await connection.QueryAsync<Position>(
                "sp_GetAllPositions",
                commandType: CommandType.StoredProcedure);
        }

        public async Task<IEnumerable<Position>> GetByDepartmentAsync(Guid departmentId)
        {
            using var connection = _connectionFactory.CreateConnection();

            var parameters = new DynamicParameters();
            parameters.Add("@DepartmentId", departmentId, DbType.Guid);

            return await connection.QueryAsync<Position>(
                "sp_GetPositionsByDepartment",
                parameters,
                commandType: CommandType.StoredProcedure);
        }

        public async Task<Guid> CreateAsync(Position position)
        {
            // Generate a new UUID if not provided
            if (position.PositionId == Guid.Empty)
            {
                position.PositionId = Guid.NewGuid();
            }

            using var connection = _connectionFactory.CreateConnection();

            var parameters = new DynamicParameters();
            parameters.Add("@PositionId", position.PositionId, DbType.Guid);
            parameters.Add("@PositionName", position.PositionName, DbType.String);
            parameters.Add("@DepartmentId", position.DepartmentId, DbType.Guid);
            parameters.Add("@Description", position.Description, DbType.String);

            await connection.ExecuteAsync(
                "sp_CreatePosition",
                parameters,
                commandType: CommandType.StoredProcedure);

            return position.PositionId;
        }

        public async Task<bool> UpdateAsync(Position position)
        {
            using var connection = _connectionFactory.CreateConnection();

            var parameters = new DynamicParameters();
            parameters.Add("@PositionId", position.PositionId, DbType.Guid);
            parameters.Add("@PositionName", position.PositionName, DbType.String);
            parameters.Add("@DepartmentId", position.DepartmentId, DbType.Guid);
            parameters.Add("@Description", position.Description, DbType.String);

            var rowsAffected = await connection.ExecuteAsync(
                "sp_UpdatePosition",
                parameters,
                commandType: CommandType.StoredProcedure);

            return rowsAffected > 0;
        }

        public async Task<bool> DeleteAsync(Guid positionId)
        {
            using var connection = _connectionFactory.CreateConnection();

            var parameters = new DynamicParameters();
            parameters.Add("@PositionId", positionId, DbType.Guid);

            var rowsAffected = await connection.ExecuteAsync(
                "sp_DeletePosition",
                parameters,
                commandType: CommandType.StoredProcedure);

            return rowsAffected > 0;
        }
    }
}
