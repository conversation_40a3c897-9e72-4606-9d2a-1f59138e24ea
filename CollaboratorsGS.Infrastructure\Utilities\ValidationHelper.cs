using CollaboratorsGS.Application.DTOs;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.ModelBinding;

namespace CollaboratorsGS.Infrastructure.Utilities
{
    public static class ValidationHelper
    {
        /// <summary>
        /// Creates a standardized validation error response from ModelState
        /// </summary>
        /// <param name="modelState">The ModelState to validate</param>
        /// <param name="errorMessage">Custom error message (optional)</param>
        /// <returns>BadRequestObjectResult with standardized ApiResponse format</returns>
        public static BadRequestObjectResult CreateValidationErrorResponse(
            ModelStateDictionary modelState,
            string errorMessage = "Validation failed")
        {
            var errors = modelState
                .Where(x => x.Value?.Errors.Count > 0)
                .SelectMany(x => x.Value!.Errors.Select(e => new ErrorDetail
                {
                    Field = x.Key,
                    ErrorCode = MessageCodes.VE6001,
                    Message = e.ErrorMessage
                }))
                .ToList();

            return new BadRequestObjectResult(ApiResponse<object>.ErrorResponse(
                MessageCodes.VE6001,
                errorMessage,
                400,
                errors));
        }
    }
}