using System;

namespace CollaboratorsGS.Application.DTOs.CandidateApplication
{
    public class CandidateApplicationDto
    {
        public Guid ApplicationId { get; set; }
        public Guid CandidateId { get; set; }
        public Guid PostingId { get; set; }
        public string Title { get; set; } = string.Empty;
        public DateTime ApplicationDate { get; set; }
        public string Status { get; set; } = string.Empty;
        public string? InterviewRound1Result { get; set; }
        public DateTime? InterviewRound1Date { get; set; }
        public string? InterviewRound2Result { get; set; }
        public DateTime? InterviewRound2Date { get; set; }
        public DateTime? OnboardDate { get; set; }
        public DateTime? WarrantyEndDate { get; set; }
        public DateTime? UpdatedAt { get; set; }
        public string CandidateName { get; set; } = string.Empty;
    }

    // DTO for candidate applied positions with job posting details
    public class CandidateAppliedHistoryDto
    {
        public Guid ApplicationId { get; set; }
        public Guid PostingId { get; set; }
        public string JobTitle { get; set; } = string.Empty;
        public string JobCode { get; set; } = string.Empty;
        public string? Level { get; set; }
        public string? Position { get; set; }
        public string? WorkingLocation { get; set; }
        public int? SalaryFrom { get; set; }
        public int? SalaryTo { get; set; }
        public DateTime ApplicationDate { get; set; }
        public string Status { get; set; } = string.Empty;
        public DateTime? ExpiredAt { get; set; }
    }

    // DTO for candidate application history timeline
    public class CandidateApplicationDetailDto
    {
        public Guid ApplicationId { get; set; }
        public Guid PostingId { get; set; }
        public string JobTitle { get; set; } = string.Empty;
        public string JobCode { get; set; } = string.Empty;
        public List<ApplicationStatusDetailDto> StatusHistory { get; set; } = new List<ApplicationStatusDetailDto>();
    }

     public class ApplicationStatusDetailDto
    {
        public string Status { get; set; } = string.Empty;
        public string StatusDisplayName { get; set; } = string.Empty;
        public DateTime Date { get; set; }
        public string? Description { get; set; }
    }
}
