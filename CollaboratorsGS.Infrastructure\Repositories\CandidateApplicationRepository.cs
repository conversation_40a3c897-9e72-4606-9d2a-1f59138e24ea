using CollaboratorsGS.Domain.Entities;
using CollaboratorsGS.Domain.Repositories;
using CollaboratorsGS.Infrastructure.Data;
using Dapper;
using System.Data;

namespace CollaboratorsGS.Infrastructure.Repositories
{
    public class CandidateApplicationRepository : ICandidateApplicationRepository
    {
        private readonly IConnectionFactory _connectionFactory;

        public CandidateApplicationRepository(IConnectionFactory connectionFactory)
        {
            _connectionFactory = connectionFactory;
        }

        private static CandidateApplication MapFromDynamic(dynamic row)
        {
            return new CandidateApplication
            {
                ApplicationId = row.application_id,
                CandidateId = row.candidate_id,
                PostingId = row.posting_id,
                ApplicationDate = row.application_date,
                Status = row.status,
                InterviewRound1Result = row.interview_round1_result,
                InterviewRound1Date = row.interview_round1_date,
                InterviewRound2Result = row.interview_round2_result,
                InterviewRound2Date = row.interview_round2_date,
                OnboardDate = row.onboard_date,
                WarrantyEndDate = row.warranty_end_date,
                UpdatedAt = row.updated_at,
                CandidateName = row.candidate_name,
                Title = row.title
            };
        }

        public async Task<CandidateApplication?> GetByIdAsync(Guid applicationId)
        {
            using var connection = _connectionFactory.CreateConnection();

            var parameters = new DynamicParameters();
            parameters.Add("@ApplicationId", applicationId, DbType.Guid);

            var row = await connection.QuerySingleOrDefaultAsync(
                "sp_GetCandidateApplicationById",
                parameters,
                commandType: CommandType.StoredProcedure);

            if (row == null) return null;

            return MapFromDynamic(row);
        }

        public async Task<IEnumerable<CandidateApplication>> GetAllAsync()
        {
            using var connection = _connectionFactory.CreateConnection();

            // Sử dụng dynamic mapping để đảm bảo mapping chính xác
            var results = await connection.QueryAsync(
                "sp_GetAllCandidateApplications",
                commandType: CommandType.StoredProcedure);

            return results.Select(MapFromDynamic);
        }

        public async Task<IEnumerable<CandidateApplication>> GetByCandidateAsync(Guid candidateId)
        {
            using var connection = _connectionFactory.CreateConnection();

            var parameters = new DynamicParameters();
            parameters.Add("@CandidateId", candidateId, DbType.Guid);

            var results = await connection.QueryAsync(
                "sp_GetCandidateApplicationsByCandidate",
                parameters,
                commandType: CommandType.StoredProcedure);

            return results.Select(MapFromDynamic);
        }

        public async Task<IEnumerable<CandidateApplication>> GetByPostingAsync(Guid postingId)
        {
            using var connection = _connectionFactory.CreateConnection();

            var parameters = new DynamicParameters();
            parameters.Add("@PostingId", postingId, DbType.Guid);

            var results = await connection.QueryAsync(
                "sp_GetCandidateApplicationsByPosting",
                parameters,
                commandType: CommandType.StoredProcedure);

            return results.Select(MapFromDynamic);
        }

        public async Task<IEnumerable<CandidateApplication>> GetByStatusAsync(string status)
        {
            using var connection = _connectionFactory.CreateConnection();

            var parameters = new DynamicParameters();
            parameters.Add("@Status", status, DbType.String);

            var results = await connection.QueryAsync(
                "sp_GetCandidateApplicationsByStatus",
                parameters,
                commandType: CommandType.StoredProcedure);

            return results.Select(MapFromDynamic);
        }

        public async Task<Guid> CreateAsync(CandidateApplication application)
        {
            // Generate a new UUID if not provided
            if (application.ApplicationId == Guid.Empty)
            {
                application.ApplicationId = Guid.NewGuid();
            }

            using var connection = _connectionFactory.CreateConnection();

            var parameters = new DynamicParameters();
            parameters.Add("@ApplicationId", application.ApplicationId, DbType.Guid);
            parameters.Add("@CandidateId", application.CandidateId, DbType.Guid);
            parameters.Add("@PostingId", application.PostingId, DbType.Guid);
            parameters.Add("@ApplicationDate", application.ApplicationDate, DbType.Date);
            parameters.Add("@Status", application.Status, DbType.String);

            await connection.ExecuteAsync(
                "sp_CreateCandidateApplication",
                parameters,
                commandType: CommandType.StoredProcedure);

            return application.ApplicationId;
        }

        public async Task<bool> UpdateAsync(CandidateApplication application)
        {
            using var connection = _connectionFactory.CreateConnection();

            var parameters = new DynamicParameters();
            parameters.Add("@ApplicationId", application.ApplicationId, DbType.Guid);
            parameters.Add("@Status", application.Status, DbType.String);
            parameters.Add("@InterviewRound1Result", application.InterviewRound1Result, DbType.String);
            parameters.Add("@InterviewRound1Date", application.InterviewRound1Date, DbType.Date);
            parameters.Add("@InterviewRound2Result", application.InterviewRound2Result, DbType.String);
            parameters.Add("@InterviewRound2Date", application.InterviewRound2Date, DbType.Date);
            parameters.Add("@OnboardDate", application.OnboardDate, DbType.Date);
            parameters.Add("@WarrantyEndDate", application.WarrantyEndDate, DbType.Date);

            var rowsAffected = await connection.ExecuteAsync(
                "sp_UpdateCandidateApplication",
                parameters,
                commandType: CommandType.StoredProcedure);

            return rowsAffected > 0;
        }

        public async Task<bool> DeleteAsync(Guid applicationId)
        {
            using var connection = _connectionFactory.CreateConnection();

            var parameters = new DynamicParameters();
            parameters.Add("@ApplicationId", applicationId, DbType.Guid);

            var rowsAffected = await connection.ExecuteAsync(
                "sp_DeleteCandidateApplication",
                parameters,
                commandType: CommandType.StoredProcedure);

            return rowsAffected > 0;
        }
    }
}
