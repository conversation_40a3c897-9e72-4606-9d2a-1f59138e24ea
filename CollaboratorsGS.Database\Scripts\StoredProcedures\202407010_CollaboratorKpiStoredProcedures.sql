-- Stored Procedures for CollaboratorKpi operations

-- Get CollaboratorKpi by ID
IF EXISTS (SELECT * FROM sys.objects WHERE type = 'P' AND name = 'sp_GetCollaboratorKpiById')
    DROP PROCEDURE sp_GetCollaboratorKpiById
GO

CREATE PROCEDURE sp_GetCollaboratorKpiById
    @KpiId UNIQUEIDENTIFIER
AS
BEGIN
    SELECT k.*, ctv.full_name as collaborator_name
    FROM collaborator_kpis k
    INNER JOIN collaborators ctv ON k.collaborator_id = ctv.collaborator_id
    WHERE k.collaborator_kpis_id = @KpiId
END
GO

-- Get collaborator_kpis by CollaboratorId
IF EXISTS (SELECT * FROM sys.objects WHERE type = 'P' AND name = 'sp_GetCollaboratorKpisByCollaboratorId')
    DROP PROCEDURE sp_GetCollaboratorKpisByCollaboratorId
GO

CREATE PROCEDURE sp_GetCollaboratorKpisByCollaboratorId
    @CollaboratorId UNIQUEIDENTIFIER
AS
BEGIN
    SELECT k.*, ctv.full_name as collaborator_name
    FROM collaborator_kpis k
    INNER JOIN collaborators ctv ON k.collaborator_id = ctv.collaborator_id
    WHERE k.collaborator_id = @CollaboratorId
    ORDER BY k.period DESC
END
GO

-- Get collaborator_kpis by Period
IF EXISTS (SELECT * FROM sys.objects WHERE type = 'P' AND name = 'sp_GetCollaboratorKpisByPeriod')
    DROP PROCEDURE sp_GetCollaboratorKpisByPeriod
GO

CREATE PROCEDURE sp_GetCollaboratorKpisByPeriod
    @Period VARCHAR(7)
AS
BEGIN
    SELECT k.*, ctv.full_name as collaborator_name
    FROM collaborator_kpis k
    INNER JOIN collaborators ctv ON k.collaborator_id = ctv.collaborator_id
    WHERE k.period = @Period
    ORDER BY ctv.full_name
END
GO

-- Get All collaborator_kpis
IF EXISTS (SELECT * FROM sys.objects WHERE type = 'P' AND name = 'sp_GetAllCollaboratorKpis')
    DROP PROCEDURE sp_GetAllCollaboratorKpis
GO

CREATE PROCEDURE sp_GetAllCollaboratorKpis
AS
BEGIN
    SELECT k.*, ctv.full_name as collaborator_name
    FROM collaborator_kpis k
    INNER JOIN collaborators ctv ON k.collaborator_id = ctv.collaborator_id
    ORDER BY k.period DESC, ctv.full_name
END
GO

-- Create CollaboratorKpi
IF EXISTS (SELECT * FROM sys.objects WHERE type = 'P' AND name = 'sp_CreateCollaboratorKpi')
    DROP PROCEDURE sp_CreateCollaboratorKpi
GO

CREATE PROCEDURE sp_CreateCollaboratorKpi
    @KpiId UNIQUEIDENTIFIER,
    @CollaboratorId UNIQUEIDENTIFIER,
    @Period VARCHAR(7),
    @TotalCandidatesImported INT = 0,
    @TotalCandidatesPassedRound1 INT = 0,
    @TotalCandidatesPassedRound2 INT = 0,
    @TotalCandidatesOnboarded INT = 0,
    @TotalCandidatesFailed INT = 0,
    @TotalCandidatesOnboardedWarranty INT = 0,
    @SuccessRate FLOAT = NULL,
    @CalculatedAt DATETIME
AS
BEGIN
    INSERT INTO collaborator_kpis (
        collaborator_kpis_id, collaborator_id, period, total_candidates_imported, total_candidates_passed_round1,
        total_candidates_passed_round2, total_candidates_onboarded, total_candidates_failed,
        total_candidates_onboarded_warranty, success_rate, calculated_at
    )
    VALUES (
        @KpiId, @CollaboratorId, @Period, @TotalCandidatesImported, @TotalCandidatesPassedRound1,
        @TotalCandidatesPassedRound2, @TotalCandidatesOnboarded, @TotalCandidatesFailed,
        @TotalCandidatesOnboardedWarranty, @SuccessRate, @CalculatedAt
    )
END
GO

-- Update CollaboratorKpi
IF EXISTS (SELECT * FROM sys.objects WHERE type = 'P' AND name = 'sp_UpdateCollaboratorKpi')
    DROP PROCEDURE sp_UpdateCollaboratorKpi
GO

CREATE PROCEDURE sp_UpdateCollaboratorKpi
    @KpiId UNIQUEIDENTIFIER,
    @TotalCandidatesImported INT = NULL,
    @TotalCandidatesPassedRound1 INT = NULL,
    @TotalCandidatesPassedRound2 INT = NULL,
    @TotalCandidatesOnboarded INT = NULL,
    @TotalCandidatesFailed INT = NULL,
    @TotalCandidatesOnboardedWarranty INT = NULL,
    @SuccessRate FLOAT = NULL,
    @CalculatedAt DATETIME
AS
BEGIN
    UPDATE collaborator_kpis
    SET total_candidates_imported = ISNULL(@TotalCandidatesImported, total_candidates_imported),
        total_candidates_passed_round1 = ISNULL(@TotalCandidatesPassedRound1, total_candidates_passed_round1),
        total_candidates_passed_round2 = ISNULL(@TotalCandidatesPassedRound2, total_candidates_passed_round2),
        total_candidates_onboarded = ISNULL(@TotalCandidatesOnboarded, total_candidates_onboarded),
        total_candidates_failed = ISNULL(@TotalCandidatesFailed, total_candidates_failed),
        total_candidates_onboarded_warranty = ISNULL(@TotalCandidatesOnboardedWarranty, total_candidates_onboarded_warranty),
        success_rate = @SuccessRate,
        calculated_at = @CalculatedAt
    WHERE collaborator_kpis_id = @KpiId
END
GO

-- Delete CollaboratorKpi
IF EXISTS (SELECT * FROM sys.objects WHERE type = 'P' AND name = 'sp_DeleteCollaboratorKpi')
    DROP PROCEDURE sp_DeleteCollaboratorKpi
GO

CREATE PROCEDURE sp_DeleteCollaboratorKpi
    @KpiId UNIQUEIDENTIFIER
AS
BEGIN
    DELETE FROM collaborator_kpis
    WHERE collaborator_kpis_id = @KpiId
END
GO


