using AutoMapper;
using CollaboratorsGS.Application.DTOs.ObjectData;
using CollaboratorsGS.Application.Interfaces;
using CollaboratorsGS.Domain.Entities;
using CollaboratorsGS.Domain.Repositories;
using Microsoft.Extensions.Logging;

namespace CollaboratorsGS.Infrastructure.Services
{
    public class ObjectDataService : IObjectDataService
    {
        private readonly IObjectDataRepository _objectDataRepository;
        private readonly IMapper _mapper;
        private readonly ILogger<ObjectDataService> _logger;

        public ObjectDataService(
            IObjectDataRepository objectDataRepository,
            IMapper mapper,
            ILogger<ObjectDataService> logger)
        {
            _objectDataRepository = objectDataRepository;
            _mapper = mapper;
            _logger = logger;
        }

        public async Task<ObjectDataDto?> GetByIdAsync(Guid objectId)
        {
            try
            {
                var objectData = await _objectDataRepository.GetByIdAsync(objectId);
                if (objectData == null)
                    return null;

                return _mapper.Map<ObjectDataDto>(objectData);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting object data with ID {ObjectId}", objectId);
                throw;
            }
        }

        public async Task<IEnumerable<ObjectDataDto>> GetAllAsync()
        {
            try
            {
                var objectDataList = await _objectDataRepository.GetAllAsync();
                return _mapper.Map<IEnumerable<ObjectDataDto>>(objectDataList);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting all object data");
                throw;
            }
        }

        public async Task<IEnumerable<ObjectDataDto>> GetByTypeAsync(string objectType)
        {
            try
            {
                var objectDataList = await _objectDataRepository.GetByTypeAsync(objectType);
                return _mapper.Map<IEnumerable<ObjectDataDto>>(objectDataList);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting object data by type {ObjectType}", objectType);
                throw;
            }
        }

        public async Task<ObjectDataDto?> GetByCodeAsync(string objectCode)
        {
            try
            {
                var objectData = await _objectDataRepository.GetByCodeAsync(objectCode);
                if (objectData == null)
                    return null;

                return _mapper.Map<ObjectDataDto>(objectData);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting object data by code {ObjectCode}", objectCode);
                throw;
            }
        }

        public async Task<IEnumerable<ObjectDataDto>> GetByValueAsync(string objectValue)
        {
            try
            {
                var objectDataList = await _objectDataRepository.GetByValueAsync(objectValue);
                return _mapper.Map<IEnumerable<ObjectDataDto>>(objectDataList);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting object data by value {ObjectValue}", objectValue);
                throw;
            }
        }

        public async Task<Guid> CreateAsync(CreateObjectDataRequest request)
        {
            try
            {
                var objectData = _mapper.Map<ObjectData>(request);

                // Set additional properties
                objectData.CreatedAt = DateTime.UtcNow;

                return await _objectDataRepository.CreateAsync(objectData);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating object data");
                throw;
            }
        }

        public async Task<bool> UpdateAsync(Guid objectId, UpdateObjectDataRequest request)
        {
            try
            {
                // Check if object data exists
                var existingObjectData = await _objectDataRepository.GetByIdAsync(objectId);
                if (existingObjectData == null)
                {
                    _logger.LogWarning("Object data with ID {ObjectId} not found", objectId);
                    return false;
                }

                // Update properties
                existingObjectData.ObjectType = request.ObjectType;
                existingObjectData.ObjectCode = request.ObjectCode;
                existingObjectData.ObjectValue = request.ObjectValue;
                existingObjectData.Description = request.Description;
                existingObjectData.UpdatedAt = DateTime.UtcNow;

                return await _objectDataRepository.UpdateAsync(existingObjectData);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating object data with ID {ObjectId}", objectId);
                throw;
            }
        }

        public async Task<bool> DeleteAsync(Guid objectId)
        {
            try
            {
                // Check if object data exists
                var existingObjectData = await _objectDataRepository.GetByIdAsync(objectId);
                if (existingObjectData == null)
                {
                    _logger.LogWarning("Object data with ID {ObjectId} not found", objectId);
                    return false;
                }

                return await _objectDataRepository.DeleteAsync(objectId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting object data with ID {ObjectId}", objectId);
                throw;
            }
        }
    }
}
