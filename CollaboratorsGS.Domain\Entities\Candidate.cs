﻿
namespace CollaboratorsGS.Domain.Entities
{
    public class Candidate
    {
        public Guid CandidateId { get; set; }
        public string FullName { get; set; } = string.Empty;
        public string PhoneNumber { get; set; } = string.Empty;
        public string? Email { get; set; }
        public string? EducationLevel { get; set; }
        public string? WorkExperience { get; set; }
        public string? Skills { get; set; }
        public DateTime? DateOfBirth { get; set; }
        public string? Gender { get; set; }
        public string? Address { get; set; }
        public string? Ward { get; set; }
        public string? District { get; set; }
        public string? Province { get; set; }
        public string? FullAddress { get; set; }
        public string? ProfilePicture { get; set; }
        public string? FullBodyPicture { get; set; }
        public int? HeightCm { get; set; }
        public int? WeightKg { get; set; }
        public string? Level { get; set; }
        public string? Source { get; set; }
        public Guid? CollaboratorId { get; set; }
        public string? CitizenId { get; set; }
        public string? CitizenIdAddress { get; set; }
        public DateTime? CitizenIdIssueDate { get; set; }
        public string? CitizenIdIssuePlace { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime? UpdatedAt { get; set; }
        // Navigation properties
        public Collaborator? Collaborator { get; set; }
        public ICollection<CandidateApplication>? Applications { get; set; }
        public ICollection<CandidateEmploymentHistory>? EmploymentHistory { get; set; }
        public ICollection<CandidateDocument>? Documents { get; set; }
    }
}
