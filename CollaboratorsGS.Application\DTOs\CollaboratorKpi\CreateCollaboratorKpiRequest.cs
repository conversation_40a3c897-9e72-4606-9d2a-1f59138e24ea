using System;
using System.ComponentModel.DataAnnotations;

namespace CollaboratorsGS.Application.DTOs.CollaboratorKpi
{
    public class CreateCollaboratorKpiRequest
    {
        [Required]
        public Guid CollaboratorId { get; set; }
        
        [Required]
        [RegularExpression(@"^\d{4}-\d{2}$", ErrorMessage = "Period must be in format YYYY-MM")]
        public string Period { get; set; } = string.Empty;
        
        public int TotalCandidatesImported { get; set; } = 0;
        
        public int TotalCandidatesPassedRound1 { get; set; } = 0;
        
        public int TotalCandidatesPassedRound2 { get; set; } = 0;
        
        public int TotalCandidatesOnboarded { get; set; } = 0;
        
        public int TotalCandidatesFailed { get; set; } = 0;
        
        public int TotalCandidatesOnboardedWarranty { get; set; } = 0;
        
        public float? SuccessRate { get; set; }
    }
}
