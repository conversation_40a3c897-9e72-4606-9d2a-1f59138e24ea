using Minio.Helper;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Globalization;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading.Tasks;

namespace CollaboratorsGS.Infrastructure.Utilities
{
    public static class Utilities
    {
        static string[] validFileTypes = { ".xlsx", ".xls", ".doc", ".docx", ".pdf", ".jpg", ".jpeg", ".gif", ".png" };
        public static string getHtmlFile(string root, string filename)
        {
            string filePaths = Path.Combine(root, "Files/EmailTemplate/", filename);
            var htmlString = System.IO.File.ReadAllText(filePaths);
            return htmlString;
        }
        public static void EnsureDirExist(string path)
        {
            if (!Directory.Exists(path))
            {
                string directoryName = Path.GetDirectoryName(path);
                EnsureDirExist(directoryName);
                Directory.CreateDirectory(path);
            }
        }
        public static FileInfo GetFileExcel(string folder, string filePath, string fileName)
        {
            FileInfo fileInfoTemplate = null;
            fileName = $"{Guid.NewGuid()}{fileName}";
            fileInfoTemplate = new FileInfo(filePath);
            string path = Path.Combine(Directory.GetCurrentDirectory(), "wwwroot", folder);
            EnsureDirExist(path);
            string file = Path.Combine(path, fileName);
            FileInfo filewrite = fileInfoTemplate.CopyTo(file);
            return filewrite;
        }
        
        public static string RemoveSign(string arg)
        {
            if (string.IsNullOrEmpty(arg)) return arg;
            Regex regex = new Regex(@"\p{IsCombiningDiacriticalMarks}+");
            string strFormD = arg.Normalize(System.Text.NormalizationForm.FormD);
            while (strFormD.Contains(" "))
            {
                strFormD = strFormD.Replace(" ", "-");
            }
            strFormD = Regex.Replace(strFormD, " *[\\~#%&*{}/:<>?|\"]+ *", "");
            return regex.Replace(strFormD, String.Empty).Replace('\u0111', 'd').Replace('\u0110', 'D').ToLower();
        }
        private static Dictionary<string, string> GetMimeTypes()
        {
            return new Dictionary<string, string>
            {
                {".txt", "text/plain"},
                {".pdf", "application/pdf"},
                {".doc", "application/vnd.ms-word"},
                {".docx", "application/vnd.ms-word"},
                {".xls", "application/vnd.ms-excel"},
                {".xlsx", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"},
                {".png", "image/png"},
                {".jpg", "image/jpeg"},
                {".jpeg", "image/jpeg"},
                {".gif", "image/gif"},
                {".csv", "text/csv"}
            };
        }
        public static T GetObjectFromJSON<T>(string json) where T : new()
        {
            return JsonConvert.DeserializeObject<T>(json);
        }
        public static bool ValidFileType(object Extension)
        {
            return validFileTypes.Contains(Extension);
        }
        public static Guid ConvertStringToGuid(object arg)
        {
            if (ToString(arg).Length == 0) return Guid.Empty;

            if (Guid.TryParse(ToString(arg), out Guid result))
                return result;
            else
                return Guid.Empty;
        }
        public static int ToInt(object arg)
        {
            if (ToString(arg).Length == 0) return 0;
            return Convert.ToInt32(arg);
        }
        public static long ToInt64(object arg)
        {
            if (ToString(arg).Length == 0) return 0;
            return Convert.ToInt64(arg);
        }
        public static string ToString(object arg)
        {
            if (arg == null) return "";
            return arg.ToString().Trim();
        }
        public static bool ToBool(object arg)
        {
            if (ToString(arg).Length == 0) return false;
            return Convert.ToBoolean(arg);
        }
        public static decimal ConvertToDecimal(object arg)
        {
            decimal result = 0;
            string input = arg != null ? arg.ToString() : "";

            // remove empty spaces
            input = input.Replace(" ", "");
            // checks if the string is empty
            if (string.IsNullOrEmpty(input) == false)
            {
                // check if input has , and . for thousands separator and decimal place
                if (input.Contains(",") && input.Contains("."))
                {
                    // find the decimal separator, might be , or .
                    int decimalpos = input.LastIndexOf(',') > input.LastIndexOf('.') ? input.LastIndexOf(',') : input.LastIndexOf('.');
                    // uses | as a temporary decimal separator
                    input = input.Substring(0, decimalpos) + "|" + input.Substring(decimalpos + 1);
                    // formats the output removing the , and . and replacing the temporary | with .
                    input = input.Replace(".", "").Replace(",", "").Replace("|", ".");
                }
                // replaces , with .
                if (input.Contains(","))
                {
                    input = input.Replace(',', '.');
                }
                // checks if the input number has thousands separator and no decimal places
                if (input.Count(item => item == '.') > 1)
                {
                    input = input.Replace(".", "");
                }
                // tries to convert input to double
                if (decimal.TryParse(input, out result) == true)
                {
                    result = decimal.Parse(input, System.Globalization.NumberStyles.AllowLeadingSign | NumberStyles.AllowDecimalPoint | NumberStyles.AllowThousands, CultureInfo.InvariantCulture);
                }

            }
            return result;
        }
        public static double ConvertToDouble(object arg)
        {
            double result = 0;
            string input = arg != null ? arg.ToString() : "";

            // remove empty spaces
            input = input.Replace(" ", "");
            // checks if the string is empty
            if (string.IsNullOrEmpty(input) == false)
            {
                // check if input has , and . for thousands separator and decimal place
                if (input.Contains(",") && input.Contains("."))
                {
                    // find the decimal separator, might be , or .
                    int decimalpos = input.LastIndexOf(',') > input.LastIndexOf('.') ? input.LastIndexOf(',') : input.LastIndexOf('.');
                    // uses | as a temporary decimal separator
                    input = input.Substring(0, decimalpos) + "|" + input.Substring(decimalpos + 1);
                    // formats the output removing the , and . and replacing the temporary | with .
                    input = input.Replace(".", "").Replace(",", "").Replace("|", ".");
                }
                // replaces , with .
                if (input.Contains(","))
                {
                    input = input.Replace(',', '.');
                }
                // checks if the input number has thousands separator and no decimal places
                if (input.Count(item => item == '.') > 1)
                {
                    input = input.Replace(".", "");
                }
                // tries to convert input to double
                if (double.TryParse(input, out result) == true)
                {
                    result = double.Parse(input, System.Globalization.NumberStyles.AllowLeadingSign | NumberStyles.AllowDecimalPoint | NumberStyles.AllowThousands, CultureInfo.InvariantCulture);
                }

            }
            return result;
        }
        public static string[] SplitToArray(string value, int length)
        {
            int strLength = value.Length;
            int strCount = (strLength + length - 1) / length;
            string[] result = new string[strCount];
            for (int i = 0; i < strCount; ++i)
            {
                result[i] = value.Substring(i * length, Math.Min(length, strLength));
                strLength -= length;
            }
            return result;
        }
        public static DataTable ToDataTable<T>(this List<T> iList)
        {
            DataTable dataTable = new DataTable();
            PropertyDescriptorCollection propertyDescriptorCollection =
                TypeDescriptor.GetProperties(typeof(T));
            for (int i = 0; i < propertyDescriptorCollection.Count; i++)
            {
                PropertyDescriptor propertyDescriptor = propertyDescriptorCollection[i];
                Type type = propertyDescriptor.PropertyType;

                if (type.IsGenericType && type.GetGenericTypeDefinition() == typeof(Nullable<>))
                    type = Nullable.GetUnderlyingType(type);


                dataTable.Columns.Add(propertyDescriptor.Name, type);
            }
            object[] values = new object[propertyDescriptorCollection.Count];
            foreach (T iListItem in iList)
            {
                for (int i = 0; i < values.Length; i++)
                {
                    values[i] = propertyDescriptorCollection[i].GetValue(iListItem);
                }
                dataTable.Rows.Add(values);
            }
            return dataTable;
        }
    }
}
