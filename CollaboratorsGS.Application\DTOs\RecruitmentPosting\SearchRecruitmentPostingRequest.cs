using System.ComponentModel.DataAnnotations;
using CollaboratorsGS.Application.DTOs.Common;
using CollaboratorsGS.Domain.Enums;

namespace CollaboratorsGS.Application.DTOs.RecruitmentPosting
{
    public class SearchRecruitmentPostingRequest : PaginationRequest
    {
        public string? keyword { get; set; }
        public string? level { get; set; }
        public string? working_location { get; set; }
        public int? salary_from { get; set; }
        public int? salary_to { get; set; }
        public bool? is_urgent { get; set; }
        public bool? is_hot { get; set; }
        public RecruitmentPostingStatus? status { get; set; }
    }
}
