using System;
using System.ComponentModel.DataAnnotations;

namespace CollaboratorsGS.Application.DTOs.CandidateApplication
{
    public class UpdateCandidateApplicationRequest
    {
        [Required]
        public Guid ApplicationId { get; set; }
        
        [Required]
        public string Status { get; set; } = string.Empty;
        
        public string? InterviewRound1Result { get; set; }
        
        public DateTime? InterviewRound1Date { get; set; }
        
        public string? InterviewRound2Result { get; set; }
        
        public DateTime? InterviewRound2Date { get; set; }
        
        public DateTime? OnboardDate { get; set; }
        
        public DateTime? WarrantyEndDate { get; set; }
    }
}
