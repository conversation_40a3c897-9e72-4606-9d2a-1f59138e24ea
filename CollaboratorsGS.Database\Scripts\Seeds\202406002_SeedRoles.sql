-- Seed Roles with <PERSON><PERSON><PERSON>
IF NOT EXISTS (SELECT TOP 1 1 FROM Roles)
BEGIN
    PRINT 'Seeding Roles with UUID...';

    -- Create basic Roles with UUIDs
    DECLARE @admin_role_id UNIQUEIDENTIFIER = NEWID();
    <PERSON><PERSON>AR<PERSON> @user_role_id UNIQUEIDENTIFIER = NEWID();
    <PERSON><PERSON><PERSON><PERSON> @recruiter_role_id UNIQUEIDENTIFIER = NEWID();
    DECLARE @manager_role_id UNIQUEIDENTIFIER = NEWID();
    DECLARE @collaborator_role_id UNIQUEIDENTIFIER = '10C8EE97-2C36-4963-83AB-46CC536E58D8';

    -- Insert Roles with explicit UUIDs
    INSERT INTO Roles (role_id, role_name, description) VALUES (@admin_role_id, 'Admin', 'Administrator with full access to all features');
    INSERT INTO Roles (role_id, role_name, description) VALUES (@user_role_id, 'User', 'Regular user with limited access');
    INSERT INTO Roles (role_id, role_name, description) VALUES (@recruiter_role_id, 'Recruiter', 'Recruitment staff with access to recruitment features');
    INSERT INTO Roles (role_id, role_name, description) VALUES (@manager_role_id, 'Manager', 'Department manager with access to management features');
    INSERT INTO Roles (role_id, role_name, description) VALUES (@collaborator_role_id, 'Collaborator', 'External collaborator with access to collaborator features');
    
    PRINT 'Roles seeded successfully.';
END
ELSE
BEGIN
    PRINT 'Roles already exist. Skipping...';
END
GO
