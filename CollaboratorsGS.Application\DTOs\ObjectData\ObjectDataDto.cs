using System;

namespace CollaboratorsGS.Application.DTOs.ObjectData
{
    public class ObjectDataDto
    {
        public Guid ObjectId { get; set; } // Changed from int to Guid
        public string ObjectType { get; set; } = string.Empty;
        public string? ObjectCode { get; set; }
        public string ObjectValue { get; set; } = string.Empty;
        public string? Description { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime? UpdatedAt { get; set; }
    }
}
