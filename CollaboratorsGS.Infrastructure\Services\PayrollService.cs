using AutoMapper;
using CollaboratorsGS.Application.DTOs.Candidate;
using CollaboratorsGS.Application.DTOs.Collaborator;
using CollaboratorsGS.Application.DTOs;
using CollaboratorsGS.Application.Interfaces;
using CollaboratorsGS.Domain.Entities;
using CollaboratorsGS.Domain.Repositories;
using CollaboratorsGS.Infrastructure.Repositories;
using Microsoft.AspNetCore.Routing.Matching;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace CollaboratorsGS.Infrastructure.Services
{
    public class PayrollService : IPayrollService
    {
        private readonly IPayrollRepository _payrollRepository;
        private readonly IMapper _mapper;
        public PayrollService(
          IPayrollRepository payrollRepository,
          IMapper mapper)
        {
            _payrollRepository = payrollRepository;
            _mapper = mapper;
        }

        public async Task<IEnumerable<Payroll>> GetListAsync(int Year, int month, int UserId)
        {
            var payrolllist = await _payrollRepository.GetListAsync(Year, month, UserId);
            return payrolllist; 
        }

        public async Task<Payroll> GetByIdAsync(int payroll_id, int UserId)
        {
            return await _payrollRepository.GetByIdAsync(payroll_id, UserId);
        }
    }
}
