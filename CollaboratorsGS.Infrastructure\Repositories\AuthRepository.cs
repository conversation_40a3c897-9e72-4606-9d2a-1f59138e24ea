using CollaboratorsGS.Domain.Entities;
using CollaboratorsGS.Domain.Repositories;
using CollaboratorsGS.Infrastructure.Data;
using Dapper;

namespace CollaboratorsGS.Infrastructure.Repositories
{
    public class AuthRepository : IAuthRepository
    {
        private readonly IConnectionFactory _connectionFactory;

        public AuthRepository(IConnectionFactory connectionFactory)
        {
            _connectionFactory = connectionFactory;
        }

        public async Task<Authen> CreateTokenAsync(Authen token)
        {
            if (token.TokenId == Guid.Empty)
                token.TokenId = Guid.NewGuid();

            using var connection = _connectionFactory.CreateConnection();

            var query = @"
                INSERT INTO authen (token_id, user_id, access_token, refresh_token, issued_at, expires_at, status)
                VALUES (@token_id, @user_id, @access_token, @refresh_token, @issued_at, @expires_at, @status)";

            var parameters = DapperParamHelper.ToSnakeCaseParams(token);
            await connection.ExecuteAsync(query, parameters);

            return token;
        }

        public async Task<bool> RevokeTokenAsync(string refreshToken)
        {
            using var connection = _connectionFactory.CreateConnection();

            var query = @"
                UPDATE Authen
                SET status = 'Revoked'
                WHERE refresh_token = @RefreshToken AND status = 'Active'";

            var rowsAffected = await connection.ExecuteAsync(query, new { RefreshToken = refreshToken });
            return rowsAffected > 0;
        }

        public async Task<Authen?> GetByRefreshTokenAsync(string refreshToken)
        {
            using var connection = _connectionFactory.CreateConnection();

            var query = @"
                SELECT a.*, u.*
                FROM Authen a
                INNER JOIN Users u ON a.user_id = u.user_id
                WHERE a.refresh_token = @RefreshToken AND a.status = 'Active'";

            var result = await connection.QueryAsync<Authen, User, Authen>(
                query,
                (token, user) =>
                {
                    token.User = user;
                    return token;
                },
                new { RefreshToken = refreshToken },
                splitOn: "user_id"
            );

            return result.FirstOrDefault();
        }

        public async Task<bool> RevokeAllUserTokensAsync(Guid userId)
        {
            using var connection = _connectionFactory.CreateConnection();

            var query = @"
                UPDATE Authen
                SET status = 'Revoked'
                WHERE user_id = @UserId AND status = 'Active'";

            var rowsAffected = await connection.ExecuteAsync(query, new { UserId = userId });
            return rowsAffected > 0;
        }

        public async Task<PasswordResetToken> CreatePasswordResetTokenAsync(PasswordResetToken token)
        {
            // Generate a new UUID if not provided
            if (token.TokenId == Guid.Empty)
            {
                token.TokenId = Guid.NewGuid();
            }

            using var connection = _connectionFactory.CreateConnection();

            var query = @"
                INSERT INTO password_reset_tokens (token_id, user_id, Token, expires_at, created_at)
                VALUES (@TokenId, @UserId, @Token, @ExpiresAt, @CreatedAt)";

            await connection.ExecuteAsync(query, token);

            return token;
        }

        public async Task<PasswordResetToken?> GetPasswordResetTokenAsync(string token)
        {
            using var connection = _connectionFactory.CreateConnection();

            var query = @"
                SELECT t.*, u.*
                FROM password_reset_tokens t
                INNER JOIN Users u ON t.user_id = u.user_id
                WHERE t.token = @Token AND t.expires_at > GETUTCDATE()";

            var result = await connection.QueryAsync<PasswordResetToken, User, PasswordResetToken>(
                query,
                (resetToken, user) =>
                {
                    resetToken.User = user;
                    return resetToken;
                },
                new { Token = token },
                splitOn: "user_id"
            );

            return result.FirstOrDefault();
        }

        public async Task<bool> InvalidatePasswordResetTokenAsync(string token)
        {
            using var connection = _connectionFactory.CreateConnection();

            var query = @"
                DELETE FROM password_reset_tokens
                WHERE token = @Token";

            var rowsAffected = await connection.ExecuteAsync(query, new { Token = token });
            return rowsAffected > 0;
        }

        public async Task<ExternalAuthInfo?> GetExternalAuthInfoAsync(string provider, string externalId)
        {
            using var connection = _connectionFactory.CreateConnection();

            // First check if the table exists, if not create it
            var checkTableQuery = @"
                            IF NOT EXISTS (
                                SELECT * FROM sys.tables 
                                WHERE name = 'external_auth_info' AND schema_id = SCHEMA_ID('dbo')
                            )
                            BEGIN
                                CREATE TABLE [dbo].[external_auth_info] (
                                    [id] UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
                                    [user_id] UNIQUEIDENTIFIER NOT NULL,
                                    [provider] VARCHAR(50) NOT NULL,
                                    [external_id] VARCHAR(255) NOT NULL,
                                    [email] VARCHAR(255) NULL,
                                    [name] VARCHAR(255) NULL,
                                    [created_at] DATETIME NOT NULL DEFAULT GETDATE(),
                                    [last_login] DATETIME NULL,
                                    CONSTRAINT [fk_external_auth_info_users] 
                                        FOREIGN KEY ([user_id]) REFERENCES [dbo].[users] ([user_id])
                                );

                                CREATE UNIQUE INDEX [ix_external_auth_info_provider_external_id] 
                                ON [dbo].[external_auth_info]([provider], [external_id]);
                            END
                            ";

            await connection.ExecuteAsync(checkTableQuery);

            var query = @"
                SELECT e.*, u.*
                FROM external_auth_info e
                INNER JOIN Users u ON e.UserId = u.UserId
                WHERE e.Provider = @Provider AND e.ExternalId = @ExternalId";

            var result = await connection.QueryAsync<ExternalAuthInfo, User, ExternalAuthInfo>(
                query,
                (externalAuth, user) =>
                {
                    externalAuth.User = user;
                    return externalAuth;
                },
                new { Provider = provider, ExternalId = externalId },
                splitOn: "user_id"
            );

            return result.FirstOrDefault();
        }

        public async Task<ExternalAuthInfo> CreateExternalAuthInfoAsync(ExternalAuthInfo externalAuthInfo)
        {
            using var connection = _connectionFactory.CreateConnection();

            // First check if the table exists, if not create it
            var checkTableQuery = @"
                            IF NOT EXISTS (
                                SELECT * FROM sys.tables 
                                WHERE name = 'external_auth_info' AND schema_id = SCHEMA_ID('dbo')
                            )
                            BEGIN
                                CREATE TABLE [dbo].[external_auth_info] (
                                    [id] UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
                                    [user_id] UNIQUEIDENTIFIER NOT NULL,
                                    [provider] VARCHAR(50) NOT NULL,
                                    [external_id] VARCHAR(255) NOT NULL,
                                    [email] VARCHAR(255) NULL,
                                    [name] VARCHAR(255) NULL,
                                    [created_at] DATETIME NOT NULL DEFAULT GETDATE(),
                                    [last_login] DATETIME NULL,
                                    CONSTRAINT [fk_external_auth_info_users] 
                                        FOREIGN KEY ([user_id]) REFERENCES [dbo].[users] ([user_id])
                                );

                                CREATE UNIQUE INDEX [ix_external_auth_info_provider_external_id] 
                                ON [dbo].[external_auth_info]([provider], [external_id]);
                            END
                            ";

            await connection.ExecuteAsync(checkTableQuery);

            // Generate a new UUID if not provided
            if (externalAuthInfo.Id == Guid.Empty)
            {
                externalAuthInfo.Id = Guid.NewGuid();
            }

            var query = @"
                INSERT INTO external_auth_info (Id, User_Id, Provider, External_Id, Email, Name, Created_At, Last_Login)
                VALUES (@Id, @UserId, @Provider, @ExternalId, @Email, @Name, @CreatedAt, @LastLogin)";

            await connection.ExecuteAsync(query, externalAuthInfo);

            return externalAuthInfo;
        }

        public async Task<bool> UpdateExternalAuthInfoAsync(ExternalAuthInfo externalAuthInfo)
        {
            using var connection = _connectionFactory.CreateConnection();

            var query = @"
                UPDATE external_auth_info
                SET Email = @Email,
                    Name = @Name,
                    LastLogin = @LastLogin
                WHERE Id = @Id";

            var rowsAffected = await connection.ExecuteAsync(query, externalAuthInfo);
            return rowsAffected > 0;
        }
    }
}
