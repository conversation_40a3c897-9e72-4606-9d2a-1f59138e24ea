using AutoMapper;
using CollaboratorsGS.Application.DTOs.CollaboratorKpi;
using CollaboratorsGS.Application.Interfaces;
using CollaboratorsGS.Domain.Entities;
using CollaboratorsGS.Domain.Repositories;

namespace CollaboratorsGS.Infrastructure.Services
{
    public class CollaboratorKpiService : ICollaboratorKpiService
    {
        private readonly ICollaboratorKpiRepository _kpiRepository;
        private readonly ICollaboratorRepository _collaboratorRepository;
        private readonly IMapper _mapper;

        public CollaboratorKpiService(
            ICollaboratorKpiRepository kpiRepository,
            ICollaboratorRepository collaboratorRepository,
            IMapper mapper)
        {
            _kpiRepository = kpiRepository;
            _collaboratorRepository = collaboratorRepository;
            _mapper = mapper;
        }

        public async Task<IEnumerable<CollaboratorKpiDto>> GetAllAsync()
        {
            var kpis = await _kpiRepository.GetAllAsync();
            return _mapper.Map<IEnumerable<CollaboratorKpiDto>>(kpis);
        }

        public async Task<CollaboratorKpiDto?> GetByIdAsync(Guid kpiId)
        {
            var kpi = await _kpiRepository.GetByIdAsync(kpiId);
            return kpi != null ? _mapper.Map<CollaboratorKpiDto>(kpi) : null;
        }

        public async Task<IEnumerable<CollaboratorKpiDto>> GetByCollaboratorIdAsync(Guid CollaboratorId)
        {
            // Check if collaborator exists
            var collaborator = await _collaboratorRepository.GetByIdAsync(CollaboratorId);
            if (collaborator == null)
                throw new InvalidOperationException($"Collaborator with ID {CollaboratorId} not found");

            var kpis = await _kpiRepository.GetByCollaboratorIdAsync(CollaboratorId);
            return _mapper.Map<IEnumerable<CollaboratorKpiDto>>(kpis);
        }

        public async Task<IEnumerable<CollaboratorKpiDto>> GetByPeriodAsync(string period)
        {
            var kpis = await _kpiRepository.GetByPeriodAsync(period);
            return _mapper.Map<IEnumerable<CollaboratorKpiDto>>(kpis);
        }

        public async Task<Guid> CreateKpiAsync(CreateCollaboratorKpiRequest request)
        {
            // Check if collaborator exists
            var collaborator = await _collaboratorRepository.GetByIdAsync(request.CollaboratorId);
            if (collaborator == null)
                throw new InvalidOperationException($"Collaborator with ID {request.CollaboratorId} not found");

            var kpi = _mapper.Map<CollaboratorKpi>(request);
            
            // Calculate success rate if not provided
            if (!request.SuccessRate.HasValue && request.TotalCandidatesImported > 0)
            {
                kpi.SuccessRate = (float)request.TotalCandidatesOnboarded / request.TotalCandidatesImported * 100;
            }
            
            kpi.CalculatedAt = DateTime.UtcNow;
            
            return await _kpiRepository.CreateAsync(kpi);
        }

        public async Task<bool> UpdateKpiAsync(UpdateCollaboratorKpiRequest request)
        {
            var existingKpi = await _kpiRepository.GetByIdAsync(request.KpiId);
            if (existingKpi == null)
                throw new InvalidOperationException($"KPI with ID {request.KpiId} not found");

            // Update only the fields that are provided
            if (request.TotalCandidatesImported.HasValue)
                existingKpi.TotalCandidatesImported = request.TotalCandidatesImported.Value;
            
            if (request.TotalCandidatesPassedRound1.HasValue)
                existingKpi.TotalCandidatesPassedRound1 = request.TotalCandidatesPassedRound1.Value;
            
            if (request.TotalCandidatesPassedRound2.HasValue)
                existingKpi.TotalCandidatesPassedRound2 = request.TotalCandidatesPassedRound2.Value;
            
            if (request.TotalCandidatesOnboarded.HasValue)
                existingKpi.TotalCandidatesOnboarded = request.TotalCandidatesOnboarded.Value;
            
            if (request.TotalCandidatesFailed.HasValue)
                existingKpi.TotalCandidatesFailed = request.TotalCandidatesFailed.Value;
            
            if (request.TotalCandidatesOnboardedWarranty.HasValue)
                existingKpi.TotalCandidatesOnboardedWarranty = request.TotalCandidatesOnboardedWarranty.Value;
            
            // Calculate success rate if not provided
            if (request.SuccessRate.HasValue)
                existingKpi.SuccessRate = request.SuccessRate;
            else if (existingKpi.TotalCandidatesImported > 0)
                existingKpi.SuccessRate = (float)existingKpi.TotalCandidatesOnboarded / existingKpi.TotalCandidatesImported * 100;
            
            existingKpi.CalculatedAt = DateTime.UtcNow;

            return await _kpiRepository.UpdateAsync(existingKpi);
        }

        public async Task<bool> DeleteKpiAsync(Guid kpiId)
        {
            var existingKpi = await _kpiRepository.GetByIdAsync(kpiId);
            if (existingKpi == null)
                throw new InvalidOperationException($"KPI with ID {kpiId} not found");

            return await _kpiRepository.DeleteAsync(kpiId);
        }

        public Task<Guid> CreateCollaboratorKpiAsync(CreateCollaboratorKpiRequest request)
        {
            throw new NotImplementedException();
        }

        public Task<CollaboratorKpiDto?> GetCreatedCollaboratorKpiAsync(Guid kpiId)
        {
            throw new NotImplementedException();
        }

        public Task<CollaboratorKpiDto?> UpdateCollaboratorKpiAsync(UpdateCollaboratorKpiRequest request)
        {
            throw new NotImplementedException();
        }

        public Task<bool> DeleteCollaboratorKpiAsync(Guid kpiId)
        {
            throw new NotImplementedException();
        }

        public Task<bool> CalculateKpiAsync(Guid collaboratorId, string period)
        {
            throw new NotImplementedException();
        }

        public async Task<CollaboratorKpiReport> GetKpiSummaryByCollaboratorIdAsync(Guid collaboratorId)
        {
           var kpiReport = await _kpiRepository.GetKpiSummaryByCollaboratorIdAsync(collaboratorId);
           return _mapper.Map<CollaboratorKpiReport>(kpiReport);

        }

        public async Task<CollaboratorKpiDetailDto> GetKpiDetailByUserIdAsync(Guid userId)
        {
            var result = await _kpiRepository.GetKpiDetailByUserIdAsync(userId);
            var data = (dynamic)result;

            var currentPeriod = data.CurrentPeriod;
            var progressDetails = data.ProgressDetails as IEnumerable<dynamic>;
            var kpiHistory = data.KpiHistory as IEnumerable<dynamic>;

            return new CollaboratorKpiDetailDto
            {
                CurrentPeriod = new CollaboratorKpiCurrentPeriodDto
                {
                    Period = currentPeriod?.period ?? string.Empty,
                    StartDate = currentPeriod?.start_date ?? DateTime.MinValue,
                    EndDate = currentPeriod?.end_date ?? DateTime.MinValue,
                    DaysRemaining = Convert.ToInt32(currentPeriod?.days_remaining ?? 0),
                    TargetOnboard = Convert.ToInt32(currentPeriod?.target_onboard ?? 0),
                    AchievedOnboard = Convert.ToInt32(currentPeriod?.achieved_onboard ?? 0),
                    SuccessPercentage = Convert.ToDecimal(currentPeriod?.success_percentage ?? 0),
                    Status = currentPeriod?.status ?? "Không đạt",
                    TotalReward = Convert.ToDecimal(currentPeriod?.total_reward ?? 0)
                },
                ProgressDetails = progressDetails?.Select(p => new CollaboratorKpiProgressDto
                {
                    Title = p.title ?? string.Empty,
                    Count = Convert.ToInt32(p.count ?? 0),
                    Target = Convert.ToInt32(p.target ?? 0),
                    Percentage = Convert.ToDecimal(p.percentage ?? 0)
                }).ToList() ?? new List<CollaboratorKpiProgressDto>(),
                KpiHistory = kpiHistory?.Select(h => new CollaboratorKpiHistoryDetailDto
                {
                    Period = h.period ?? string.Empty,
                    TargetOnboard = Convert.ToInt32(h.target_onboard ?? 0),
                    AchievedOnboard = Convert.ToInt32(h.achieved_onboard ?? 0),
                    Status = h.status ?? "Không đạt",
                    TotalReward = Convert.ToDecimal(h.total_reward ?? 0)
                }).ToList() ?? new List<CollaboratorKpiHistoryDetailDto>()
            };
        }
    }
}
  