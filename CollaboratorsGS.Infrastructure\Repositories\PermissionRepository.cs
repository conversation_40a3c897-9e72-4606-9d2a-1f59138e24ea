using CollaboratorsGS.Domain.Entities;
using CollaboratorsGS.Domain.Repositories;
using CollaboratorsGS.Infrastructure.Data;
using Dapper;

namespace CollaboratorsGS.Infrastructure.Repositories
{
    public class PermissionRepository : IPermissionRepository
    {
        private readonly IConnectionFactory _connectionFactory;

        public PermissionRepository(IConnectionFactory connectionFactory)
        {
            _connectionFactory = connectionFactory;
        }

        public async Task<Permission?> GetByIdAsync(Guid permissionId)
        {
            using var connection = _connectionFactory.CreateConnection();

            var query = "SELECT * FROM Permissions WHERE PermissionId = @PermissionId";

            return await connection.QuerySingleOrDefaultAsync<Permission>(query, new { PermissionId = permissionId });
        }

        public async Task<Permission?> GetByNameAsync(string permissionName)
        {
            using var connection = _connectionFactory.CreateConnection();

            var query = "SELECT * FROM Permissions WHERE PermissionName = @PermissionName";

            return await connection.QuerySingleOrDefaultAsync<Permission>(query, new { PermissionName = permissionName });
        }

        public async Task<IEnumerable<Permission>> GetAllAsync()
        {
            using var connection = _connectionFactory.CreateConnection();

            var query = "SELECT * FROM Permissions ORDER BY PermissionName";

            return await connection.QueryAsync<Permission>(query);
        }

        public async Task<Guid> CreateAsync(Permission permission)
        {
            // Generate a new UUID if not provided
            if (permission.PermissionId == Guid.Empty)
            {
                permission.PermissionId = Guid.NewGuid();
            }

            using var connection = _connectionFactory.CreateConnection();

            var query = @"
                INSERT INTO Permissions (PermissionId, PermissionName, Description)
                VALUES (@PermissionId, @PermissionName, @Description)";

            await connection.ExecuteAsync(query, permission);
            return permission.PermissionId;
        }

        public async Task<bool> UpdateAsync(Permission permission)
        {
            using var connection = _connectionFactory.CreateConnection();

            var query = @"
                UPDATE permissions
                SET permission_name = @PermissionName,
                    description = @Description
                WHERE permission_id = @PermissionId";

            var rowsAffected = await connection.ExecuteAsync(query, permission);
            return rowsAffected > 0;
        }

        public async Task<bool> DeleteAsync(Guid permissionId)
        {
            using var connection = _connectionFactory.CreateConnection();

            // First delete from RolePermissions
            var deleteRolePermissionsQuery = "DELETE FROM role_permissions WHERE permission_id = @PermissionId";
            await connection.ExecuteAsync(deleteRolePermissionsQuery, new { PermissionId = permissionId });

            // Then delete the permission
            var deletePermissionQuery = "DELETE FROM permissions WHERE permission_id = @PermissionId";
            var rowsAffected = await connection.ExecuteAsync(deletePermissionQuery, new { PermissionId = permissionId });

            return rowsAffected > 0;
        }
    }
}
