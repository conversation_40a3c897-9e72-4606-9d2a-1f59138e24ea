-- Create Collaborator related tables

-- Create Collaborators table
IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'collaborators' AND schema_id = SCHEMA_ID('dbo'))
BEGIN
CREATE TABLE [dbo].[collaborators] (
    [collaborator_id] UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
    [user_id] UNIQUEIDENTIFIER NOT NULL UNIQUE,
    [full_name] NVARCHAR(255) NOT NULL,
    [phone_number] NVARCHAR(50) UNIQUE NOT NULL,
    [email] NVARCHAR(255) UNIQUE NOT NULL,
    [level_id] UNIQUEIDENTIFIER NOT NULL,
    [status] NVARCHAR(50) NOT NULL,
    [last_level_updated_at] DATETIME,
    [created_at] DATETIME NOT NULL DEFAULT GETDATE(),
    [approved_by] UNIQUEIDENTIFIER,
    [approved_date] DATE,
    [updated_at] DATETIME,
    CONSTRAINT [FK_Collaborators_User] FOREIGN KEY ([user_id]) REFERENCES [dbo].[users] ([user_id]),
    CONSTRAINT [FK_Collaborators_Level] FOREIGN KEY ([level_id]) REFERENCES [dbo].[collaborator_levels] ([level_id]),
    CONSTRAINT [FK_Collaborators_ApprovedBy] FOREIGN KEY ([approved_by]) REFERENCES [dbo].[users] ([user_id])
);
END
GO

-- Create CollaboratorProfiles table
IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'collaborator_profiles' AND schema_id = SCHEMA_ID('dbo'))
BEGIN
CREATE TABLE [dbo].[collaborator_profiles] (
    [profile_id] UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
    [collaborator_id] UNIQUEIDENTIFIER NOT NULL UNIQUE,
    [citizen_id] NVARCHAR(50),
    [citizen_id_front] NVARCHAR(255),
    [citizen_id_back] NVARCHAR(255),
    [permanent_address] NVARCHAR(MAX),
    [current_address] NVARCHAR(MAX),
    [bank_name] NVARCHAR(255),
    [bank_account_number] NVARCHAR(50),
    [created_at] DATETIME NOT NULL DEFAULT GETDATE(),
    [updated_at] DATETIME,
    CONSTRAINT [FK_CollaboratorProfiles_Collaborators] FOREIGN KEY ([collaborator_id]) REFERENCES [dbo].[collaborators] ([collaborator_id])
);
END
GO

-- Create Contracts table
IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'contracts' AND schema_id = SCHEMA_ID('dbo'))
BEGIN
CREATE TABLE [dbo].[contracts] (
    [contract_id] UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
    [collaborator_id] UNIQUEIDENTIFIER NOT NULL,
    [contract_content] NVARCHAR(MAX) NOT NULL,
    [status] NVARCHAR(50) NOT NULL,
    [signed_at] DATETIME,
    [created_at] DATETIME NOT NULL DEFAULT GETDATE(),
    [updated_at] DATETIME,
    CONSTRAINT [FK_Contracts_Collaborators] FOREIGN KEY ([collaborator_id]) REFERENCES [dbo].[collaborators] ([collaborator_id])
);
END
GO

-- Create CollaboratorViolations table
IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'collaborator_violations' AND schema_id = SCHEMA_ID('dbo'))
BEGIN
CREATE TABLE [dbo].[collaborator_violations] (
    [violation_id] UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
    [collaborator_id] UNIQUEIDENTIFIER NOT NULL,
    [violation_type] NVARCHAR(50) NOT NULL,
    [description] NVARCHAR(MAX),
    [created_at] DATETIME NOT NULL DEFAULT GETDATE(),
    [handled_by] UNIQUEIDENTIFIER,
    [handled_at] DATETIME,
    CONSTRAINT [FK_CollaboratorViolations_Collaborators] FOREIGN KEY ([collaborator_id]) REFERENCES [dbo].[collaborators] ([collaborator_id]),
    CONSTRAINT [FK_CollaboratorViolations_HandledBy] FOREIGN KEY ([handled_by]) REFERENCES [dbo].[Users] ([user_id])
);
END
GO
