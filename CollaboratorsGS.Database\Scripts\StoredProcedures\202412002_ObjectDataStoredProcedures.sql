-- Stored Procedures for object_data operations

-- Get object_data by ID
IF EXISTS (SELECT * FROM sys.objects WHERE type = 'P' AND name = 'sp_GetObjectDataById')
    DROP PROCEDURE sp_GetObjectDataById
GO

CREATE PROCEDURE sp_GetObjectDataById
    @object_id UNIQUEIDENTIFIER
AS
BEGIN
    SELECT
        object_id,
        object_type,
        object_code,
        object_value,
        description,
        created_at,
        updated_at
    FROM object_data
    WHERE object_id = @object_id
END
GO

-- Get All object_data
IF EXISTS (SELECT * FROM sys.objects WHERE type = 'P' AND name = 'sp_GetAllObjectData')
    DROP PROCEDURE sp_GetAllObjectData
GO

CREATE PROCEDURE sp_GetAllObjectData
AS
BEGIN
    SELECT 
        object_id,
        object_type,
        object_code,
        object_value,
        description,
        created_at,
        updated_at
    FROM object_data
    ORDER BY object_type, object_value
END
GO

-- Get object_data by Type
IF EXISTS (SELECT * FROM sys.objects WHERE type = 'P' AND name = 'sp_GetObjectDataByType')
    DROP PROCEDURE sp_GetObjectDataByType
GO

CREATE PROCEDURE sp_GetObjectDataByType
    @object_type NVARCHAR(100)
AS
BEGIN
    SELECT 
        object_id,
        object_type,
        object_code,
        object_value,
        description,
        created_at,
        updated_at
    FROM object_data
    WHERE object_type = @object_type
    ORDER BY object_value
END
GO

-- Get object_data by Code
IF EXISTS (SELECT * FROM sys.objects WHERE type = 'P' AND name = 'sp_GetObjectDataByCode')
    DROP PROCEDURE sp_GetObjectDataByCode
GO

CREATE PROCEDURE sp_GetObjectDataByCode
    @object_code NVARCHAR(100)
AS
BEGIN
    SELECT 
        object_id,
        object_type,
        object_code,
        object_value,
        description,
        created_at,
        updated_at
    FROM object_data
    WHERE object_code = @object_code
END
GO

-- Get object_data by Value
IF EXISTS (SELECT * FROM sys.objects WHERE type = 'P' AND name = 'sp_GetObjectDataByValue')
    DROP PROCEDURE sp_GetObjectDataByValue
GO

CREATE PROCEDURE sp_GetObjectDataByValue
    @object_value NVARCHAR(255)
AS
BEGIN
    SELECT 
        object_id,
        object_type,
        object_code,
        object_value,
        description,
        created_at,
        updated_at
    FROM object_data
    WHERE object_value LIKE '%' + @object_value + '%'
    ORDER BY object_type, object_value
END
GO

-- Create object_data
IF EXISTS (SELECT * FROM sys.objects WHERE type = 'P' AND name = 'sp_CreateObjectData')
    DROP PROCEDURE sp_CreateObjectData
GO

CREATE PROCEDURE sp_CreateObjectData
    @object_id UNIQUEIDENTIFIER,
    @object_type NVARCHAR(100),
    @object_code NVARCHAR(100) = NULL,
    @object_value NVARCHAR(255),
    @description NVARCHAR(500) = NULL,
    @created_at DATETIME
AS
BEGIN
    INSERT INTO object_data (
        object_id,
        object_type,
        object_code,
        object_value,
        description,
        created_at
    )
    VALUES (
        @object_id,
        @object_type,
        @object_code,
        @object_value,
        @description,
        @created_at
    )

    -- Return the new ID
    SELECT @object_id as object_id
END
GO

-- Update object_data
IF EXISTS (SELECT * FROM sys.objects WHERE type = 'P' AND name = 'sp_UpdateObjectData')
    DROP PROCEDURE sp_UpdateObjectData
GO

CREATE PROCEDURE sp_UpdateObjectData
    @object_id UNIQUEIDENTIFIER,
    @object_type NVARCHAR(100),
    @object_code NVARCHAR(100) = NULL,
    @object_value NVARCHAR(255),
    @description NVARCHAR(500) = NULL,
    @updated_at DATETIME
AS
BEGIN
    UPDATE object_data
    SET
        object_type = @object_type,
        object_code = @object_code,
        object_value = @object_value,
        description = @description,
        updated_at = @updated_at
    WHERE object_id = @object_id
END
GO

-- Delete object_data
IF EXISTS (SELECT * FROM sys.objects WHERE type = 'P' AND name = 'sp_DeleteObjectData')
    DROP PROCEDURE sp_DeleteObjectData
GO

CREATE PROCEDURE sp_DeleteObjectData
    @object_id UNIQUEIDENTIFIER
AS
BEGIN
    DELETE FROM object_data
    WHERE object_id = @object_id
END
GO
