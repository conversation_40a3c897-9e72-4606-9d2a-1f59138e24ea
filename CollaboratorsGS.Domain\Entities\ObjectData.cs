namespace CollaboratorsGS.Domain.Entities
{
    public class ObjectData
    {
        public Guid ObjectId { get; set; } // Changed from INT to GUID
        public string ObjectType { get; set; } = string.Empty;
        public string? ObjectCode { get; set; }
        public string ObjectValue { get; set; } = string.Empty;
        public string? Description { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime? UpdatedAt { get; set; }
    }
}
