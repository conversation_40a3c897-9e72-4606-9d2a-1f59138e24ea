using CollaboratorsGS.Application.Interfaces;
using CollaboratorsGS.Domain.Entities;
using CollaboratorsGS.Domain.Repositories;
using CollaboratorsGS.Infrastructure.Data;
using Dapper;

namespace CollaboratorsGS.Infrastructure.Repositories
{
    public class UserLogsRepository : IUserLogs
    {
        private readonly IConnectionFactory _connectionFactory;

        public UserLogsRepository(IConnectionFactory connectionFactory)
        {
            _connectionFactory = connectionFactory;
        }

       
    }
}
