using CollaboratorsGS.Domain.Entities;

namespace CollaboratorsGS.Domain.Repositories
{
    public interface ICollaboratorLevelRepository
    {
        Task<CollaboratorLevel?> GetByIdAsync(Guid levelId);
        Task<CollaboratorLevel?> GetByNameAsync(string name);
        Task<IEnumerable<CollaboratorLevel>> GetAllAsync();
        Task<Guid> CreateAsync(CollaboratorLevel level);
        Task<bool> UpdateAsync(CollaboratorLevel level);
        Task<bool> DeleteAsync(Guid levelId);
    }
}
