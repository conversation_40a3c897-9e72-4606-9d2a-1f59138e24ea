using AutoMapper;
using CollaboratorsGS.Application.DTOs.Contract;
using CollaboratorsGS.Application.Interfaces;
using CollaboratorsGS.Domain.Entities;
using CollaboratorsGS.Domain.Repositories;

namespace CollaboratorsGS.Infrastructure.Services
{
    public class ContractService : IContractService
    {
        private readonly IContractRepository _contractRepository;
        private readonly ICollaboratorRepository _collaboratorRepository;
        private readonly IMapper _mapper;

        public ContractService(
            IContractRepository contractRepository,
            ICollaboratorRepository collaboratorRepository,
            IMapper mapper)
        {
            _contractRepository = contractRepository;
            _collaboratorRepository = collaboratorRepository;
            _mapper = mapper;
        }

        public async Task<IEnumerable<ContractDto>> GetAllAsync()
        {
            var contracts = await _contractRepository.GetAllAsync();
            return _mapper.Map<IEnumerable<ContractDto>>(contracts);
        }

        public async Task<ContractDto?> GetByIdAsync(Guid contractId)
        {
            var contract = await _contractRepository.GetByIdAsync(contractId);
            return contract != null ? _mapper.Map<ContractDto>(contract) : null;
        }

        public async Task<IEnumerable<ContractDto>> GetByCollaboratorIdAsync(Guid collaboratorId)
        {
            // Check if collaborator exists
            var collaborator = await _collaboratorRepository.GetByIdAsync(collaboratorId);
            if (collaborator == null)
                throw new InvalidOperationException($"Collaborator with ID {collaboratorId} not found");

            var contracts = await _contractRepository.GetByCollaboratorIdAsync(collaboratorId);
            return _mapper.Map<IEnumerable<ContractDto>>(contracts);
        }

        public async Task<IEnumerable<ContractDto>> GetByStatusAsync(string status)
        {
            var contracts = await _contractRepository.GetByStatusAsync(status);
            return _mapper.Map<IEnumerable<ContractDto>>(contracts);
        }

        public async Task<Guid> CreateContractAsync(CreateContractRequest request)
        {
            // Check if collaborator exists
            var collaborator = await _collaboratorRepository.GetByIdAsync(request.CtvId);
            if (collaborator == null)
                throw new InvalidOperationException($"Collaborator with ID {request.CtvId} not found");

            var contract = _mapper.Map<Contract>(request);
            contract.CreatedAt = DateTime.UtcNow;
            
            return await _contractRepository.CreateAsync(contract);
        }

        public async Task<bool> UpdateContractAsync(UpdateContractRequest request)
        {
            var existingContract = await _contractRepository.GetByIdAsync(request.ContractId);
            if (existingContract == null)
                throw new InvalidOperationException($"Contract with ID {request.ContractId} not found");

            // Update only the fields that are provided
            if (!string.IsNullOrEmpty(request.ContractContent))
                existingContract.ContractContent = request.ContractContent;
            
            if (!string.IsNullOrEmpty(request.Status))
                existingContract.Status = request.Status;
            
            if (request.SignedAt.HasValue)
                existingContract.SignedAt = request.SignedAt;

            existingContract.UpdatedAt = DateTime.UtcNow;

            return await _contractRepository.UpdateAsync(existingContract);
        }

        public async Task<bool> DeleteContractAsync(Guid contractId)
        {
            var existingContract = await _contractRepository.GetByIdAsync(contractId);
            if (existingContract == null)
                throw new InvalidOperationException($"Contract with ID {contractId} not found");

            return await _contractRepository.DeleteAsync(contractId);
        }
    }
}
