    -- Create System, Integration and Security related tables

-- Create AuditLogs table
IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'audit_logs' AND schema_id = SCHEMA_ID('dbo'))
BEGIN
CREATE TABLE [dbo].[audit_logs] (
    [log_id] UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
    [user_id] UNIQUEIDENTIFIER NOT NULL,
    [action] VARCHAR(50) NOT NULL,
    [entity_type] NVARCHAR(50) NOT NULL,
    [entity_id] UNIQUEIDENTIFIER NOT NULL,
    [old_value] NVARCHAR(MAX),
    [new_value] NVARCHAR(MAX),
    [action_date] DATETIME NOT NULL DEFAULT GETDATE(),
    [ip_address] NVARCHAR(50),
    [status] NVARCHAR(50) NOT NULL,
    CONSTRAINT [fk_audit_logs_users] FOREIGN KEY ([user_id]) REFERENCES [dbo].[users] ([user_id])
);
END
GO


-- Create IntegrationLogs table
IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'integration_logs' AND schema_id = SCHEMA_ID('dbo'))
BEGIN
CREATE TABLE [dbo].[integration_logs] (
    [log_id] UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
    [integration_type] VARCHAR(50) NOT NULL,
    [request_data] NVARCHAR(MAX),
    [response_data] NVARCHAR(MAX),
    [status] NVARCHAR(50) NOT NULL,
    [created_at] DATETIME NOT NULL DEFAULT GETDATE()
);
END
GO

-- Create DataEncryptionLogs table
IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'data_encryption_logs' AND schema_id = SCHEMA_ID('dbo'))
BEGIN
CREATE TABLE [dbo].[data_encryption_logs] (
    [log_id] UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
    [entity_type] NVARCHAR(50) NOT NULL,
    [entity_id] UNIQUEIDENTIFIER NOT NULL,
    [field_name] NVARCHAR(255) NOT NULL,
    [encryption_status] NVARCHAR(50) NOT NULL,
    [encrypted_at] DATETIME NOT NULL DEFAULT GETDATE()
);
END
GO

-- Create ApiIntegrations table
IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'api_integrations' AND schema_id = SCHEMA_ID('dbo'))
BEGIN
CREATE TABLE [dbo].[api_integrations] (
    [integration_id] UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
    [integration_name] NVARCHAR(255) NOT NULL,
    [api_key] NVARCHAR(255) NOT NULL,
    [endpoint_url] NVARCHAR(255) NOT NULL,
    [status] NVARCHAR(50) NOT NULL,
    [created_at] DATETIME NOT NULL DEFAULT GETDATE(),
    [updated_at] DATETIME
);
END
GO

-- Create Settings table
IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'settings' AND schema_id = SCHEMA_ID('dbo'))
BEGIN
CREATE TABLE [dbo].[settings] (
    [setting_id] UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
    [setting_key] NVARCHAR(255) NOT NULL UNIQUE,
    [setting_value] NVARCHAR(MAX) NOT NULL,
    [setting_type] NVARCHAR(50) NOT NULL,
    [description] NVARCHAR(255),
    [created_at] DATETIME NOT NULL DEFAULT GETDATE(),
    [updated_at] DATETIME,
    [updated_by] UNIQUEIDENTIFIER,
    CONSTRAINT [fk_settings_updated_by] FOREIGN KEY ([updated_by]) REFERENCES [dbo].[users] ([user_id])
);
END
GO

-- Create SettingLogs table
IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'setting_logs' AND schema_id = SCHEMA_ID('dbo'))
BEGIN
CREATE TABLE [dbo].[setting_logs] (
    [log_id] UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
    [setting_id] UNIQUEIDENTIFIER NOT NULL,
    [old_value] NVARCHAR(MAX),
    [new_value] NVARCHAR(MAX),
    [changed_by] UNIQUEIDENTIFIER NOT NULL,
    [changed_at] DATETIME NOT NULL DEFAULT GETDATE(),
    CONSTRAINT [fk_setting_logs_setting] FOREIGN KEY ([setting_id]) REFERENCES [dbo].[settings] ([setting_id]),
    CONSTRAINT [fk_setting_logs_changed_by] FOREIGN KEY ([changed_by]) REFERENCES [dbo].[users] ([user_id])
);
END
GO