using CollaboratorsGS.Application.DTOs;

namespace CollaboratorsGS.Application.Interfaces
{
    public interface INotificationService
    {
        Task<bool> SendNotificationAsync(NotificationRequest request);
        Task<bool> SaveDeviceTokenAsync(DeviceTokenRequest request);
        Task<bool> RemoveDeviceTokenAsync(string token);
        Task<List<string>> GetUserDeviceTokensAsync(string userId);
        Task<bool> SubscribeToTopicAsync(string token, string topic);
        Task<bool> UnsubscribeFromTopicAsync(string token, string topic);
    }
}
