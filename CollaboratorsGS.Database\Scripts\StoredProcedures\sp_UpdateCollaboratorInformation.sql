-- Stored procedure update profile information 
-- must update fields nessary: current_address, bank_name, bank_account_number, account_bank_name

IF OBJECT_ID('sp_UpdateCollaboratorInformation', 'P') IS NOT NULL
    DROP PROCEDURE sp_UpdateCollaboratorInformation;
GO

CREATE PROCEDURE sp_UpdateCollaboratorInformation
    @CollaboratorId UNIQUEIDENTIFIER,
    @CurrentAddress NVARCHAR(MAX) = NULL,
    @BankName NVARCHAR(255) = NULL,
    @BankAccountNumber NVARCHAR(100) = NULL,
    @BankAccountName NVARCHAR(255) = NULL
AS
BEGIN
    SET NOCOUNT ON;
    
    DECLARE @RowsAffected INT = 0;
    DECLARE @ProfileExisted BIT = 0;
    
    BEGIN TRY
        -- check if collaborator exists
        -- IF NOT EXISTS (SELECT 1 FROM collaborators WHERE collaborator_id = @CollaboratorId)
        -- BEGIN
        --     RAISERROR('Collaborator not found with ID: %s', 16, 1, CAST(@CollaboratorId AS NVARCHAR(36)));
        --     RETURN;
        -- END
        
        -- check if profile exists
        IF EXISTS (SELECT 1 FROM collaborator_profiles WHERE collaborator_id = @CollaboratorId)
        BEGIN
            SET @ProfileExisted = 1;
            
            -- UPDATE existing profile
            UPDATE collaborator_profiles 
            SET 
                current_address = COALESCE(@CurrentAddress, current_address),
                bank_name = COALESCE(@BankName, bank_name),
                bank_account_number = COALESCE(@BankAccountNumber, bank_account_number),
                bank_account_name = COALESCE(@BankAccountName, bank_account_name),
                data_source = 'manual',
                updated_at = GETDATE()
            WHERE collaborator_id = @CollaboratorId;
            
            SET @RowsAffected = @@ROWCOUNT;
        END
        ELSE
        BEGIN
            SET @ProfileExisted = 0;
            
            -- CREATE new profile - with fields that are not null
            INSERT INTO collaborator_profiles (
                collaborator_id,
                current_address,
                bank_name,
                bank_account_number,
                bank_account_name,
                data_source,
                created_at
            )
            VALUES (
                @CollaboratorId,
                @CurrentAddress,
                @BankName,
                @BankAccountNumber,
                @BankAccountName,
                'manual',
                GETDATE()
            );
            
            SET @RowsAffected = @@ROWCOUNT;
        END
        
        -- Return result
        SELECT 
            @RowsAffected as RowsAffected,
            @ProfileExisted as ProfileExisted;
            
    END TRY
    BEGIN CATCH
        DECLARE @ErrorMessage NVARCHAR(4000) = ERROR_MESSAGE();
        DECLARE @ErrorSeverity INT = ERROR_SEVERITY();
        DECLARE @ErrorState INT = ERROR_STATE();
        
        RAISERROR(@ErrorMessage, @ErrorSeverity, @ErrorState);
    END CATCH
END;
GO

