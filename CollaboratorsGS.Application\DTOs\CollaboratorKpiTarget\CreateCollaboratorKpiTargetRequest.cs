using System;
using System.ComponentModel.DataAnnotations;

namespace CollaboratorsGS.Application.DTOs.CollaboratorKpiTarget
{
    public class CreateCollaboratorKpiTargetRequest
    {
        [Required]
        public Guid CollaboratorId { get; set; }
        
        [Required]
        [RegularExpression(@"^\d{4}-\d{2}$", ErrorMessage = "Period must be in format YYYY-MM")]
        public string Period { get; set; } = string.Empty;
        
        [Range(0, int.MaxValue, ErrorMessage = "Target candidates imported must be non-negative")]
        public int TargetCandidatesImported { get; set; } = 0;
        
        [Range(0, int.MaxValue, ErrorMessage = "Target candidates passed round 1 must be non-negative")]
        public int TargetCandidatesPassedRound1 { get; set; } = 0;
        
        [Range(0, int.MaxValue, ErrorMessage = "Target candidates onboarded must be non-negative")]
        public int TargetCandidatesOnboarded { get; set; } = 0;
    }
}
