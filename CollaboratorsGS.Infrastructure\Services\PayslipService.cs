using AutoMapper;
using CollaboratorsGS.Application.Interfaces;
using CollaboratorsGS.Domain.Entities;
using CollaboratorsGS.Domain.Repositories;
using iTextSharp.text.pdf;
using iTextSharp.text;
// using DevExpress.Spreadsheet;
// using DevExpress.XtraPrinting;
using Minio.Helper;
using Newtonsoft.Json;
using OfficeOpenXml;
using OfficeOpenXml.Style;
using System.Data;
using System.Drawing;
using System.Globalization;
using System.Reflection.Metadata;
using Minio;
using System.Reflection.Metadata.Ecma335;
using Minio.DataModel.Args;
using Microsoft.AspNetCore.Hosting;
using CollaboratorsGS.Infrastructure.Utilities;

namespace CollaboratorsGS.Infrastructure.Services
{
    public class PayslipService : IPayslipService
    {
        private readonly IPayslipRepository _payslipRepository;
        private readonly IMapper _mapper;
        private readonly string folder = "Files/Export/payslip";
        private readonly string template_v2 = "wwwroot/template/payslip_02.xlsx";
        private readonly string template_v1 = "wwwroot/template/payslip_01.xlsx";

        private readonly IWebHostEnvironment _env;

        public PayslipService(
            IPayslipRepository payslipRepository,
            IMapper mapper,
            IWebHostEnvironment env)
        {
            _payslipRepository = payslipRepository;
            _mapper = mapper;
            _env = env;
        }
        public async Task<Payslip> GetPayslipAsync(int Month, int Year, int user_id, int approveview, int payroll_id)
        {
            var item = await _payslipRepository.GetPayslipAsync(Month, Year, user_id, approveview, payroll_id);
            return item;
        }
        public async Task<string> GetPayslipUrlAsync(int version, string benefitName, string benefitPhone, int Month, int Year, int user_id, int approveview, int payroll_id)
        {
            var item = await _payslipRepository.GetPayslipAsync(Month, Year, user_id, approveview, payroll_id);
            if (version == 1)
            {
                return await PrintPayslip_V2Async(item, benefitName, benefitPhone);
            }
            else if (version == 2)
            {
                return await PrintPayslip_V2Async(item, benefitName, benefitPhone);
            }
            return "";
        }
        public string PrintPayslip_V1(Payslip payslip, string benefitName, string benefitPhone)
        {
            string filepdf = "";
            bool inserttimesheet = false;
            DataTable dt = payslip.PayslipConfig;
            if (dt.Rows.Count > 0)
            {
                string template = "payslip_02.xlsx";
                FileInfo file = Utilities.Utilities.GetFileExcel(folder, template_v2, "payslip.xlsx");
                using (var xls = new ExcelPackage(file))
                {
                    ExcelWorksheet sheet = xls.Workbook.Worksheets[0];
                    int rowindex = 1;
                    ///Info
                    if (dt.Rows.Count > 2)
                    {
                        sheet.InsertRow(2, dt.Rows.Count - 2, 1);
                    }
                    foreach (DataRow row in dt.Rows)
                    {
                        sheet.Cells[rowindex, 1].Value = rowindex;
                        sheet.Cells[rowindex, 2].Value = row["headerlevel2"];
                        sheet.Cells[rowindex, 5].Value = row["ValueColumn"];
                        rowindex += 1;
                    }
                    //Bang cong
                    List<PayslipDetail> lst = payslip.TimeSheet;
                    int rowCount = lst.Count;
                    int pageSize = 5;
                    int pageCount = 0;
                    string columnvalue = "";
                    bool isnumber = false;
                    decimal value = 0;

                    if (rowCount <= pageSize) pageCount = 1;
                    {
                        int page = rowCount % 5;
                        if (page == 0) pageCount = rowCount / pageSize; else pageCount = rowCount / pageSize + 1;
                    }
                    rowindex = dt.Rows.Count + 1;
                    sheet.Cells[rowindex, 1].Value = string.Format(@"BẢNG CÔNG THÁNG {0} năm {1}", payslip.month, payslip.year);
                    if (rowCount > 0)
                    {
                        if (2 * pageCount > 12)
                        {
                            sheet.InsertRow(rowindex + 1, 2 * pageCount - 12);
                            inserttimesheet = true;
                        }

                        rowindex += 1;
                        for (int i = 1; i <= pageCount; i++)
                        {
                            int colindex = 3;
                            var group = lst.Skip((i - 1) * pageSize).Take(pageSize);
                            foreach (var item in group)
                            {
                                sheet.Cells[rowindex, colindex].Value = item.headerlevel2;
                                sheet.Cells[rowindex, 2].Value = "Ngày";
                                columnvalue = Utilities.Utilities.ToString(item.ValueColumn);
                                isnumber = decimal.TryParse(columnvalue, out value);

                                if (isnumber) value = decimal.Round(value, 2);
                                if (value > 0)
                                {
                                    sheet.Cells[rowindex + 1, colindex].Value = value;
                                    sheet.Cells[rowindex + 1, colindex].Style.Numberformat.Format = "#,##0.00";
                                }
                                else
                                {
                                    if (columnvalue.Length > 0 && !columnvalue.Equals("0")) sheet.Cells[rowindex + 1, colindex].Value = columnvalue;
                                }

                                sheet.Cells[rowindex + 1, 2].Value = "Số giờ";
                                sheet.Cells[rowindex + 1, 2].Style.Font.UnderLine = true;
                                using (ExcelRange range = sheet.Cells[rowindex, 2, rowindex + 1, 2])
                                {
                                    range.Style.Font.Bold = true;
                                }
                                colindex += 1;
                            }
                            rowindex += 2;
                        }
                    }
                    else
                    {
                        sheet.Row(rowindex).Hidden = false;
                    }
                    //Bang luong
                    DataTable dtsalary = payslip.ConvertToDataTable(payslip.salary);// new PaysplipBL().GetPayslipdetail_V2(month, year, user_id, 1, payroll_id);


                    string startwith = "";


                    int salarycount = dtsalary.Rows.Count;
                    if (inserttimesheet)
                        rowindex += 1;
                    else rowindex += 3;

                    ///Bang luong
                    SafeMergeCells(sheet, rowindex - 1, 1, rowindex - 1, 7);
                    using (ExcelRange range = sheet.Cells[rowindex - 1, 1, rowindex - 1, 7])
                    {
                        range.Value = "CHI TIẾT BẢNG LƯƠNG";
                        range.Style.Font.Bold = true;
                        range.Style.Fill.PatternType = ExcelFillStyle.Solid;
                        range.Style.Fill.BackgroundColor.SetColor(ColorTranslator.FromHtml("#70AD47"));
                    }

                    if (dtsalary.Rows.Count > 0)
                    {
                        if (salarycount > 3)
                        {
                            sheet.InsertRow(rowindex + 1, salarycount - 3, rowindex);
                        }

                        foreach (DataRow dr in dtsalary.Rows)
                        {
                            startwith = Convert.ToString(dr["headerlevel1"]);
                            columnvalue = Convert.ToString(dr["ValueColumn"]);
                            int number;
                            //bool margin = Int32.TryParse(startwith, out number);
                            isnumber = decimal.TryParse(columnvalue, out value);

                            sheet.Cells[rowindex, 1].Value = startwith;
                            sheet.Cells[rowindex, 2].Value = dr["headerlevel2"];
                            if (isnumber) value = decimal.Round(value, 2);

                            using (ExcelRange rang = sheet.Cells[rowindex, 6, rowindex, 7])
                            {
                                if (value > 0) rang.Value = value;
                                rang.Style.Numberformat.Format = "#,##0.00";
                                rang.Merge = true;
                            }
                            rowindex += 1;
                        }
                        int row_end = sheet.Dimension.Rows;
                        SafeMergeCells(sheet, row_end, 1, row_end, 1);
                        using (ExcelRange range = sheet.Cells[row_end, 1, row_end, 1])
                        {
                            range.Style.Fill.PatternType = ExcelFillStyle.Solid;
                            range.Style.Fill.BackgroundColor.SetColor(ColorTranslator.FromHtml("#70AD47"));
                        }
                        SafeMergeCells(sheet, row_end, 2, row_end, 5);
                        using (ExcelRange range = sheet.Cells[row_end, 2, row_end, 5])
                        {
                            range.Style.Fill.PatternType = ExcelFillStyle.Solid;
                            range.Style.Fill.BackgroundColor.SetColor(ColorTranslator.FromHtml("#70AD47"));
                        }
                        SafeMergeCells(sheet, row_end, 6, row_end, 7);
                        using (ExcelRange range = sheet.Cells[row_end, 6, row_end, 7])
                        {
                            range.Value = value;
                            range.Style.Fill.PatternType = ExcelFillStyle.Solid;
                            range.Style.Fill.BackgroundColor.SetColor(ColorTranslator.FromHtml("#70AD47"));
                        }
                    }
                    xls.Save();
                }
                // filepdf = ConverToPdf(file.FullName);
                return file.FullName;
            }
            return "";
        }

        public async Task<string> PrintPayslip_V2Async(Payslip payslip, string benefitName, string benefitPhone)
        {
            string filePdfUrl = "";
            System.Data.DataTable dt = payslip.PayslipConfig;
            if (dt.Rows.Count > 0)
            {

                FileInfo templateFile = Utilities.Utilities.GetFileExcel(folder, template_v2, "payslip_02.xlsx");

                string rootPath = Path.Combine(Directory.GetCurrentDirectory(), "wwwroot", "temp");
                Directory.CreateDirectory(rootPath);

                string tempExcelPath = Path.Combine(rootPath, "temp.xlsx");
                string tempPdfPath = Path.Combine(rootPath, "output.pdf");
                using (var xls = new ExcelPackage(templateFile))
                {
                    ExcelWorksheet sheet = xls.Workbook.Worksheets[0];
                    int rowindex = 2;

                    int rowheaderCount = dt.Rows.Count;
                    int maxrowheader = rowheaderCount % 3 + 1;

                    ///Info
                    if (maxrowheader > 1)
                    {
                        sheet.InsertRow(3, maxrowheader - 1, 2);
                    }

                    int col = 1;
                    foreach (DataRow row in dt.Rows)
                    {
                        sheet.Row(rowindex).Height = 33.5;
                        sheet.Cells[rowindex, col].Value = row["headerlevel2"];
                        sheet.Cells[rowindex, col + 1].Value = row["ValueColumn"];

                        // Safely merge cells - check if already merged first
                        SafeMergeCells(sheet, rowindex, col + 1, rowindex, col + 2);

                        if (col <= 3) { col += 3; continue; } else { col = 1; }

                        rowindex += 1;
                    }


                    List<PayslipDetail> lst = payslip.TimeSheet;

                    int rowCount = lst.Count;
                    int pageSize = 3;
                    int pageCount = 0;
                    string columnvalue = "";
                    bool isnumber = false;
                    decimal value = 0;
                    decimal amount = 0;
                    bool isamount = false;
                    int end_rowtimesheet = 0;

                    int rowdelete = 0;

                    rowindex += 1;
                    int starttimesheet = rowindex;
                    if (rowCount > 0)
                    {
                        if (rowCount <= pageSize) pageCount = 1;
                        {
                            int page = rowCount % 3;
                            if (page == 0) pageCount = rowCount / pageSize; else pageCount = rowCount / pageSize + 1;
                        }
                        sheet.Cells[rowindex, 1].Value = string.Format(@"BẢNG CÔNG THÁNG {0} NĂM {1}", payslip.month, payslip.year);
                        sheet.Cells[1, 1].Value = sheet.Cells[1, 1].Text.Replace("{p1}", payslip.month.ToString()).Replace("{p2}", payslip.year.ToString());
                        if (rowCount > 0)
                        {
                            if (pageCount > 3)
                            {
                                sheet.InsertRow(rowindex + 3, pageCount - 3, rowindex + 2);
                            }

                            rowindex += 2;

                            end_rowtimesheet = rowindex + pageCount - 1;

                            pageSize = pageCount;

                            int index = rowindex;

                            int colindex = 1;
                            for (int i = 1; i <= pageCount; i++)
                            {
                                var group = lst.Skip((i - 1) * pageSize).Take(pageSize);
                                index = rowindex;
                                foreach (var item in group)
                                {
                                    sheet.Cells[index, colindex].Value = item.headerlevel2;

                                    DateTime date;
                                    if (DateTime.TryParseExact(string.Format("{0}/{1}", item.headerlevel2, payslip.year), "dd/MM/yyyy", null, DateTimeStyles.None, out date))
                                    {
                                        if (date.DayOfWeek == DayOfWeek.Sunday)
                                        {
                                            using (var range = sheet.Cells[index, colindex])
                                            {
                                                range.Style.Fill.PatternType = ExcelFillStyle.Solid;
                                                range.Style.Fill.BackgroundColor.SetColor(ColorTranslator.FromHtml("#F8CBAD"));
                                            }
                                        }
                                    }

                                    columnvalue = Utilities.Utilities.ToString(item.ValueColumn);
                                    isnumber = decimal.TryParse(
                                    columnvalue,
                                    NumberStyles.Any,
                                    CultureInfo.InvariantCulture,
                                    out value);

                                    if (isnumber) value = decimal.Round(value, 2);
                                    if (value > 0)
                                        sheet.Cells[index, colindex + 1].Value = value;
                                    else
                                    {
                                        if (columnvalue.Length > 0 && !columnvalue.Equals("0"))
                                            sheet.Cells[index, colindex + 1].Value = columnvalue;
                                    }
                                    sheet.Cells[index, 2].Style.Font.UnderLine = true;
                                    using (ExcelRange range = sheet.Cells[rowindex, 2, rowindex + 1, 2])
                                    {
                                        range.Style.Font.Bold = true;
                                    }
                                    index += 1;
                                }
                                colindex += 2;
                            }
                        }

                        rowindex = rowindex + pageCount - 1;
                    }
                    else
                    {
                        rowdelete = starttimesheet + 4;
                        for (int i = starttimesheet; i <= rowdelete; i++)
                        {
                            sheet.Row(i).Hidden = true;
                        }
                        rowindex = rowdelete;
                    }



                    List<Salarydetail> slarylist = payslip.salary;

                    if (slarylist.Count > 0)
                    {
                        var result = slarylist.GroupBy(x => new { x.type })
                                      .Select(g => new
                                      {
                                          type = g.Key.type,
                                          Count = g.Count(),
                                          salary = g.OrderBy(x => x.ColumnIndex)
                                      });

                        int salarycount = 0;
                        foreach (var group in result)
                        {
                            var lstsalary = group.salary.ToList().GroupBy(x => new { x.headerlevel1 }).ToList();
                            int count = lstsalary.Count;
                            if (count > salarycount) salarycount = count;
                        }

                        rowindex += 4;

                        int row_salary = rowindex;

                        if (salarycount > 3)
                        {
                            sheet.InsertRow(row_salary + 1, salarycount - 3, row_salary);
                        }
                        int end_row = row_salary + salarycount - 2;
                        for (int i = row_salary; i <= end_row; i++)
                        {
                            sheet.Row(i).Height = 33.5;
                        }

                        int rowNew_index = rowindex;

                        var salary = result.Where(x => x.type == 2).FirstOrDefault();

                        var group1 = salary.salary.GroupBy(x => new { x.headerlevel1 }).Select(g => new
                        {
                            headerlevel1 = g.Key,
                            Count = g.Count(),
                            salary = g.OrderBy(x => x.ColumnIndex)
                        });
                        sheet.Cells[rowNew_index - 3, 1].Value = string.Format(@"BẢNG CHI TIẾT LƯƠNG THÁNG {0} NĂM {1}", payslip.month, payslip.year);

                        foreach (var item in group1)
                        {
                            var rowdata = item.salary.ToList();

                            if (rowdata.Count > 1)
                            {
                                SafeMergeCells(sheet, rowNew_index, 1, rowNew_index, 2);
                                using (var range = sheet.Cells[rowNew_index, 1, rowNew_index, 2])
                                {
                                    range.Value = rowdata[0].headerlevel2;
                                    range.Style.ShrinkToFit = true;
                                }
                                isnumber = decimal.TryParse(
                                    rowdata[0].ValueColumn,
                                    NumberStyles.Any,
                                    CultureInfo.InvariantCulture,
                                    out value);
                                isnumber = decimal.TryParse(rowdata[0].ValueColumn,out value);

                                if (isnumber)
                                {
                                    if (value > 0)
                                        sheet.Cells[rowNew_index, 3].Value =Utilities.Utilities.ConvertToDecimal(value);
                                }
                                else
                                {
                                    sheet.Cells[rowNew_index, 3].Value = rowdata[0].ValueColumn;
                                }
                                isamount = decimal.TryParse(
                                    rowdata[1].ValueColumn,
                                    NumberStyles.Any,
                                    CultureInfo.InvariantCulture, 
                                    out amount);
                                if (isnumber)
                                {
                                    if (amount > 0)
                                        sheet.Cells[rowNew_index, 5].Value = (double)Math.Truncate(amount);
                                        sheet.Cells[rowNew_index, 5].Style.Numberformat.Format = "#,##0";
                                }
                                else
                                {
                                    sheet.Cells[rowNew_index, 5].Value = rowdata[0].ValueColumn;
                                }
                                if (isamount && isamount && value != 0 && amount > 0)
                                {
                                    sheet.Cells[rowNew_index, 4].Value = Math.Round(amount / value, 2);
                                }
                            }
                            else
                            {
                                SafeMergeCells(sheet, rowNew_index, 1, rowNew_index, 2);
                                using (var range = sheet.Cells[rowNew_index, 1, rowNew_index, 2])
                                {
                                    range.Value = rowdata[0].headerlevel2;
                                    range.Style.ShrinkToFit = true;
                                }
                                
                                isnumber = decimal.TryParse(
                                    rowdata[0].ValueColumn,
                                    NumberStyles.Any,
                                    CultureInfo.InvariantCulture, 
                                    out amount);

                                if (isnumber)
                                {
                                    if (amount > 0 && rowdata[0].configtype.Equals(2))
                                        sheet.Cells[rowNew_index, 3].Value = amount;
                                    if (amount > 0 && rowdata[0].configtype.Equals(4))
                                        sheet.Cells[rowNew_index, 5].Value = (double)Math.Truncate(amount);
                                    sheet.Cells[rowNew_index, 5].Style.Numberformat.Format = "#,##0";

                                }
                                else
                                {
                                    if (rowdata[0].configtype.Equals(2))
                                        sheet.Cells[rowNew_index, 3].Value = rowdata[0].ValueColumn;
                                    if (rowdata[0].configtype.Equals(4))
                                        sheet.Cells[rowNew_index, 5].Value = rowdata[0].ValueColumn;
                                }
                            }
                            rowNew_index += 1;
                        }

                        int end_row1 = rowNew_index;

                        rowNew_index = rowindex;
                        var group2 = result.Where(x => x.type == 3).FirstOrDefault();
                        if (group2 != null)
                        {
                            foreach (var item in group2.salary)
                            {

                                if (item.headerlevel2.ToLower().Equals("các khoản khấu trừ theo quy định pháp luật:") || item.headerlevel2.ToLower().Equals("các khoản khấu trừ theo chính sách công ty:"))
                                {
                                    SafeMergeCells(sheet, rowNew_index, 6, rowNew_index, 8);
                                    using (ExcelRange range = sheet.Cells[rowNew_index, 6, rowNew_index, 8])
                                    {
                                        range.Style.Font.Bold = true;
                                        range.Value = item.headerlevel2;
                                        range.Style.Font.Color.SetColor(ColorTranslator.FromHtml("#FF0000"));
                                    }
                                }
                                else
                                {
                                    SafeMergeCells(sheet, rowNew_index, 6, rowNew_index, 7);
                                    using (var range = sheet.Cells[rowNew_index, 6, rowNew_index, 7])
                                    {
                                        range.Value = item.headerlevel2;
                                        range.Style.ShrinkToFit = true;
                                    }
                                    isamount = decimal.TryParse(item.ValueColumn, out amount);
                                    if (isnumber)
                                    {
                                        if (amount > 0)
                                            sheet.Cells[rowNew_index, 8].Value = amount;
                                    }
                                }
                                rowNew_index += 1;
                            }
                        }
                        for (int i = rowNew_index; i <= end_row1; i++)
                        {
                            SafeMergeCells(sheet, i, 6, i, 7);
                        }

                        int end_row2 = rowNew_index;
                        if (end_row2 > end_row1)
                        {
                            end_row1 = end_row2;
                        }
                        string text = string.Format("{0} {1}", benefitName, benefitPhone);
                        sheet.Cells[end_row1 + 2, 1].Value = sheet.Cells[end_row1 + 2, 1].Text.Replace("{p1}", text);
                    }
                    sheet.Calculate();
                    xls.SaveAs(new FileInfo(tempExcelPath));
                }



               
                try
                {
                    var Convertfile = ConvertExcelToPdfAsync(tempExcelPath);

                    var minio = new MinioClient()
                            .WithEndpoint("**************:9000")
                            .WithCredentials("1qlAB7j3cEE5wjxkf6sG", "J7STecWOLUAI6gQqD05JzkxTt4iX2ubtIYPbuWpL")
                            .Build();
                    var fileName = Guid.NewGuid().ToString() + ".pdf";
                    string bucket = "uploads";
                    string objectName = $"payslip/{fileName}"; 

                    bool exists = minio.BucketExistsAsync(new BucketExistsArgs().WithBucket(bucket)).GetAwaiter().GetResult();
                    if (!exists)
                        minio.MakeBucketAsync(new MakeBucketArgs().WithBucket(bucket)).GetAwaiter().GetResult();

                    using (var stream = File.OpenRead(Convertfile.Result))
                    {
                        minio.PutObjectAsync(new PutObjectArgs()
                            .WithBucket(bucket)
                            .WithObject(objectName)
                            .WithStreamData(stream)
                            .WithObjectSize(stream.Length)
                            .WithContentType("application/pdf"))
                            .GetAwaiter().GetResult();
                        string filePdfUrlMiniO = await minio.PresignedGetObjectAsync(new PresignedGetObjectArgs()

                        .WithBucket(bucket)
                        .WithObject(objectName)
                        .WithExpiry(3600));
                        
                        filePdfUrl = filePdfUrlMiniO;
                    }
                }

                finally
                {
                //     if (File.Exists(tempExcelPath)) File.Delete(tempExcelPath);
                //     if (File.Exists(tempPdfPath)) File.Delete(tempPdfPath);
                //     if (Directory.Exists(rootPath)) Directory.Delete(rootPath, true);
                }

            }
            
            return filePdfUrl;

        }

        public async Task<string> ConvertExcelToPdfAsync(string inputFileName)
        {
            var excelPath = Path.Combine(_env.WebRootPath, "temp", inputFileName);
            var outputFileName = Path.ChangeExtension(inputFileName, ".pdf");
            var outputPath = Path.Combine(_env.WebRootPath, "temp", outputFileName);
            Directory.CreateDirectory(Path.Combine(_env.WebRootPath, "temp"));
            await ExportExcelToPdfExactText(excelPath, outputPath);
            var fileUrl = $"{outputFileName}";
            return fileUrl;
        }

        /// <summary>
        /// Check if a cell range is already merged to avoid merge conflicts
        /// </summary>
        private bool IsCellRangeMerged(ExcelWorksheet sheet, string rangeAddress)
        {
            try
            {
                var targetRange = new ExcelAddress(rangeAddress);
                foreach (var mergedRange in sheet.MergedCells)
                {
                    var existingRange = new ExcelAddress(mergedRange);
                    // Check if ranges overlap
                    if (targetRange.Start.Row <= existingRange.End.Row &&
                        targetRange.End.Row >= existingRange.Start.Row &&
                        targetRange.Start.Column <= existingRange.End.Column &&
                        targetRange.End.Column >= existingRange.Start.Column)
                    {
                        return true;
                    }
                }
                return false;
            }
            catch
            {
                // If there's any error checking, assume it's safe to merge
                return false;
            }
        }

        /// <summary>
        /// Safely merge cells with conflict checking
        /// </summary>
        private void SafeMergeCells(ExcelWorksheet sheet, int startRow, int startCol, int endRow, int endCol)
        {
            try
            {
                var rangeAddress = $"{ExcelCellAddress.GetColumnLetter(startCol)}{startRow}:{ExcelCellAddress.GetColumnLetter(endCol)}{endRow}";
                if (!IsCellRangeMerged(sheet, rangeAddress))
                {
                    using (var range = sheet.Cells[startRow, startCol, endRow, endCol])
                    {
                        range.Merge = true;
                    }
                }
            }
            catch (Exception ex)
            {
                // Log the error but don't throw to prevent breaking the entire process
                System.Diagnostics.Debug.WriteLine($"Error merging cells: {ex.Message}");
            }
        }

        public async Task ExportExcelToPdfExactText(string excelPath, string pdfPath)
        {

            using var package = new ExcelPackage(new FileInfo(excelPath));
            var worksheet = package.Workbook.Worksheets[0];
            var rowCount = worksheet.Dimension.Rows;
            var colCount = worksheet.Dimension.Columns;

            using var stream = new FileStream(pdfPath, FileMode.Create);
            var pdfDoc = new iTextSharp.text.Document(PageSize.A4.Rotate(), 10, 10, 10, 10);
            var writer = PdfWriter.GetInstance(pdfDoc, stream);
            pdfDoc.Open();

            var baseFont = BaseFont.CreateFont("wwwroot/fonts/DejaVuSans.ttf", BaseFont.IDENTITY_H, BaseFont.EMBEDDED);
            var fontDefault = new iTextSharp.text.Font(baseFont, 10, iTextSharp.text.Font.NORMAL);


            var mergedCells = worksheet.MergedCells;
            var alreadyMerged = new HashSet<string>();



            bool lastColEmpty = true;
            for (int row = 1; row <= worksheet.Dimension.Rows; row++)
            {
                if (!string.IsNullOrWhiteSpace(worksheet.Cells[row, colCount].Text))
                {
                    lastColEmpty = false;
                    break;
                }
            }

            if (lastColEmpty)
                colCount--;
            var table = new PdfPTable(colCount) { WidthPercentage = 100 };

            for (int row = 1; row <= rowCount; row++)
            {
                for (int col = 1; col <= colCount; col++)
                {
                    string cellKey = $"{row}:{col}";
                    if (alreadyMerged.Contains(cellKey)) continue;

                    var cell = worksheet.Cells[row, col];
                    var value = cell.Text?.Trim() ?? "";

                    var style = cell.Style;
                    float fontSize = style.Font.Size > 0 ? style.Font.Size : 10;
                    bool isBold = style.Font.Bold;
                    var font = new iTextSharp.text.Font(baseFont, fontSize, isBold ? iTextSharp.text.Font.BOLD : iTextSharp.text.Font.NORMAL);

                    var pdfCell = new PdfPCell(new Phrase(value, font))
                    {
                        HorizontalAlignment = Element.ALIGN_CENTER,
                        VerticalAlignment = Element.ALIGN_MIDDLE,
                        Padding = 5,
                        Border = iTextSharp.text.Rectangle.BOX
                    };

                    if (!string.IsNullOrEmpty(style.Fill.BackgroundColor?.Rgb))
                    {
                        try
                        {
                            var color = System.Drawing.ColorTranslator.FromHtml("#" + style.Fill.BackgroundColor.Rgb);
                            pdfCell.BackgroundColor = new BaseColor(color);
                        }
                        catch { }
                    }


                    foreach (var range in mergedCells)
                    {
                        var addr = new ExcelAddress(range);
                        if (addr.Start.Row == row && addr.Start.Column == col)
                        {
                            pdfCell.Rowspan = addr.End.Row - addr.Start.Row + 1;
                            pdfCell.Colspan = addr.End.Column - addr.Start.Column + 1;

                            for (int r = addr.Start.Row; r <= addr.End.Row; r++)
                                for (int c = addr.Start.Column; c <= addr.End.Column; c++)
                                    alreadyMerged.Add($"{r}:{c}");
                            break;
                        }
                    }

                    table.AddCell(pdfCell);
                }
            }

            pdfDoc.Add(table);
            pdfDoc.Close();
        }


    }
}

