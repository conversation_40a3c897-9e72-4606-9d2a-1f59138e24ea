using CollaboratorsGS.Domain.Entities;
using CollaboratorsGS.Domain.Repositories;
using CollaboratorsGS.Domain.Enums;
using CollaboratorsGS.Infrastructure.Data;
using Dapper;
using System.Data;
using System.Text;

namespace CollaboratorsGS.Infrastructure.Repositories
{
    public class RecruitmentPostingRepository : IRecruitmentPostingRepository
    {
        private readonly IConnectionFactory _connectionFactory;

        public RecruitmentPostingRepository(IConnectionFactory connectionFactory)
        {
            _connectionFactory = connectionFactory;
        }

        public async Task<RecruitmentPosting?> GetByIdAsync(Guid postingId)
        {
            using var connection = _connectionFactory.CreateConnection();

            var parameters = new DynamicParameters();
            parameters.Add("@posting_id", postingId, DbType.Guid);

            return await connection.QuerySingleOrDefaultAsync<RecruitmentPosting>(
                "sp_GetRecruitmentPostingById",
                parameters,
                commandType: CommandType.StoredProcedure);
        }

        public async Task<RecruitmentPosting?> GetByIdWithDetailAsync(Guid postingId)
        {
            using var connection = _connectionFactory.CreateConnection();

            var parameters = new DynamicParameters();
            parameters.Add("@posting_id", postingId, DbType.Guid);

            return await connection.QuerySingleOrDefaultAsync<RecruitmentPosting>(
                "sp_GetRecruitmentPostingByIdWithDetail",
                parameters,
                commandType: CommandType.StoredProcedure);
        }

        public async Task<IEnumerable<RecruitmentPosting>> GetAllAsync()
        {
            using var connection = _connectionFactory.CreateConnection();

            return await connection.QueryAsync<RecruitmentPosting>(
                "sp_GetAllRecruitmentPostings",
                commandType: CommandType.StoredProcedure);
        }

        public async Task<IEnumerable<RecruitmentPosting>> GetByReferCodeAsync(string referCode)
        {
            using var connection = _connectionFactory.CreateConnection();

            var parameters = new DynamicParameters();
            parameters.Add("@refer_code", referCode, DbType.String);

            return await connection.QueryAsync<RecruitmentPosting>(
                "sp_GetRecruitmentPostingsByReferCode",
                parameters,
                commandType: CommandType.StoredProcedure);
        }

        public async Task<(IEnumerable<RecruitmentPosting> data, int totalCount)> SearchAsync(
            string? keyword = null,
            string? level = null,
            string? workingLocation = null,
            int? salaryFrom = null,
            int? salaryTo = null,
            bool? isUrgent = null,
            bool? isHot = null,
            RecruitmentPostingStatus? status = null,
            int page = 1,
            int pageSize = 10,
            string sortBy = "created_at",
            string sortOrder = "desc")
        {
            using var connection = _connectionFactory.CreateConnection();

            var parameters = new DynamicParameters();
            parameters.Add("@keyword", keyword, DbType.String);
            parameters.Add("@level", level, DbType.String);
            parameters.Add("@working_location", workingLocation, DbType.String);
            parameters.Add("@salary_from", salaryFrom, DbType.Int32);
            parameters.Add("@salary_to", salaryTo, DbType.Int32);
            parameters.Add("@is_urgent", isUrgent, DbType.Boolean);
            parameters.Add("@is_hot", isHot, DbType.Boolean);
            parameters.Add("@status", (byte)(status ?? RecruitmentPostingStatus.Active), DbType.Byte); // Default to active
            parameters.Add("@page", page, DbType.Int32);
            parameters.Add("@page_size", pageSize, DbType.Int32);
            parameters.Add("@sort_by", sortBy, DbType.String);
            parameters.Add("@sort_order", sortOrder, DbType.String);
            parameters.Add("@total_count", dbType: DbType.Int32, direction: ParameterDirection.Output);

            var data = await connection.QueryAsync<RecruitmentPosting>(
                "sp_SearchRecruitmentPostings",
                parameters,
                commandType: CommandType.StoredProcedure);

            var totalCount = parameters.Get<int>("@total_count");

            return (data, totalCount);
        }

        public async Task<Guid> CreateAsync(RecruitmentPosting posting)
        {
            if (posting.PostingId == Guid.Empty)
            {
                posting.PostingId = Guid.NewGuid();
            }

            using var connection = _connectionFactory.CreateConnection();

            var parameters = new DynamicParameters();
            parameters.Add("@posting_id", posting.PostingId, DbType.Guid);
            parameters.Add("@refer_code", posting.ReferCode, DbType.String);
            parameters.Add("@title", posting.Title, DbType.String);
            parameters.Add("@project", posting.Project, DbType.String);
            parameters.Add("@level", posting.Level, DbType.String);
            parameters.Add("@position", posting.Position, DbType.String);
            parameters.Add("@salary_from", posting.SalaryFrom, DbType.Int32);
            parameters.Add("@salary_to", posting.SalaryTo, DbType.Int32);
            parameters.Add("@commission", posting.Commission, DbType.Int32);
            parameters.Add("@commission_warranty_months", posting.CommissionWarrantyMonths, DbType.Int32);
            parameters.Add("@working_location", posting.WorkingLocation, DbType.String);
            parameters.Add("@working_time", posting.WorkingTime, DbType.String);
            parameters.Add("@view_count", posting.ViewCount, DbType.Int32);
            parameters.Add("@referral_count", posting.ReferralCount, DbType.Int32);
            parameters.Add("@is_urgent", posting.IsUrgent, DbType.Boolean);
            parameters.Add("@is_hot", posting.IsHot, DbType.Boolean);
            parameters.Add("@is_saved", posting.IsSaved, DbType.Boolean);
            parameters.Add("@status", (byte)posting.Status, DbType.Byte);
            parameters.Add("@job_detail_json", posting.JobDetailJson, DbType.String);
            parameters.Add("@created_at", posting.CreatedAt, DbType.DateTime);
            parameters.Add("@updated_at", posting.UpdatedAt, DbType.DateTime);
            parameters.Add("@expired_at", posting.ExpiredAt, DbType.DateTime);

            await connection.ExecuteAsync(
                "sp_CreateRecruitmentPosting",
                parameters,
                commandType: CommandType.StoredProcedure);

            return posting.PostingId;
        }

        public async Task<bool> UpdateAsync(RecruitmentPosting posting)
        {
            using var connection = _connectionFactory.CreateConnection();

            var parameters = new DynamicParameters();
            parameters.Add("@posting_id", posting.PostingId, DbType.Guid);
            parameters.Add("@refer_code", posting.ReferCode, DbType.String);
            parameters.Add("@title", posting.Title, DbType.String);
            parameters.Add("@project", posting.Project, DbType.String);
            parameters.Add("@level", posting.Level, DbType.String);
            parameters.Add("@position", posting.Position, DbType.String);
            parameters.Add("@salary_from", posting.SalaryFrom, DbType.Int32);
            parameters.Add("@salary_to", posting.SalaryTo, DbType.Int32);
            parameters.Add("@commission", posting.Commission, DbType.Int32);
            parameters.Add("@commission_warranty_months", posting.CommissionWarrantyMonths, DbType.Int32);
            parameters.Add("@working_location", posting.WorkingLocation, DbType.String);
            parameters.Add("@working_time", posting.WorkingTime, DbType.String);
            parameters.Add("@is_urgent", posting.IsUrgent, DbType.Boolean);
            parameters.Add("@is_hot", posting.IsHot, DbType.Boolean);
            parameters.Add("@is_saved", posting.IsSaved, DbType.Boolean);
            parameters.Add("@status", (byte)posting.Status, DbType.Byte);
            parameters.Add("@job_detail_json", posting.JobDetailJson, DbType.String);
            parameters.Add("@updated_at", posting.UpdatedAt, DbType.DateTime);
            parameters.Add("@expired_at", posting.ExpiredAt, DbType.DateTime);

            var rowsAffected = await connection.ExecuteAsync(
                "sp_UpdateRecruitmentPosting",
                parameters,
                commandType: CommandType.StoredProcedure);

            return rowsAffected > 0;
        }

        public async Task<bool> DeleteAsync(Guid postingId)
        {
            using var connection = _connectionFactory.CreateConnection();

            var parameters = new DynamicParameters();
            parameters.Add("@posting_id", postingId, DbType.Guid);

            var rowsAffected = await connection.ExecuteAsync(
                "sp_DeleteRecruitmentPosting",
                parameters,
                commandType: CommandType.StoredProcedure);

            return rowsAffected > 0;
        }

        public async Task<bool> IncrementViewCountAsync(Guid postingId)
        {
            using var connection = _connectionFactory.CreateConnection();

            var parameters = new DynamicParameters();
            parameters.Add("@posting_id", postingId, DbType.Guid);

            var rowsAffected = await connection.ExecuteAsync(
                "sp_IncrementRecruitmentPostingViewCount",
                parameters,
                commandType: CommandType.StoredProcedure);

            return rowsAffected > 0;
        }

        public async Task<bool> IncrementReferralCountAsync(Guid postingId)
        {
            using var connection = _connectionFactory.CreateConnection();

            var parameters = new DynamicParameters();
            parameters.Add("@posting_id", postingId, DbType.Guid);

            var rowsAffected = await connection.ExecuteAsync(
                "sp_IncrementRecruitmentPostingReferralCount",
                parameters,
                commandType: CommandType.StoredProcedure);

            return rowsAffected > 0;
        }
    }
}
