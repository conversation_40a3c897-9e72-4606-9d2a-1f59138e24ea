using System;
using System.ComponentModel.DataAnnotations;

namespace CollaboratorsGS.Application.DTOs.CollaboratorViolation
{
    public class CreateCollaboratorViolationRequest
    {
        [Required]
        public Guid CtvId { get; set; }
        
        [Required]
        public string ViolationType { get; set; } = string.Empty;
        
        public string? Description { get; set; }
        
        public Guid? HandledBy { get; set; }
        
        public DateTime? HandledAt { get; set; }
    }
}
