{"ConnectionStrings": {"DefaultConnection": "Data Source=103.153.73.243\\SQL2017,4022;Initial Catalog=db_collaborators;Integrated Security=false;User ID=sa;Password=@2025#solution;Max Pool Size=2000;TrustServerCertificate=True;", "ECMConnection": "Data Source=27.0.12.242,4022;Initial Catalog=db_ecm;Integrated Security=false;User ID=sa;Password=************;Max Pool Size=2000"}, "Jwt": {"Key": "YourSecretKeyForAuthenticationOfApplication", "Issuer": "CollaboratorsGSAuthenticationServer", "Audience": "CollaboratorsGSServiceClient", "ExpiryInMinutes": 60}, "Authentication": {"Google": {"ClientId": "your-google-client-id", "ClientSecret": "your-google-client-secret"}}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "FileStorage": {"StorageType": "Minio", "LocalBasePath": "uploads", "LocalBaseUrl": "/uploads", "MinioEndpoint": "103.153.73.243:9000", "MinioAccessKey": "1qlAB7j3cEE5wjxkf6sG", "MinioSecretKey": "J7STecWOLUAI6gQqD05JzkxTt4iX2ubtIYPbuWpL", "MinioBucketName": "gstalent", "MinioWithSSL": false}, "Firebase": {"ApiKey": "AIzaSyD_ZKQ9q5UgNERzVLEyQDopkv9i24WkbF0", "AuthDomain": "talentgs-d2818.firebaseapp.com", "ProjectId": "talentgs-d2818", "StorageBucket": "talentgs-d2818.firebasestorage.app", "MessagingSenderId": "429508658970", "AppId": "1:429508658970:web:72933b74bf703e1a500ea3", "MeasurementId": "G-5VM4YLK15K", "VapidKey": ""}, "EmailSettings": {"SmtpHost": "smtp.gmail.com", "SmtpPort": "587", "SmtpUsername": "<EMAIL>", "SmtpPassword": "ihyo qkab rpbj dhlk", "FromEmail": "<EMAIL>", "FromName": "Collaborator System"}, "AppSettings": {"FrontendUrl": "https://ctv.greenspeed.vn"}, "AllowedHosts": "*", "ScannerEndpoint": "https://api-extract.greenspeed.vn"}