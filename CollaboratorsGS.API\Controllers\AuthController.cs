using CollaboratorsGS.Application.DTOs;
using CollaboratorsGS.Application.DTOs.Auth;
using CollaboratorsGS.Application.Interfaces;
using CollaboratorsGS.Infrastructure.Utilities;
using Google.Protobuf.WellKnownTypes;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace CollaboratorsGS.API.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class AuthController : ControllerBase
    {
        private readonly IAuthService _authService;

        public AuthController(IAuthService authService)
        {
            _authService = authService;
        }

        [HttpPost("login")]
        public async Task<IActionResult> Login([FromBody] LoginRequest request)
        {
            var response = await _authService.LoginAsync(request);

            if (!response.is_success)
            {
                return BadRequest(ApiResponse<object>.ErrorResponse(
                    MessageCodes.ER4005,
                    "Login failed",
                    400,
                   new List<ErrorDetail>
                {
                    new ErrorDetail
                    {
                        Field = "username,password",
                        ErrorCode = MessageCodes.ER4005,
                        Message =  response.message ?? string.Empty
                    }
                }));
            }

            return Ok(ApiResponse<object>.SuccessResponse(
                 MessageCodes.SC2004,
                 "Login successfully",
                 response));
        }

        [HttpPost("register")]
        public async Task<IActionResult> Register([FromBody] RegisterRequest request)
        {
            var response = await _authService.RegisterAsync(request);

            var errors = new List<ErrorDetail>();
            var usernameError = BusinessValidationHelper.CheckUniqueness(!response.is_success, "Username", "Username");
            if (usernameError != null) errors.Add(usernameError);
            if (errors.Count > 0)
                return BadRequest(ApiResponse<object>.ErrorResponse(MessageCodes.ER4005,
                "Register failed", 400,
                errors));

            return Ok(ApiResponse<object>.SuccessResponse(
                MessageCodes.SC2000,
                "Register successfully",
                response));
        }

        [HttpPost("refresh-token")]
        public async Task<IActionResult> RefreshToken([FromBody] RefreshTokenRequest request)
        {
            var response = await _authService.RefreshTokenAsync(request);

            if (!response.is_success)
                return BadRequest(ApiResponse<object>.ErrorResponse(
                    MessageCodes.ER4005,
                    "Refresh token failed",400,errors: new List<ErrorDetail>
                {
                    new ErrorDetail
                    {
                        Field = "refresh_token",
                        ErrorCode = MessageCodes.ER4005,
                        Message = response.message ?? "Invalid refresh token"
                    }
                }));

            return Ok(ApiResponse<object>.SuccessResponse(
                MessageCodes.SC2000,
                "Refresh-token successfully",
                response));
        }

        [Authorize]
        [HttpPost("revoke-token")]
        public async Task<IActionResult> RevokeToken([FromBody] RefreshTokenRequest request)
        {
            var result = await _authService.RevokeTokenAsync(request.refresh_token);

            if (!result)
                return BadRequest(ApiResponse<object>.ErrorResponse(
                    MessageCodes.ER4005,
                    "Revoke token failed",400, new List<ErrorDetail>
                {
                    new ErrorDetail
                    {
                        Field = "Refresh_token",
                        ErrorCode = MessageCodes.ER4005,
                        Message =  "Invalid token or already revoked"
                    }
                }));

            return Ok(ApiResponse<object>.SuccessResponse(
                MessageCodes.SC2000,
                "Revoke token successfully",
                200));
        }

        [HttpPost("google-login")]
        public async Task<IActionResult> GoogleLogin([FromBody] ExternalAuthRequest request)
        {
            var response = await _authService.ExternalLoginAsync(request);

            if (!response.is_success)
                return BadRequest(ApiResponse<object>.ErrorResponse(
                    MessageCodes.ER4005,
                    "Google login failed",400, new List<ErrorDetail>
                {
                    new ErrorDetail
                    {
                        Field = "Provider,IdToken",
                        ErrorCode = MessageCodes.ER4005,
                        Message =  "Invalid provider or id token invalid"
                    }
                }));

            return Ok(ApiResponse<object>.SuccessResponse(
                MessageCodes.SC2000,
                "Google login successfully",
                response,200));
        }

        [Authorize]
        [HttpPost("logout")]
        public async Task<IActionResult> Logout([FromBody] LogoutRequest request)
        {
            var result = await _authService.LogoutAsync(request);

            if (!result)
                return BadRequest(
                    ApiResponse<object>.ErrorResponse(
                    MessageCodes.ER4005,
                    "Logout failed", 400, new List<ErrorDetail>
                {
                    new ErrorDetail
                    {
                        Field = "Refresh_token",
                        ErrorCode = MessageCodes.ER4005,
                        Message =  "Invalid token or logout failed"
                    }
                }));

            return Ok(ApiResponse<object>.SuccessResponse(
                MessageCodes.SC2000,
                "Logout successfully",
                200));
        }

        [HttpPost("forgot-password")]
        public async Task<IActionResult> ForgotPassword([FromBody] ForgotPasswordRequest request)
        {
            var response = await _authService.ForgotPasswordAsync(request);
            if (!response.is_success)
                return BadRequest(ApiResponse<object>.ErrorResponse(
                    MessageCodes.ER4005,"Forgot-password failed to sent mail",400,new List<ErrorDetail>
                {
                    new ErrorDetail
                    {
                        Field = "Email",
                        ErrorCode = MessageCodes.ER4005,
                        Message = response.message ?? "Email not found"
                    }
                }));
            return Ok(ApiResponse<object>.SuccessResponse(
                MessageCodes.SC2000,
                response.message ?? "Forgot-password successfully to sent mail",
                response,
                200));
        }

        [HttpPost("reset-password")]
        public async Task<IActionResult> ResetPassword([FromBody] ResetPasswordRequest request)
        {
            var response = await _authService.ResetPasswordAsync(request);

            if (!response.is_success)
                return BadRequest(ApiResponse<object>.ErrorResponse(
                    MessageCodes.ER4005,"Reset-password failed",400,new List<ErrorDetail>
                {
                    new ErrorDetail
                    {
                        Field = "Token,NewPassword",
                        ErrorCode = MessageCodes.ER4005,
                        Message = response.message ?? "Email not found"
                    }
                }));

            return Ok(ApiResponse<object>.SuccessResponse(
                MessageCodes.SC2000,
                response.message ?? "Reset-password successfully",
                response,
                200));
        }
    }
}
