-- Seed admin user with UUID
IF NOT EXISTS (SELECT TOP 1 1 FROM users WHERE username = 'admin')
BEGIN
    PRINT 'Seeding admin user with UUID...';

    -- Get admin role id
    DECLARE @admin_role_id UNIQUEIDENTIFIER;
    SELECT @admin_role_id = role_id FROM roles WHERE role_name = 'admin';

    -- Create admin user with UUID and hashed password (password: Admin@123)
    DECLARE @admin_user_id UNIQUEIDENTIFIER = NEWID();
    DECLARE @admin_password NVARCHAR(100) = '$' + '2a$'+'11$Qn4TveR/8p51zR4ygLxFXeH6OpvTdQxcgiB.WeQomcsh.RpVi6DXq';
    INSERT INTO users (
        user_id,
        username,
        password,
        role_id,
        full_name,
        email,
        phone_number,
        created_at,
        is_active
    )
    VALUES (
        @admin_user_id,
        'admin',
         @admin_password , -- BCrypt hash for "Admin@123"
        @admin_role_id,
        'System Administrator',
        '<EMAIL>',
        NULL,
        GETDATE(),
        1
    );

    PRINT 'Admin user seeded successfully.';
END
ELSE
BEGIN
    PRINT 'Admin user already exists. Skipping...';
END
GO
