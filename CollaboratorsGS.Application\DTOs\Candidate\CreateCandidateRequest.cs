using System.ComponentModel.DataAnnotations;
using CollaboratorsGS.Application.DTOs.Common;
using CollaboratorsGS.Application.Attributes;

namespace CollaboratorsGS.Application.DTOs.Candidate
{
    public class CreateCandidateRequest
    {
        [Required(ErrorMessage = "Full name is required")]
        public string FullName { get; set; } = string.Empty;

        [Required(ErrorMessage = "Phone number is required")]
        public string PhoneNumber { get; set; } = string.Empty;

        public string? Email { get; set; }
        public string? EducationLevel { get; set; }
        public string? WorkExperience { get; set; }
        public string? Skills { get; set; }

        // Accept date as string in dd/MM/yyyy format
        [DateFormat]
        public string? DateOfBirth { get; set; }

        public string? Address { get; set; }
        public string? ProfilePicture { get; set; }
        public string? FullBodyPicture { get; set; }
        public int? HeightCm { get; set; }
        public int? WeightKg { get; set; }
        public string? Level { get; set; }
        public string? Source { get; set; }
        public Guid? CollaboratorId { get; set; }
        public string? CitizenId { get; set; }
        public string? CitizenIdAddress { get; set; }

        // Accept date as string in dd/MM/yyyy format
        [DateFormat]
        public string? CitizenIdIssueDate { get; set; }

        public string? CitizenIdIssuePlace { get; set; }
        public List<CandidateDocumentInfo> Documents { get; set; } = new List<CandidateDocumentInfo>();
    }

    public class CandidateDocumentInfo
    {
        public string FilePath { get; set; } = string.Empty;
        public string DocumentType { get; set; } = string.Empty;
        public string FileType { get; set; } = string.Empty;
    }

    public class CreateCandidateFromCVRequest
    {
        [DateFormat]
        public string? DateOfBirth { get; set; }
        public string? WorkExperience { get; set; }
        public string? Province { get; set; }
        public string? District { get; set; }
        public string? Ward { get; set; }
        public string? Address { get; set; }
        public string? Level { get; set; }
        public string? Source { get; set; }
    }
}
