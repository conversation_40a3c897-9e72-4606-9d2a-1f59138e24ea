using DbUp;
using Microsoft.Extensions.Configuration;

namespace CollaboratorsGS.Database
{
    public class DatabaseUpgrader
    {
        private readonly string _connectionString;
        private readonly string _scriptsPath;

        public DatabaseUpgrader(string connectionString, string scriptsPath)
        {
            _connectionString = connectionString;
            _scriptsPath = scriptsPath;
        }

        public static DatabaseUpgrader CreateFromConfiguration(IConfiguration configuration)
        {
            var connectionString = configuration.GetConnectionString("DefaultConnection")
                ?? throw new InvalidOperationException("Connection string 'DefaultConnection' not found in configuration.");
           var scriptsPath = Path.Combine(AppContext.BaseDirectory, "Scripts");
            return new DatabaseUpgrader(connectionString, scriptsPath);
        }

        public bool PerformUpgrade()
        {
            var upgrader = DeployChanges.To
                .SqlDatabase(_connectionString)
                .WithScriptsFromFileSystem(Path.Combine(_scriptsPath, "Types"))
                .WithScriptsFromFileSystem(Path.Combine(_scriptsPath, "Tables"))
                // .WithScriptsFromFileSystem(Path.Combine(_scriptsPath, "Functions"))
                .WithScriptsFromFileSystem(Path.Combine(_scriptsPath, "StoredProcedures"))
                .WithScriptsFromFileSystem(Path.Combine(_scriptsPath, "Seeds"))
                .LogToConsole()
                .Build();

            var result = upgrader.PerformUpgrade();

            if (!result.Successful)
            {
                Console.ForegroundColor = ConsoleColor.Red;
                Console.WriteLine(result.Error);
                Console.ResetColor();
                return false;
            }

            Console.ForegroundColor = ConsoleColor.Green;
            Console.WriteLine("Success!");
            Console.ResetColor();
            return true;
        }

        public bool PerformMigrationOnly()
        {
            var upgrader = DeployChanges.To
                .SqlDatabase(_connectionString)
                .WithScriptsFromFileSystem(Path.Combine(_scriptsPath, "Types"))
                .WithScriptsFromFileSystem(Path.Combine(_scriptsPath, "Tables"))
                // .WithScriptsFromFileSystem(Path.Combine(_scriptsPath, "Functions"))
                .WithScriptsFromFileSystem(Path.Combine(_scriptsPath, "StoredProcedures"))
                .LogToConsole()
                .Build();

            var result = upgrader.PerformUpgrade();

            if (!result.Successful)
            {
                Console.ForegroundColor = ConsoleColor.Red;
                Console.WriteLine(result.Error);
                Console.ResetColor();
                return false;
            }

            Console.ForegroundColor = ConsoleColor.Green;
            Console.WriteLine("Migration Success!");
            Console.ResetColor();
            return true;
        }

        public bool PerformSeedingOnly()
        {
            var upgrader = DeployChanges.To
                .SqlDatabase(_connectionString)
                .WithScriptsFromFileSystem(Path.Combine(_scriptsPath, "Seeds"))
                .LogToConsole()
                .Build();

            var result = upgrader.PerformUpgrade();

            if (!result.Successful)
            {
                Console.ForegroundColor = ConsoleColor.Red;
                Console.WriteLine(result.Error);
                Console.ResetColor();
                return false;
            }

            Console.ForegroundColor = ConsoleColor.Green;
            Console.WriteLine("Seeding Success!");
            Console.ResetColor();
            return true;
        }
        
    }
}