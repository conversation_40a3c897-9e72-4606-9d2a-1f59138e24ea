using CollaboratorsGS.Domain.Entities;

namespace CollaboratorsGS.Domain.Repositories
{
    public interface ICandidateApplicationRepository
    {
        Task<CandidateApplication?> GetByIdAsync(Guid applicationId);
        Task<IEnumerable<CandidateApplication>> GetAllAsync();
        Task<IEnumerable<CandidateApplication>> GetByCandidateAsync(Guid candidateId);
        Task<IEnumerable<CandidateApplication>> GetByPostingAsync(Guid postingId);
        Task<IEnumerable<CandidateApplication>> GetByStatusAsync(string status);
        Task<Guid> CreateAsync(CandidateApplication application);
        Task<bool> UpdateAsync(CandidateApplication application);
        Task<bool> DeleteAsync(Guid applicationId);
    }
}
