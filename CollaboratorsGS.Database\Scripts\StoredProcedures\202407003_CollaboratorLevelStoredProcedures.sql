-- Stored Procedures for CollaboratorLevel operations

-- Get CollaboratorLevel by ID
IF EXISTS (SELECT * FROM sys.objects WHERE type = 'P' AND name = 'sp_GetCollaboratorLevelById')
    DROP PROCEDURE sp_GetCollaboratorLevelById
GO

CREATE PROCEDURE sp_GetCollaboratorLevelById
    @level_id UNIQUEIDENTIFIER
AS
BEGIN
    SELECT *
    FROM collaborator_levels
    WHERE level_id = @level_id
END
GO


-- Get CollaboratorLevel by Name
IF EXISTS (SELECT * FROM sys.objects WHERE type = 'P' AND name = 'sp_GetCollaboratorLevelByName')
    DROP PROCEDURE sp_GetCollaboratorLevelByName
GO

CREATE PROCEDURE sp_GetCollaboratorLevelByName
    @name NVARCHAR(100)
AS
BEGIN
    SELECT *
    FROM collaborator_levels
    WHERE level_name = @name
END

GO
-- Get All collaborator_levels
IF EXISTS (SELECT * FROM sys.objects WHERE type = 'P' AND name = 'sp_GetAllCollaboratorLevels')
    DROP PROCEDURE sp_GetAllCollaboratorLevels
GO

CREATE PROCEDURE sp_GetAllCollaboratorLevels
AS
BEGIN
    SELECT *
    FROM collaborator_levels
    ORDER BY min_kpi_threshold
END
GO

-- Create CollaboratorLevel
IF EXISTS (SELECT * FROM sys.objects WHERE type = 'P' AND name = 'sp_CreateCollaboratorLevel')
    DROP PROCEDURE sp_CreateCollaboratorLevel
GO

CREATE PROCEDURE sp_CreateCollaboratorLevel
    @level_id UNIQUEIDENTIFIER,
    @level_name VARCHAR(50),
    @min_kpi_threshold FLOAT,
    @commission_rate FLOAT,
    @round1_bonus DECIMAL(18, 2),
    @round2_bonus DECIMAL(18, 2),
    @onboard_bonus DECIMAL(18, 2),
    @description VARCHAR(255)
AS
BEGIN
    INSERT INTO collaborator_levels (
        level_id,
        level_name,
        min_kpi_threshold,
        commission_rate,
        round1_bonus,
        round2_bonus,
        onboard_bonus,
        description
    )
    VALUES (
        @level_id,
        @level_name,
        @min_kpi_threshold,
        @commission_rate,
        @round1_bonus,
        @round2_bonus,
        @onboard_bonus,
        @description
    )

    SELECT 1 -- Return 1 to indicate success
END
GO

-- Update CollaboratorLevel
IF EXISTS (SELECT * FROM sys.objects WHERE type = 'P' AND name = 'sp_UpdateCollaboratorLevel')
    DROP PROCEDURE sp_UpdateCollaboratorLevel
GO

CREATE PROCEDURE sp_UpdateCollaboratorLevel
    @level_id UNIQUEIDENTIFIER,
    @level_name VARCHAR(50),
    @min_kpi_threshold FLOAT,
    @commission_rate FLOAT,
    @round1_bonus DECIMAL(18, 2),
    @round2_bonus DECIMAL(18, 2),
    @onboard_bonus DECIMAL(18, 2),
    @description VARCHAR(255)
AS
BEGIN
    UPDATE collaborator_levels
    SET
        level_name = @level_name,
        min_kpi_threshold = @min_kpi_threshold,
        commission_rate = @commission_rate,
        round1_bonus = @round1_bonus,
        round2_bonus = @round2_bonus,
        onboard_bonus = @onboard_bonus,
        description = @description
    WHERE level_id = @level_id

    SELECT @@ROWCOUNT -- Return the number of rows affected
END
GO

-- Delete CollaboratorLevel
IF EXISTS (SELECT * FROM sys.objects WHERE type = 'P' AND name = 'sp_DeleteCollaboratorLevel')
    DROP PROCEDURE sp_DeleteCollaboratorLevel
GO

CREATE PROCEDURE sp_DeleteCollaboratorLevel
    @level_id UNIQUEIDENTIFIER
AS
BEGIN
    -- Check if the level is being used by any collaborators
    IF EXISTS (SELECT 1 FROM collaborators WHERE level_id = @level_id)
    BEGIN
        -- Return 0 to indicate failure (level is in use)
        SELECT 0
        RETURN
    END

    -- Delete the level
    DELETE FROM collaborator_levels
    WHERE level_id = @level_id

    -- Return the number of rows affected
    SELECT @@ROWCOUNT
END
GO
