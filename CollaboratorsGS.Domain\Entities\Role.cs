using System.ComponentModel.DataAnnotations.Schema;

namespace CollaboratorsGS.Domain.Entities
{
    public class Role
    {
        public Guid RoleId { get; set; }

        public string RoleName { get; set; } = string.Empty;

        public string? Description { get; set; }

        // Navigation properties
        public ICollection<User>? Users { get; set; }
        public ICollection<RolePermission>? RolePermissions { get; set; }
    }
}
