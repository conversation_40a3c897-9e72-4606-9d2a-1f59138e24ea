using CollaboratorsGS.Application.DTOs.Common;

namespace CollaboratorsGS.Application.DTOs.Collaborator
{
    /// <summary>
    /// Example of a search request for Collaborator
    /// </summary>
    public class SearchCollaboratorRequest : PaginationRequest
    {
        public string? keyword { get; set; }
        public string? status { get; set; }
        public string? level { get; set; }
        public string? email { get; set; }
        public string? phone_number { get; set; }
        public DateTime? created_from { get; set; }
        public DateTime? created_to { get; set; }
    }
}
