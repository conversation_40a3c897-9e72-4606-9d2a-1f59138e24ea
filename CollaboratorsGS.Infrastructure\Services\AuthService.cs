
using AutoMapper;
using CollaboratorsGS.Application.DTOs;
using CollaboratorsGS.Application.DTOs.Auth;
using CollaboratorsGS.Application.Interfaces;
using CollaboratorsGS.Domain.Entities;
using CollaboratorsGS.Domain.Repositories;
using BC = BCrypt.Net.BCrypt;

namespace CollaboratorsGS.Infrastructure.Services
{
    public class AuthService : IAuthService
    {
        private readonly IUserRepository _userRepository;
        private readonly IRoleRepository _roleRepository;
        private readonly IAuthRepository _authRepository;
        private readonly IJwtService _jwtService;
        private readonly IExternalAuthService _externalAuthService;
        private readonly IMapper _mapper;
        private readonly ICollaboratorRepository _collaboratorRepository;
        private readonly ICollaboratorLevelRepository _collaboratorLevelRepository;
        private readonly IEmailService _emailService;
        private readonly IEmailTemplateService _emailTemplateService;
     
        private readonly Guid _defaultRoleId = Guid.Parse("10C8EE97-2C36-4963-83AB-46CC536E58D8");
        private readonly Guid _defaultLevelId = Guid.Parse("fcd31878-beae-4cfe-86d8-5604cc720bfe");



        public AuthService(
            IUserRepository userRepository,
            IRoleRepository roleRepository,
            IAuthRepository authRepository,
            IJwtService jwtService,
            IExternalAuthService externalAuthService,
            IMapper mapper,
            ICollaboratorRepository collaboratorRepository,
            ICollaboratorLevelRepository collaboratorLevelRepository,
            IEmailService emailService,
            IEmailTemplateService emailTemplateService)
        {
            _userRepository = userRepository;
            _roleRepository = roleRepository;
            _authRepository = authRepository;
            _jwtService = jwtService;
            _externalAuthService = externalAuthService;
            _mapper = mapper;
            _collaboratorRepository = collaboratorRepository;
            _collaboratorLevelRepository = collaboratorLevelRepository;
            _emailService = emailService;
            _emailTemplateService = emailTemplateService;
        }

        public async Task<AuthResponse> LoginAsync(LoginRequest request)
        {
            var user = await _userRepository.GetByUsernameAsync(request.username);
            var userinternal = await _userRepository.GetByUsernameInternalAsync(request.username);

            if (user == null && userinternal == null)
            {
                return new AuthResponse
                {
                    is_success = false,
                    message = "Invalid username or password"
                };
            }

            if (user != null)
            {
                if (!BC.Verify(request.password, user.Password))
                {

                    return new AuthResponse
                    {
                        is_success = false,
                        message = "Password wrong"
                    };

                }
                // var role_id = DapperParamHelper.ToSnakeCaseParams(user?.RoleId);

                // Get user permissions
                var permissions = await _roleRepository.GetPermissionsByRoleIdAsync(user.RoleId);

                var permissionNames = permissions.Select(p => p.PermissionName).ToList();
                // Generate tokens

                var accessToken = _jwtService.GenerateAccessToken(user, permissionNames);

                var refreshToken = _jwtService.GenerateRefreshToken();

                // Save token to database
                await _authRepository.CreateTokenAsync(new Authen
                {
                    UserId = user.UserId,
                    AccessToken = accessToken,
                    RefreshToken = refreshToken,
                    IssuedAt = DateTime.UtcNow,
                    ExpiresAt = DateTime.UtcNow.AddMinutes(60), // Should match token expiry
                    Status = "Active"
                });

                // Update last login
                user.LastLogin = DateTime.UtcNow;
                await _userRepository.UpdateAsync(user);

                return new AuthResponse
                {
                    is_success = true,
                    message = "Login successful",
                    access_token = accessToken,
                    refresh_token = refreshToken,
                    expires_at = DateTime.UtcNow.AddMinutes(60),
                    User = _mapper.Map<UserDto>(user)
                };
            }
            if (userinternal != null)
            {
                if (!string.IsNullOrEmpty(userinternal.Username))
                {
                    {
                        var passwordInternal = SecurityController.Decrypt(userinternal.PasswordHash ?? string.Empty);

                        if (passwordInternal != request.password)
                        {
                            return new AuthResponse
                            {
                                is_success = false,
                                message = "Password user internal wrong"
                            };
                        }
                        var defaultRoleId = await _roleRepository.GetByNameAsync("Collaborator");
                        var userInternal = new User
                        {
                            Username = userinternal.Username,
                            Password = BC.HashPassword(passwordInternal ?? string.Empty),
                            Email = userinternal.Email ?? string.Empty,
                            FullName = userinternal.FullName ?? string.Empty,
                            PhoneNumber = userinternal.PhoneNumber ?? string.Empty,
                            RoleId = defaultRoleId?.RoleId ?? _defaultRoleId,
                            CreatedAt = DateTime.UtcNow,
                            IsActive = true,
                            Group = 1
                        };

                        var userId = await _userRepository.CreateAsync(userInternal);
                        userInternal.UserId = userId;

                        // Get admin user ID for ApprovedBy

                        var userAdmin = await _userRepository.GetByUsernameAsync("admin");
                        Guid? approvedByUserId = userAdmin?.UserId;
                        // Get level for new collaborator
                        var level = await _collaboratorLevelRepository.GetByNameAsync("Sơ cấp");

                        // Create new collaborator for internal user

                        var collaboratorInternal = new Collaborator
                        {
                            UserId = userInternal.UserId,
                            // CollaboratorId = userId,
                            FullName = userinternal.FullName ?? string.Empty,
                            PhoneNumber = userinternal.PhoneNumber ?? string.Empty,
                            Email = userinternal.Email ?? string.Empty,
                            LevelId = level?.LevelId ?? _defaultLevelId,
                            Status = "Active",
                            ApprovedBy = approvedByUserId,
                            ApprovedDate = approvedByUserId.HasValue ? DateTime.UtcNow : null,
                            CreatedAt = DateTime.UtcNow
                        };

                        // Save collaborator to database
                        await _collaboratorRepository.CreateAsync(collaboratorInternal);

                        // Get user with role
                        var createdUser = await _userRepository.GetByIdAsync(userId);

                        if (createdUser == null)
                        {
                            return new AuthResponse
                            {
                                is_success = false,
                                message = "Failed to create user"
                            };
                        }
                        // var role_id_internal = DapperParamHelper.ToSnakeCaseParams(userInternal?.RoleId);
                        // Get user permissions
                        var permissionsInternal = await _roleRepository.GetPermissionsByRoleIdAsync(createdUser.RoleId);
                        var permissionNamesInternal = permissionsInternal.Select(p => p.PermissionName).ToList();

                        // Generate tokens
                        var accessTokenInternal = _jwtService.GenerateAccessToken(createdUser, permissionNamesInternal);
                        var refreshTokenInternal = _jwtService.GenerateRefreshToken();

                        // Save token to database
                        await _authRepository.CreateTokenAsync(new Authen
                        {
                            UserId = createdUser.UserId,
                            AccessToken = accessTokenInternal,
                            RefreshToken = refreshTokenInternal,
                            IssuedAt = DateTime.UtcNow,
                            ExpiresAt = DateTime.UtcNow.AddMinutes(60), // Should match token expiry
                            Status = "Active"
                        });

                        return new AuthResponse
                        {
                            is_success = true,
                            message = "Login successful",
                            access_token = accessTokenInternal,
                            refresh_token = refreshTokenInternal,
                            expires_at = DateTime.UtcNow.AddMinutes(60),
                            User = _mapper.Map<UserDto>(createdUser),
                        };

                    }

                }

            }
            return new AuthResponse
            {
                is_success = false,
                message = "Unknown error"
            };
        }       
     public async Task<AuthResponse> RegisterAsync(RegisterRequest request)
        {
            // Check if username already exists
            var existingUser = await _userRepository.GetByUsernameAsync(request.Username);
            if (existingUser != null)
            {
                return new AuthResponse
                {
                    is_success = false,
                    message = "Username already exists"
                };
            }

            // Check if email already exists
            if (!string.IsNullOrEmpty(request.Email))
            {
                existingUser = await _userRepository.GetByEmailAsync(request.Email);
                if (existingUser != null)
                {
                    return new AuthResponse
                    {
                        is_success = false,
                        message = "Email already exists"
                    };
                }
            }
            var defaultRoleId = await _roleRepository.GetByNameAsync("Collaborator");
            // Create new user
            var user = new User
            {
                Username = request.Username,
                Password = BC.HashPassword(request.Password),
                Email = request.Email,
                FullName = request.FullName,
                PhoneNumber = request.PhoneNumber,
                RoleId = defaultRoleId?.RoleId ?? _defaultRoleId,
                CreatedAt = DateTime.UtcNow,
                IsActive = true,
                Group = 2
            };
            

            var userId = await _userRepository.CreateAsync(user);
            user.UserId = userId;

            // Get level for new collaborator
            var level = await _collaboratorLevelRepository.GetByNameAsync("Sơ cấp");
            // Create collaborator for registered user with Pending status
            var collaboratorRegistered = new Collaborator
            {
                UserId = userId,
                FullName = user.FullName ?? string.Empty,
                PhoneNumber = user.PhoneNumber ?? string.Empty,
                Email = user.Email ?? string.Empty,
                LevelId = level?.LevelId ?? _defaultLevelId,
                Status = "Pending", // Set status to "Pending" for registered users
                ApprovedBy = null, // No approver for registered users initially
                ApprovedDate = null,
                CreatedAt = DateTime.UtcNow
            };

            // Save collaborator to database
            await _collaboratorRepository.CreateAsync(collaboratorRegistered);

            // Get user with role
            var createdUser = await _userRepository.GetByIdAsync(userId);
            if (createdUser == null)
            {
                return new AuthResponse
                {
                    is_success = false,
                    message = "Failed to create user"
                };
            }

            // Get user permissions
            var permissions = await _roleRepository.GetPermissionsByRoleIdAsync(createdUser.RoleId);
            var permissionNames = permissions.Select(p => p.PermissionName).ToList();

            // Generate tokens
            var accessToken = _jwtService.GenerateAccessToken(createdUser, permissionNames);
            var refreshToken = _jwtService.GenerateRefreshToken();

            // Save token to database
            await _authRepository.CreateTokenAsync(new Authen
            {
                UserId = createdUser.UserId,
                AccessToken = accessToken,
                RefreshToken = refreshToken,
                IssuedAt = DateTime.UtcNow,
                ExpiresAt = DateTime.UtcNow.AddMinutes(60), // Should match token expiry
                Status = "Active"
            });

            return new AuthResponse
            {
                is_success = true,
                message = "Registration successful",
                access_token = accessToken,
                refresh_token = refreshToken,
                expires_at = DateTime.UtcNow.AddMinutes(60),
                User = _mapper.Map<UserDto>(createdUser)
            };
        }

        public async Task<AuthResponse> RefreshTokenAsync(RefreshTokenRequest request)
        {
            var token = await _authRepository.GetByRefreshTokenAsync(request.refresh_token);

            if (token == null || token.User == null)
            {
                return new AuthResponse
                {
                    is_success = false,
                    message = "Invalid refresh token"
                };
            }

            if (token.ExpiresAt < DateTime.UtcNow)
            {
                return new AuthResponse
                {
                    is_success = false,
                    message = "Refresh token expired"
                };
            }

            // Revoke the current refresh token
            await _authRepository.RevokeTokenAsync(request.refresh_token);

            // Get user permissions
            var permissions = await _roleRepository.GetPermissionsByRoleIdAsync(token.User.RoleId);
            var permissionNames = permissions.Select(p => p.PermissionName).ToList();

            // Generate new tokens
            var accessToken = _jwtService.GenerateAccessToken(token.User, permissionNames);
            var refreshToken = _jwtService.GenerateRefreshToken();

            // Save new token to database
            await _authRepository.CreateTokenAsync(new Authen
            {
                UserId = token.User.UserId,
                AccessToken = accessToken,
                RefreshToken = refreshToken,
                IssuedAt = DateTime.UtcNow,
                ExpiresAt = DateTime.UtcNow.AddMinutes(60), // Should match token expiry
                Status = "Active"
            });

            return new AuthResponse
            {
                is_success = true,
                message = "Token refreshed successfully",
                access_token = accessToken,
                refresh_token = refreshToken,
                expires_at = DateTime.UtcNow.AddMinutes(60),
                User = _mapper.Map<UserDto>(token.User)
            };
        }

        public async Task<bool> RevokeTokenAsync(string refreshToken)
        {
            return await _authRepository.RevokeTokenAsync(refreshToken);
        }

        public async Task<AuthResponse> ExternalLoginAsync(ExternalAuthRequest request)
        {
            if (!string.Equals(request.Provider, "google", StringComparison.OrdinalIgnoreCase))
            {
                return new AuthResponse
                {
                    is_success = false,
                    message = "Unsupported provider"
                };
            }

            var (isValid, email, name, externalId) = await _externalAuthService.ValidateGoogleTokenAsync(request.IdToken);

            if (!isValid || string.IsNullOrEmpty(email) || string.IsNullOrEmpty(externalId))
            {
                return new AuthResponse
                {
                    is_success = false,
                    message = "Invalid token"
                };
            }

            // Check if user already exists with this external auth
            var externalAuth = await _authRepository.GetExternalAuthInfoAsync(request.Provider, externalId);

            if (externalAuth != null && externalAuth.User != null)
            {
                // User exists, update last login
                externalAuth.LastLogin = DateTime.UtcNow;
                await _authRepository.UpdateExternalAuthInfoAsync(externalAuth);

                // Update user's last login
                externalAuth.User.LastLogin = DateTime.UtcNow;
                await _userRepository.UpdateAsync(externalAuth.User);

                // Get user permissions
                var permissions = await _roleRepository.GetPermissionsByRoleIdAsync(externalAuth.User.RoleId);
                var permissionNames = permissions.Select(p => p.PermissionName).ToList();

                // Generate tokens
                var accessToken = _jwtService.GenerateAccessToken(externalAuth.User, permissionNames);
                var refreshToken = _jwtService.GenerateRefreshToken();

                // Save token to database
                await _authRepository.CreateTokenAsync(new Authen
                {
                    UserId = externalAuth.User.UserId,
                    AccessToken = accessToken,
                    RefreshToken = refreshToken,
                    IssuedAt = DateTime.UtcNow,
                    ExpiresAt = DateTime.UtcNow.AddMinutes(60), // Should match token expiry
                    Status = "Active"
                });

                return new AuthResponse
                {
                    is_success = true,
                    message = "Login successful",
                    access_token = accessToken,
                    refresh_token = refreshToken,
                    expires_at = DateTime.UtcNow.AddMinutes(60),
                    User = _mapper.Map<UserDto>(externalAuth.User)
                };
            }
            else
            {
                // Check if user exists with this email
                var existingUser = await _userRepository.GetByEmailAsync(email);

                if (existingUser != null)
                {
                    // Link external auth to existing user
                    var newExternalAuth = new ExternalAuthInfo
                    {
                        UserId = existingUser.UserId,
                        Provider = request.Provider,
                        ExternalId = externalId,
                        Email = email,
                        Name = name,
                        CreatedAt = DateTime.UtcNow,
                        LastLogin = DateTime.UtcNow
                    };

                    await _authRepository.CreateExternalAuthInfoAsync(newExternalAuth);

                    // Update user's last login
                    existingUser.LastLogin = DateTime.UtcNow;
                    await _userRepository.UpdateAsync(existingUser);

                    // Get user permissions
                    var permissions = await _roleRepository.GetPermissionsByRoleIdAsync(existingUser.RoleId);
                    var permissionNames = permissions.Select(p => p.PermissionName).ToList();

                    // Generate tokens
                    var accessToken = _jwtService.GenerateAccessToken(existingUser, permissionNames);
                    var refreshToken = _jwtService.GenerateRefreshToken();

                    // Save token to database
                    await _authRepository.CreateTokenAsync(new Authen
                    {
                        UserId = existingUser.UserId,
                        AccessToken = accessToken,
                        RefreshToken = refreshToken,
                        IssuedAt = DateTime.UtcNow,
                        ExpiresAt = DateTime.UtcNow.AddMinutes(60), // Should match token expiry
                        Status = "Active"
                    });

                    return new AuthResponse
                    {
                        is_success = true,
                        message = "Login successful",
                        access_token = accessToken,
                        refresh_token = refreshToken,
                        expires_at = DateTime.UtcNow.AddMinutes(60),
                        User = _mapper.Map<UserDto>(existingUser)
                    };
                }
                else
                {
                    var username = email.Split('@')[0] + Guid.NewGuid().ToString()[..6];
                     var defaultRoleId = await _roleRepository.GetByNameAsync("Collaborator");
                    var newUser = new User
                    {
                        Username = username,
                        Password = BC.HashPassword(Guid.NewGuid().ToString()), // Random password
                        Email = email,
                        FullName = name,
                        RoleId = defaultRoleId?.RoleId ?? _defaultRoleId,
                        CreatedAt = DateTime.UtcNow,
                        LastLogin = DateTime.UtcNow,
                        IsActive = true
                    };

                    var userId = await _userRepository.CreateAsync(newUser);
                    newUser.UserId = userId;

                    // Create external auth info
                    var newExternalAuth = new ExternalAuthInfo
                    {
                        UserId = userId,
                        Provider = request.Provider,
                        ExternalId = externalId,
                        Email = email,
                        Name = name,
                        CreatedAt = DateTime.UtcNow,
                        LastLogin = DateTime.UtcNow
                    };

                    await _authRepository.CreateExternalAuthInfoAsync(newExternalAuth);

                    // Get user with role
                    var createdUser = await _userRepository.GetByIdAsync(userId);
                    if (createdUser == null)
                    {
                        return new AuthResponse
                        {
                            is_success = false,
                            message = "Failed to create user"
                        };
                    }

                    // Get user permissions
                    var permissions = await _roleRepository.GetPermissionsByRoleIdAsync(createdUser.RoleId);
                    var permissionNames = permissions.Select(p => p.PermissionName).ToList();

                    // Generate tokens
                    var accessToken = _jwtService.GenerateAccessToken(createdUser, permissionNames);
                    var refreshToken = _jwtService.GenerateRefreshToken();

                    // Save token to database
                    await _authRepository.CreateTokenAsync(new Authen
                    {
                        UserId = createdUser.UserId,
                        AccessToken = accessToken,
                        RefreshToken = refreshToken,
                        IssuedAt = DateTime.UtcNow,
                        ExpiresAt = DateTime.UtcNow.AddMinutes(60), // Should match token expiry
                        Status = "Active"
                    });

                    return new AuthResponse
                    {
                        is_success = true,
                        message = "Registration successful",
                        access_token = accessToken,
                        refresh_token = refreshToken,
                        expires_at = DateTime.UtcNow.AddMinutes(60),
                        User = _mapper.Map<UserDto>(createdUser)
                    };
                }
            }
        }

        public async Task<bool> LogoutAsync(LogoutRequest request)
        {
            if (string.IsNullOrEmpty(request.refresh_token))
            {
                return false;
            }

            // Get token info to find user
            var token = await _authRepository.GetByRefreshTokenAsync(request.refresh_token);
            if (token?.User != null)
            {
                // Revoke ALL tokens for this user (logout from all devices)
                await _authRepository.RevokeAllUserTokensAsync(token.User.UserId);
                return true;
            }

            // If token not found, just try to revoke the specific token
            return await _authRepository.RevokeTokenAsync(request.refresh_token);
        }

        public async Task<ForgotPasswordResponse> ForgotPasswordAsync(ForgotPasswordRequest request)
        {
            var user = await _userRepository.GetByEmailAsync(request.email);

            if (user == null)
            {
                // Don't reveal if email exists or not for security reasons
                return new ForgotPasswordResponse
                {
                    is_success = true,
                    message = "If the email exists in our system, a password reset link has been sent."
                };
            }

            // Generate reset token
            var resetToken = Guid.NewGuid().ToString();
            var passwordResetToken = new PasswordResetToken
            {
                UserId = user.UserId,
                Token = resetToken,
                ExpiresAt = DateTime.UtcNow.AddHours(1), // Token expires in 1 hour
                CreatedAt = DateTime.UtcNow
            };

            // Save reset token to database
            await _authRepository.CreatePasswordResetTokenAsync(passwordResetToken);

            // Send email using template
            await _emailTemplateService.SendPasswordResetEmailAsync(
                user.Email ?? string.Empty,
                user.FullName ?? user.Username,
                resetToken,
                ""); //Bỏ resetCode

            return new ForgotPasswordResponse
            {
                is_success = true,
                message = "If the email exists in our system, a password reset link has been sent."
            };
        }

        public async Task<ForgotPasswordResponse> ResetPasswordAsync(ResetPasswordRequest request)
        {
            var resetToken = await _authRepository.GetPasswordResetTokenAsync(request.token);

            if (resetToken == null || resetToken.User == null)
            {
                return new ForgotPasswordResponse
                {
                    is_success = false,
                    message = "Invalid or expired reset token"
                };
            }

            if (resetToken.ExpiresAt < DateTime.UtcNow)
            {
                return new ForgotPasswordResponse
                {
                    is_success = false,
                    message = "Reset token has expired"
                };
            }

            // Update user password
            resetToken.User.Password = BC.HashPassword(request.new_password);
            await _userRepository.UpdateAsync(resetToken.User);

            // Invalidate the reset token
            await _authRepository.InvalidatePasswordResetTokenAsync(request.token);

            // Revoke all existing tokens for this user for security
            await _authRepository.RevokeAllUserTokensAsync(resetToken.User.UserId);

            return new ForgotPasswordResponse
            {
                is_success = true,
                message = "Password has been reset successfully"
            };
        }
    }
}
