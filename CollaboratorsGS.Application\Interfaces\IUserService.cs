using CollaboratorsGS.Application.DTOs;

namespace CollaboratorsGS.Application.Interfaces
{
    public interface IUserService
    {
        Task<UserDto?> GetByIdAsync(Guid userId);
        Task<IEnumerable<UserDto>> GetAllAsync();
        Task<Guid> CreateUserAsync(CreateUserRequest request);
        Task<bool> UpdateUserAsync(Guid userId, UpdateUserRequest request);
        Task<bool> DeleteUserAsync(Guid userId);
        Task<bool> ChangePasswordAsync(ChangePasswordRequest request);
        Task<bool> AdminChangePasswordAsync(AdminChangePasswordRequest request);
    }
}
