using CollaboratorsGS.Application.DTOs.CollaboratorReport;
using CollaboratorsGS.Application.Interfaces;
using CollaboratorsGS.Infrastructure.Data;
using Dapper;
using System.Data;

namespace CollaboratorsGS.Infrastructure.Services
{
    public class CollaboratorDashboardService : ICollaboratorDashboardService
    {
        private readonly IConnectionFactory _connectionFactory;

        public CollaboratorDashboardService(IConnectionFactory connectionFactory)
        {
            _connectionFactory = connectionFactory;
        }

        public async Task<CollaboratorDashboardReportDto?> GetDashboardReportAsync(Guid collaboratorId)
        {
            using var connection = _connectionFactory.CreateConnection();

            var parameters = new DynamicParameters();
            parameters.Add("@CollaboratorId", collaboratorId, DbType.Guid);

            using var multi = await connection.QueryMultipleAsync(
                "sp_GetCollaboratorDashboardReport",
                parameters,
                commandType: CommandType.StoredProcedure);
            var collaboratorInfo = (await multi.ReadAsync<CollaboratorBasicInfoDto>()).FirstOrDefault();
            if (collaboratorInfo == null)
                return null;

            var currentKpi = (await multi.ReadAsync<CollaboratorKpiCurrentDto>()).FirstOrDefault()
                             ?? new CollaboratorKpiCurrentDto();

            var currentTarget = (await multi.ReadAsync<CollaboratorDashboardKpiTargetDto>()).FirstOrDefault()
                                ?? new CollaboratorDashboardKpiTargetDto();

            var rewardSummary = (await multi.ReadAsync<CollaboratorRewardSummaryDto>()).FirstOrDefault()
                                ?? new CollaboratorRewardSummaryDto();

            var kpiHistory = (await multi.ReadAsync<CollaboratorKpiHistoryDto>()).ToList();

            var nextLevel = (await multi.ReadAsync<CollaboratorNextLevelDto>()).FirstOrDefault();

            // Read candidates list
            var candidates = (await multi.ReadAsync<CollaboratorCandidateDto>()).ToList();

            return new CollaboratorDashboardReportDto
            {
                CollaboratorInfo = collaboratorInfo,
                CurrentKpi = currentKpi,
                CurrentTarget = currentTarget,
                RewardSummary = rewardSummary,
                KpiHistory = kpiHistory,
                NextLevel = nextLevel,
                Candidates = candidates
            };
        }
    }
}
