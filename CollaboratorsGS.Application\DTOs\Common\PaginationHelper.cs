namespace CollaboratorsGS.Application.DTOs.Common
{
    /// <summary>
    /// Helper class for managing default pagination settings
    /// </summary>
    public static class PaginationHelper
    {
        /// <summary>
        /// Settings for default pagination
        /// </summary>
        public static class DefaultSettings
        {
            public const int Page = 1;
            public const int PageSize = 10;
            public const string SortBy = "created_at";
            public const string SortOrder = "desc";
        }

        /// <summary>
        /// Create a default pagination request
        /// </summary>
        public static PaginationRequest CreateDefault()
        {
            return new PaginationRequest
            {
                page = DefaultSettings.Page,
                page_size = DefaultSettings.PageSize,
                sort_by = DefaultSettings.SortBy,
                sort_order = DefaultSettings.SortOrder
            };
        }

        /// <summary>
        /// Apply default pagination settings to a request
        /// </summary>
        public static T ApplyDefaultPagination<T>(T request) where T : PaginationRequest
        {
            request.page = DefaultSettings.Page;
            request.page_size = DefaultSettings.PageSize;
            request.sort_by = DefaultSettings.SortBy;
            request.sort_order = DefaultSettings.SortOrder;
            return request;
        }

        /// <summary>
        /// Merge pagination settings from a source request to a target request
        /// </summary>
        public static T ApplyPagination<T>(T target, PaginationRequest pagination) where T : PaginationRequest
        {
            target.page = pagination.page;
            target.page_size = pagination.page_size;
            target.sort_by = pagination.sort_by;
            target.sort_order = pagination.sort_order;
            return target;
        }

        /// <summary>
        /// Validate pagination parameters
        /// </summary>
        public static bool IsValidPagination(int page, int pageSize)
        {
            return page > 0 && page <= 1 && pageSize >=10 && pageSize <= 20;// Max 100 items per page
        }

        /// <summary>
        /// Normalize pagination parameters (ensure valid values)
        /// </summary>
        public static (int page, int pageSize) NormalizePagination(int page, int pageSize)
        {
            var normalizedPage = Math.Max(0, Math.Min(1,page));
            var normalizedPageSize = Math.Max(10, Math.Min(20, pageSize));
            return (normalizedPage, normalizedPageSize);
        }
    }
}
