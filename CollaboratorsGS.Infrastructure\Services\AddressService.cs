
using CollaboratorsGS.Application.DTOs.AddressDto;
using CollaboratorsGS.Application.Interfaces;
using CollaboratorsGS.Domain.Repositories;
using Microsoft.Extensions.Logging;
using AutoMapper;
using CollaboratorsGS.Domain.Entities;

namespace CollaboratorsGS.Infrastructure.Services
{
    public class AddressService : IAddressService
    {
        private readonly IAddressRepository _addressRepository;
        private readonly ILogger<AddressService> _logger;
        private readonly IMapper _mapper;

        public AddressService(
            IAddressRepository addressRepository,
            ILogger<AddressService> logger,
            IMapper mapper)
        {
            _addressRepository = addressRepository;
            _logger = logger;
            _mapper = mapper;
        }

        public async Task<IEnumerable<ProvinceDto>> GetAllProvincesAsync(bool isRestructure)
        {
            try
            {
                var provinces = await _addressRepository.GetAllProvincesAsync(isRestructure);
                return _mapper.Map<IEnumerable<ProvinceDto>>(provinces);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting provinces with restructure flag: {IsRestructure}", isRestructure);
                throw;
            }
        }

        public async Task<IEnumerable<DistrictDto>> GetDistrictsByProvinceNameAsync(string queryCode)
        {
            try
            {
                var districts = await _addressRepository.GetDistrictsByProvinceCodeAsync(queryCode);
                return _mapper.Map<IEnumerable<DistrictDto>>(districts);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting districts for province code: {QueryCode}", queryCode);
                throw;
            }
        }

        public async Task<IEnumerable<WardDto>> GetWardsByDictricstNameAsync(string queryCode, bool isRestructure)
        {
            try
            {
                var wards = await _addressRepository.GetWardsByDistrictCodeAsync(queryCode, isRestructure);
                return _mapper.Map<IEnumerable<WardDto>>(wards);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting wards for district code: {QueryCode} with restructure flag: {IsRestructure}", queryCode, isRestructure);
                throw;
            }
        }
    }
}