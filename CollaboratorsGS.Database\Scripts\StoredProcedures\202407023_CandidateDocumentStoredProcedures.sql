-- Stored Procedures for CandidateDocument operations

IF EXISTS (SELECT * FROM sys.objects WHERE type = 'P' AND name = 'sp_CreateCandidateDocument')
    DROP PROCEDURE sp_CreateCandidateDocument
GO

Create PROCEDURE [dbo].[sp_CreateCandidateDocument]
    @DocumentId UNIQUEIDENTIFIER,
    @CandidateId UNIQUEIDENTIFIER,
    @DocumentType VARCHAR(50),
    @FilePath VARCHAR(255),
    @FileType VARCHAR(10),
    @UploadedAt DATETIME
AS
BEGIN
    INSERT INTO candidate_documents (
        document_id,
        candidate_id,
        document_type,
        file_path,
        file_type,
        uploaded_at
    )
    VALUES (
        @DocumentId,
        @CandidateId,
        @DocumentType,
        @FilePath,
        @FileType,
        @UploadedAt
    )
END
GO

IF EXISTS (SELECT * FROM sys.objects WHERE type = 'P' AND name = 'sp_GetCandidateDocumentById')
    DROP PROCEDURE sp_GetCandidateDocumentById
GO

CREATE PROCEDURE sp_GetCandidateDocumentById
    @DocumentId UNIQUEIDENTIFIER
AS
BEGIN
    SELECT *
    FROM candidate_documents
    WHERE document_id = @DocumentId
END
GO

IF EXISTS (SELECT * FROM sys.objects WHERE type = 'P' AND name = 'sp_GetCandidateDocumentsByCandidateId')
    DROP PROCEDURE sp_GetCandidateDocumentsByCandidateId
GO

CREATE PROCEDURE sp_GetCandidateDocumentsByCandidateId
    @CandidateId UNIQUEIDENTIFIER
AS
BEGIN
    SELECT *
    FROM candidate_documents
    WHERE candidate_id = @CandidateId
    ORDER BY uploaded_at DESC
END
GO
IF EXISTS (SELECT * FROM sys.objects WHERE type = 'P' AND name = 'sp_UpdateCandidateDocument')
    DROP PROCEDURE sp_UpdateCandidateDocument
GO

CREATE PROCEDURE sp_UpdateCandidateDocument
    @DocumentId UNIQUEIDENTIFIER,
    @DocumentType NVARCHAR(50),
    @FilePath NVARCHAR(255),
    @FileType NVARCHAR(50)
AS
BEGIN
    UPDATE candidate_documents
    SET
        document_type = @DocumentType,
        file_path = @FilePath,
        file_type = @FileType,
        uploaded_at = GETDATE()
    WHERE document_id = @DocumentId

    SELECT @@ROWCOUNT AS RowsAffected
END
GO

IF EXISTS (SELECT * FROM sys.objects WHERE type = 'P' AND name = 'sp_DeleteCandidateDocument')
    DROP PROCEDURE sp_DeleteCandidateDocument
GO

CREATE PROCEDURE sp_DeleteCandidateDocument
    @DocumentId UNIQUEIDENTIFIER
AS
BEGIN
    DELETE FROM candidate_documents
    WHERE document_id = @DocumentId

    SELECT @@ROWCOUNT AS RowsAffected
END
GO
