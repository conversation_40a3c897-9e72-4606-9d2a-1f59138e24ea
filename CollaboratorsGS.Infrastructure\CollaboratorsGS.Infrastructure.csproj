<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="AutoMapper" Version="13.0.1" />
    <PackageReference Include="BCrypt.Net-Next" Version="4.0.3" />
    <PackageReference Include="Dapper" Version="2.1.66" />
    <PackageReference Include="EPPlus" Version="4.5.3.3" />
    <PackageReference Include="FirebaseAdmin" Version="2.4.0" />
    <PackageReference Include="Google.Apis.Auth" Version="1.67.0" />
    <PackageReference Include="iTextSharp" Version="5.5.13.4" />
    <PackageReference Include="Microsoft.AspNetCore.Authentication.Google" Version="8.0.3" />
    <PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="8.0.3" />
    <PackageReference Include="Microsoft.Extensions.Configuration" Version="8.0.0" />
    <PackageReference Include="Microsoft.Extensions.DependencyInjection" Version="8.0.1" />
    <PackageReference Include="Microsoft.Extensions.Options.ConfigurationExtensions" Version="8.0.0" />
    <PackageReference Include="Minio" Version="6.0.2" />
    <PackageReference Include="PdfSharp" Version="6.2.0" />
    <PackageReference Include="Swashbuckle.AspNetCore" Version="6.5.0" />
    <PackageReference Include="System.Data.SqlClient" Version="4.8.6" />
    <PackageReference Include="System.Drawing.Common" Version="9.0.7" />
    <PackageReference Include="System.IdentityModel.Tokens.Jwt" Version="7.4.0" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\CollaboratorsGS.Application\CollaboratorsGS.Application.csproj" />
  </ItemGroup>

</Project>
