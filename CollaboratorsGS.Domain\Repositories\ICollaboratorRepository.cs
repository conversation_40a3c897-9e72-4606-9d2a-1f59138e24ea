using CollaboratorsGS.Domain.Entities;

namespace CollaboratorsGS.Domain.Repositories
{
    public interface ICollaboratorRepository
    {
        Task<Collaborator?> GetByIdAsync(Guid CollaboratorId);
        Task<IEnumerable<Collaborator>> GetAllAsync();
        Task<IEnumerable<Collaborator>> GetByStatusAsync(string status);
        Task<Collaborator?> GetByUserIdAsync(Guid userId);
        Task<Collaborator?> GetByEmailAsync(string email);
        Task<Collaborator?> GetByPhoneNumberAsync(string phoneNumber);
        Task<Guid> CreateAsync(Collaborator collaborator);
        Task<bool> UpdateAsync(Collaborator collaborator);
        Task<bool> DeleteAsync(Guid CollaboratorId);
        Task<bool> ApproveAsync(Guid CollaboratorId, Guid approvedBy);
        Task<CollaboratorProfile?> GetProfileByCtvIdAsync(Guid CollaboratorId);
        Task<bool> CreateProfileAsync(CollaboratorProfile profile);
        Task<bool> UpdateProfileAsync(CollaboratorProfile profile);
        Task<bool> CreateOrUpdateProfileFromExtractionAsync(Guid collaboratorId, string citizenIdExtracted, string fullNameExtracted, string dobString, string genderExtracted, string addressExtracted, string personalIdentificationExtracted, string idIssueDateString, string idIssueAuthorityExtracted, string extractionRawData);
        Task<bool> UpdateInformationProfileAsync(Guid collaboratorId, string? currentAddress, string? bankName, string? bankAccountNumber, string? bankAccountName);
        Task UpdateAvatarAsync(Guid collaboratorId, string avatarUrl);
        Task<IEnumerable<dynamic>?> GetReferalHistoryAsync(Guid userId);
        Task<object> GetLevelDetailByUserIdAsync(Guid userId);
        Task<dynamic?> CheckLevelUpgradeEligibilityAsync(Guid userId);
        Task<Guid?> GetByUserId(Guid userId) ;
    }
}
