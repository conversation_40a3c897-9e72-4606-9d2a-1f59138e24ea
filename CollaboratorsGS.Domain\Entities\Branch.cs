
namespace CollaboratorsGS.Domain.Entities
{
    public class Branch
    {
        public Guid BranchId { get; set; }
        public Guid CompanyId { get; set; }
        public string BranchName { get; set; } = string.Empty;
        public string? PhoneNumber { get; set; }
        public string? Email { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime? UpdatedAt { get; set; }
        public string? CompanyName { get; set; }

        // Navigation properties
        public Company? Company { get; set; }
        public ICollection<Department>? Departments { get; set; }
    }
}
