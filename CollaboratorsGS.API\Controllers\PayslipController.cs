using CollaboratorsGS.Application.Constants;
using CollaboratorsGS.Application.DTOs;
using CollaboratorsGS.Application.Interfaces;
using CollaboratorsGS.Domain.Entities;
using CollaboratorsGS.Infrastructure.Services;
using CollaboratorsGS.Infrastructure.Utilities;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Minio.DataModel.Notification;
using Minio.Helper;
using Newtonsoft.Json;
using System.Data;
using System.Drawing;
using System.Globalization;
using System.Threading.Tasks;

namespace CollaboratorsGS.API.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class PayslipController : ControllerBase
    {
        private readonly IPayslipService _payslipService;
        private readonly IPayrollService _payrollService;

        private readonly IUserService _userService;

        private readonly ILogger<PayslipController> _logger;

        public PayslipController(IPayslipService payslipService,
         IPayrollService payrollService,
          ILogger<PayslipController> logger,
          IUserService userService)
        {
            _payslipService = payslipService;
            _payrollService = payrollService;
            _logger = logger;
            _userService = userService;
        }
        [HttpGet("payslip")]
        [Authorize]
        public async Task<IActionResult> GetpayrollAll(int Year, int month)
        {
            try
            {
                var userIdClaim = User.FindFirst(System.Security.Claims.ClaimTypes.NameIdentifier)
                ?? User.FindFirst("sub");

                if (userIdClaim == null || !Guid.TryParse(userIdClaim.Value, out var userId))
                {
                    return BadRequest(ApiResponse<object>.ErrorResponse(
                        MessageCodes.ER4001,
                        "User ID not found in token or invalid",
                        400,
                        new List<ErrorDetail>
                        {
                            new ErrorDetail
                            {
                                Field = "token",
                                ErrorCode = MessageCodes.ER4001,
                                Message = "Invalid user ID in token"
                            }
                        }));
                }

                var user = await _userService.GetByIdAsync(userId);
                if (user == null)
                {
                    return NotFound(ApiResponse<object>.ErrorResponse(
                         MessageCodes.ER4004,
                         "User not found",
                         404));
                }
                int EmployeeId = user.employee_id;
                var result = await _payrollService.GetListAsync(Year, month, EmployeeId);
                return Ok(ApiResponse<object>.SuccessResponse(
                    MessageCodes.SC2000,
                    "Get all payroll successfully",
                    result));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting all payroll");
                return StatusCode(500, ApiResponse<object>.ErrorResponse(
                    MessageCodes.ER5000,
                    "Internal server error",
                    500));
            }
        }
        [HttpGet("payslip-url")]
        public async Task<IActionResult> GetPayslip(int Year, int month, int payrollId)
        {
            try
            {
                var userIdClaim = User.FindFirst(System.Security.Claims.ClaimTypes.NameIdentifier)
                ?? User.FindFirst("sub");

                if (userIdClaim == null || !Guid.TryParse(userIdClaim.Value, out var userId))
                {
                    return BadRequest(ApiResponse<object>.ErrorResponse(
                        MessageCodes.ER4001,
                        "User ID not found in token or invalid",
                        400,
                        new List<ErrorDetail>
                        {
                            new ErrorDetail
                            {
                                Field = "token",
                                ErrorCode = MessageCodes.ER4001,
                                Message = "Invalid user ID in token"
                            }
                        }));
                }

                var user = await _userService.GetByIdAsync(userId);
                if (user == null)
                {
                    return NotFound(ApiResponse<object>.ErrorResponse(
                    MessageCodes.ER4004,
                    "User not found",
                    404));
                }
                int EmployeeId =  96550;


                var result = await _payrollService.GetByIdAsync(payrollId, EmployeeId);
                if (result == null)
                {
                    return NotFound(ApiResponse<object>.ErrorResponse(
                    MessageCodes.ER4004,
                    "Payroll not found",
                    404));
                }
                

                var Urlpayslip = await _payslipService.GetPayslipUrlAsync(result.vesion, result.benefitName, result.benefitPhone, month, Year, EmployeeId, 1, payrollId);
                if (string.IsNullOrEmpty(Urlpayslip))
                {
                    return NotFound(ApiResponse<object>.ErrorResponse(
                    MessageCodes.ER4004,
                    "Payslip not found",
                    404));
                }
                return Ok(ApiResponse<object>.SuccessResponse(
                    MessageCodes.SC2000,
                    "Get payslip successfully",
                    Urlpayslip));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting all candidates");
                return StatusCode(500, ApiResponse<object>.ErrorResponse(
                    MessageCodes.ER5000,
                    "Internal server error",
                    500));
            }
        }
    }


}
