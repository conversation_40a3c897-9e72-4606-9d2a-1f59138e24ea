-- Stored Procedures for Candidate operations

-- Get Candidate by ID
IF EXISTS (SELECT * FROM sys.objects WHERE type = 'P' AND name = 'sp_GetCandidateById')
    DROP PROCEDURE sp_GetCandidateById
GO

CREATE PROCEDURE sp_GetCandidateById
    @CandidateId UNIQUEIDENTIFIER
AS
BEGIN
    SELECT c.*, ctv.*
    FROM candidates c
    LEFT JOIN collaborators ctv ON c.collaborator_id = ctv.collaborator_id
    WHERE c.candidate_id = @CandidateId
END
GO

-- Get All Candidates
IF EXISTS (SELECT * FROM sys.objects WHERE type = 'P' AND name = 'sp_GetAllCandidates')
    DROP PROCEDURE sp_GetAllCandidates
GO

CREATE PROCEDURE sp_GetAllCandidates
AS
BEGIN
    SELECT c.*, ctv.*
    FROM candidates c
    LEFT JOIN collaborators ctv ON c.collaborator_id = ctv.collaborator_id
END
GO

-- Get Candidates by Collaborator ID
IF EXISTS (SELECT * FROM sys.objects WHERE type = 'P' AND name = 'sp_GetCandidatesByCollaboratorId')
    DROP PROCEDURE sp_GetCandidatesByCollaboratorId
GO

CREATE PROCEDURE sp_GetCandidatesByCollaboratorId
    @CollaboratorId UNIQUEIDENTIFIER
AS
BEGIN
    SELECT c.*, ctv.*
    FROM candidates c
    LEFT JOIN collaborators ctv ON c.collaborator_id = ctv.collaborator_id
    WHERE c.collaborator_id = @CollaboratorId
END
GO

-- Get Candidate by Email
IF EXISTS (SELECT * FROM sys.objects WHERE type = 'P' AND name = 'sp_GetCandidateByEmail')
    DROP PROCEDURE sp_GetCandidateByEmail
GO

CREATE PROCEDURE sp_GetCandidateByEmail
    @Email VARCHAR(255) = NULL
AS
BEGIN
    SELECT c.*, ctv.*
    FROM candidates c
    LEFT JOIN collaborators ctv ON c.collaborator_id = ctv.collaborator_id
    WHERE c.email = @Email
END
GO
IF EXISTS (SELECT * FROM sys.objects WHERE type = 'P' AND name = 'sp_GetCandidateByCitizenId')
    DROP PROCEDURE sp_GetCandidateByCitizenId
GO
CREATE PROCEDURE sp_GetCandidateByCitizenId
    @CitizenId VARCHAR(12) = NULL
AS
BEGIN
    SELECT c.*, ctv.*
    FROM candidates c
    LEFT JOIN collaborators ctv ON c.collaborator_id = ctv.collaborator_id
    WHERE c.citizen_id = @CitizenId
END
GO

-- Get Candidate by Phone Number
IF EXISTS (SELECT * FROM sys.objects WHERE type = 'P' AND name = 'sp_GetCandidateByPhoneNumber')
    DROP PROCEDURE sp_GetCandidateByPhoneNumber
GO

CREATE PROCEDURE sp_GetCandidateByPhoneNumber
    @PhoneNumber VARCHAR(50)
AS
BEGIN
    SELECT c.*, ctv.*
    FROM candidates c
    LEFT JOIN collaborators ctv ON c.collaborator_id = ctv.collaborator_id
    WHERE c.phone_number = @PhoneNumber
END
GO

-- Create Candidate
IF EXISTS (SELECT * FROM sys.objects WHERE type = 'P' AND name = 'sp_CreateCandidate')
    DROP PROCEDURE sp_CreateCandidate
GO

CREATE PROCEDURE sp_CreateCandidate
    @CandidateId UNIQUEIDENTIFIER,
    @FullName NVARCHAR(255),
    @PhoneNumber VARCHAR(50),
    @Email VARCHAR(255) = NULL,
    @EducationLevel NVARCHAR(500) = NULL,
    @WorkExperience NVARCHAR(500) = NULL,
    @Skills NVARCHAR(500) = NULL,
    @DateOfBirth DATE = NULL,
    @Gender VARCHAR(10) = NULL,
    @Address NVARCHAR(MAX) = NULL,
    @ProfilePicture VARCHAR(255) = NULL,
    @FullBodyPicture VARCHAR(255) = NULL,
    @HeightCm INT = NULL,
    @WeightKg INT = NULL,
    @Level VARCHAR(50) = NULL,
    @Source VARCHAR(50) = NULL,
    @CollaboratorId UNIQUEIDENTIFIER = NULL,
    @CitizenId NVARCHAR(12) = NULL,
    @CitizenIdAddress NVARCHAR(MAX) = NULL,
    @CitizenIdIssueDate DATE = NULL,
    @CitizenIdIssuePlace NVARCHAR(500) = NULL,
    @CreatedAt DATETIME
AS
BEGIN
    INSERT INTO candidates (
        candidate_id, full_name, phone_number, email, education_level, work_experience, skills, date_of_birth,
        gender, address, profile_picture, full_body_picture, height_cm, weight_kg, level, source,
        collaborator_id, citizen_id, citizen_id_address, citizen_id_issue_date, citizen_id_issue_place, created_at
    )
    VALUES (
        @CandidateId, @FullName, @PhoneNumber, @Email, @EducationLevel, @WorkExperience, @Skills, @DateOfBirth,
        @Gender, @Address, @ProfilePicture, @FullBodyPicture, @HeightCm, @WeightKg, @Level, @Source,
        @CollaboratorId, @CitizenId, @CitizenIdAddress, @CitizenIdIssueDate, @CitizenIdIssuePlace, @CreatedAt
    )
END

GO

-- Update Candidate
IF EXISTS (SELECT * FROM sys.objects WHERE type = 'P' AND name = 'sp_UpdateCandidate')
    DROP PROCEDURE sp_UpdateCandidate
GO

CREATE PROCEDURE sp_UpdateCandidate
    @CandidateId UNIQUEIDENTIFIER,
    @FullName NVARCHAR(255),
    @PhoneNumber VARCHAR(50),
    @Email VARCHAR(255) = NULL,
    @EducationLevel NVARCHAR(500) = NULL,
    @WorkExperience NVARCHAR(500) = NULL,
    @Skills NVARCHAR(500) = NULL,
    @DateOfBirth DATE = NULL,
    @Gender VARCHAR(10) = NULL,
    @Address NVARCHAR(MAX) = NULL,
    @ProfilePicture VARCHAR(255) = NULL,
    @FullBodyPicture VARCHAR(255) = NULL,
    @HeightCm INT = NULL,
    @WeightKg INT = NULL,
    @Level VARCHAR(50) = NULL,
    @Source VARCHAR(50) = NULL,
    @CollaboratorId UNIQUEIDENTIFIER = NULL,
    @CitizenId NVARCHAR(12) = NULL,
    @CitizenIdAddress NVARCHAR(MAX) = NULL,
    @CitizenIdIssueDate DATE = NULL,
    @CitizenIdIssuePlace NVARCHAR(500) = NULL,
    @UpdatedAt DATETIME
AS
BEGIN
    UPDATE candidates
    SET
        full_name = @FullName,
        phone_number = @PhoneNumber,
        email = @Email,
        education_level = @EducationLevel,
        work_experience = @WorkExperience,
        skills = @Skills,
        date_of_birth = @DateOfBirth,
        gender = @Gender,
        address = @Address,
        profile_picture = @ProfilePicture,
        full_body_picture = @FullBodyPicture,
        height_cm = @HeightCm,
        weight_kg = @WeightKg,
        level = @Level,
        source = @Source,
        collaborator_id = @CollaboratorId,
        citizen_id = @CitizenId,
        citizen_id_address = @CitizenIdAddress,
        citizen_id_issue_date = @CitizenIdIssueDate,
        citizen_id_issue_place = @CitizenIdIssuePlace,
        updated_at = @UpdatedAt
    WHERE candidate_id = @CandidateId

    SELECT @@ROWCOUNT AS RowsAffected
END

GO

-- Delete Candidate
IF EXISTS (SELECT * FROM sys.objects WHERE type = 'P' AND name = 'sp_DeleteCandidate')
    DROP PROCEDURE sp_DeleteCandidate
GO

CREATE PROCEDURE sp_DeleteCandidate
    @CandidateId UNIQUEIDENTIFIER
AS
BEGIN
    DELETE FROM candidates
    WHERE candidate_id = @CandidateId

    SELECT @@ROWCOUNT AS RowsAffected
END
GO
IF EXISTS (SELECT * FROM sys.objects WHERE type = 'P' AND name = 'sp_GetCandidatesByCollaboratorId')
    DROP PROCEDURE sp_GetCandidatesByCollaboratorId
GO
create PROCEDURE [dbo].[sp_GetCandidatesByCollaboratorId]
    @CollaboratorId UNIQUEIDENTIFIER
AS
BEGIN
    SELECT c.*, ctv.*
    FROM Candidates c
    LEFT JOIN Collaborators ctv ON c.collaborator_id = ctv.collaborator_id
    WHERE c.collaborator_id = @CollaboratorId
END
