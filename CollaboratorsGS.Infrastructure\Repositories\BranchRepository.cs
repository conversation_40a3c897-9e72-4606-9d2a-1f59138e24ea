using CollaboratorsGS.Domain.Entities;
using CollaboratorsGS.Domain.Repositories;
using CollaboratorsGS.Infrastructure.Data;
using Dapper;
using System.Data;

namespace CollaboratorsGS.Infrastructure.Repositories
{
    public class BranchRepository : IBranchRepository
    {
        private readonly IConnectionFactory _connectionFactory;

        public BranchRepository(IConnectionFactory connectionFactory)
        {
            _connectionFactory = connectionFactory;
        }

        public async Task<Branch?> GetByIdAsync(Guid branchId)
        {
            using var connection = _connectionFactory.CreateConnection();

            var parameters = new DynamicParameters();
            parameters.Add("@BranchId", branchId, DbType.Guid);

            return await connection.QuerySingleOrDefaultAsync<Branch>(
                "sp_GetBranchById",
                parameters,
                commandType: CommandType.StoredProcedure);
        }

        public async Task<IEnumerable<Branch>> GetAllAsync()
        {
            using var connection = _connectionFactory.CreateConnection();

            return await connection.QueryAsync<Branch>(
                "sp_GetAllBranches",
                commandType: CommandType.StoredProcedure);
        }

        public async Task<IEnumerable<Branch>> GetByCompanyAsync(Guid companyId)
        {
            using var connection = _connectionFactory.CreateConnection();

            var parameters = new DynamicParameters();
            parameters.Add("@CompanyId", companyId, DbType.Guid);

            return await connection.QueryAsync<Branch>(
                "sp_GetBranchesByCompany",
                parameters,
                commandType: CommandType.StoredProcedure);
        }

        public async Task<Guid> CreateAsync(Branch branch)
        {
            using var connection = _connectionFactory.CreateConnection();

            // Generate a new UUID if not provided
            if (branch.BranchId == Guid.Empty)
            {
                branch.BranchId = Guid.NewGuid();
            }

            var parameters = new DynamicParameters();
            parameters.Add("@BranchId", branch.BranchId, DbType.Guid);
            parameters.Add("@CompanyId", branch.CompanyId, DbType.Guid);
            parameters.Add("@BranchName", branch.BranchName, DbType.String);
            parameters.Add("@PhoneNumber", branch.PhoneNumber, DbType.String);
            parameters.Add("@Email", branch.Email, DbType.String);

            return await connection.ExecuteScalarAsync<Guid>(
                "sp_CreateBranch",
                parameters,
                commandType: CommandType.StoredProcedure);
        }

        public async Task<bool> UpdateAsync(Branch branch)
        {
            using var connection = _connectionFactory.CreateConnection();

            var parameters = new DynamicParameters();
            parameters.Add("@BranchId", branch.BranchId, DbType.Guid);
            parameters.Add("@CompanyId", branch.CompanyId, DbType.Guid);
            parameters.Add("@BranchName", branch.BranchName, DbType.String);
            parameters.Add("@PhoneNumber", branch.PhoneNumber, DbType.String);
            parameters.Add("@Email", branch.Email, DbType.String);

            var affectedRows = await connection.ExecuteScalarAsync<int>(
                "sp_UpdateBranch",
                parameters,
                commandType: CommandType.StoredProcedure);

            return affectedRows > 0;
        }

        public async Task<bool> DeleteAsync(Guid branchId)
        {
            using var connection = _connectionFactory.CreateConnection();

            var parameters = new DynamicParameters();
            parameters.Add("@BranchId", branchId, DbType.Guid);

            try
            {
                var affectedRows = await connection.ExecuteScalarAsync<int>(
                    "sp_DeleteBranch",
                    parameters,
                    commandType: CommandType.StoredProcedure);

                return affectedRows > 0;
            }
            catch
            {
                return false;
            }
        }
    }
}
