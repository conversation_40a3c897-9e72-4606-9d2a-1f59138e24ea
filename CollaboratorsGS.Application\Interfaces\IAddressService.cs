using CollaboratorsGS.Application.DTOs.AddressDto;
using CollaboratorsGS.Domain.Entities;

namespace CollaboratorsGS.Application.Interfaces
{
    public interface IAddressService
    {
        Task<IEnumerable<ProvinceDto>> GetAllProvincesAsync(bool isRestructure);
        Task<IEnumerable<DistrictDto>> GetDistrictsByProvinceNameAsync(string queryCode);
        Task<IEnumerable<WardDto>> GetWardsByDictricstNameAsync(string queryCode, bool isRestructure);
    }
}