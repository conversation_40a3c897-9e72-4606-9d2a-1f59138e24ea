-- Stored Procedures for Contract operations

-- Get Contract by ID
IF EXISTS (SELECT * FROM sys.objects WHERE type = 'P' AND name = 'sp_GetContractById')
    DROP PROCEDURE sp_GetContractById
GO

CREATE PROCEDURE sp_GetContractById
    @ContractId UNIQUEIDENTIFIER
AS
BEGIN
    SELECT c.*, ctv.full_name as collaborator_name
    FROM contracts c
    INNER JOIN collaborators ctv ON c.collaborator_id = ctv.collaborator_id
    WHERE c.contract_id = @ContractId
END
GO

-- Get Contracts by CollaboratorId
IF EXISTS (SELECT * FROM sys.objects WHERE type = 'P' AND name = 'sp_GetContractsByCollaboratorId')
    DROP PROCEDURE sp_GetContractsByCollaboratorId
GO

CREATE PROCEDURE sp_GetContractsByCollaboratorId
    @CollaboratorId UNIQUEIDENTIFIER
AS
BEGIN
    SELECT c.*, ctv.full_name as collaborator_name
    FROM contracts c
    INNER JOIN collaborators ctv ON c.collaborator_id = ctv.collaborator_id
    WHERE c.collaborator_id = @CollaboratorId
    ORDER BY c.created_at DESC
END
GO

-- Get Contracts by Status
IF EXISTS (SELECT * FROM sys.objects WHERE type = 'P' AND name = 'sp_GetContractsByStatus')
    DROP PROCEDURE sp_GetContractsByStatus
GO

CREATE PROCEDURE sp_GetContractsByStatus
    @Status VARCHAR(50)
AS
BEGIN
    SELECT c.*, ctv.full_name as collaborator_name
    FROM contracts c
    INNER JOIN collaborators ctv ON c.collaborator_id = ctv.collaborator_id
    WHERE c.status = @Status
    ORDER BY c.created_at DESC
END
GO

-- Create Contract
IF EXISTS (SELECT * FROM sys.objects WHERE type = 'P' AND name = 'sp_CreateContract')
    DROP PROCEDURE sp_CreateContract
GO

CREATE PROCEDURE sp_CreateContract
    @ContractId UNIQUEIDENTIFIER,
    @CollaboratorId UNIQUEIDENTIFIER,
    @ContractContent NVARCHAR(MAX),
    @Status VARCHAR(50),
    @SignedAt DATETIME = NULL,
    @CreatedAt DATETIME,
    @UpdatedAt DATETIME = NULL
AS
BEGIN
    INSERT INTO contracts (
        contract_id, collaborator_id, contract_content, status,
        signed_at, created_at, updated_at
    )
    VALUES (
        @ContractId, @CollaboratorId, @ContractContent, @Status,
        @SignedAt, @CreatedAt, @UpdatedAt
    )
END
GO

-- Update Contract
IF EXISTS (SELECT * FROM sys.objects WHERE type = 'P' AND name = 'sp_UpdateContract')
    DROP PROCEDURE sp_UpdateContract
GO

CREATE PROCEDURE sp_UpdateContract
    @ContractId UNIQUEIDENTIFIER,
    @ContractContent NVARCHAR(MAX) = NULL,
    @Status VARCHAR(50) = NULL,
    @SignedAt DATETIME = NULL,
    @UpdatedAt DATETIME
AS
BEGIN
    UPDATE contracts
    SET contract_content = ISNULL(@ContractContent, contract_content),
        status = ISNULL(@Status, status),
        signed_at = @SignedAt,
        updated_at = @UpdatedAt
    WHERE contract_id = @ContractId
END
GO

-- Delete Contract
IF EXISTS (SELECT * FROM sys.objects WHERE type = 'P' AND name = 'sp_DeleteContract')
    DROP PROCEDURE sp_DeleteContract
GO

CREATE PROCEDURE sp_DeleteContract
    @ContractId UNIQUEIDENTIFIER
AS
BEGIN
    DELETE FROM contracts
    WHERE contract_id = @ContractId
END
GO
