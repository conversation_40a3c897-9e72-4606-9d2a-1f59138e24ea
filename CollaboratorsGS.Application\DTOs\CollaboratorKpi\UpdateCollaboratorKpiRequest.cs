using System;
using System.ComponentModel.DataAnnotations;

namespace CollaboratorsGS.Application.DTOs.CollaboratorKpi
{
    public class UpdateCollaboratorKpiRequest
    {
        [Required]
        public Guid KpiId { get; set; }
        
        public int? TotalCandidatesImported { get; set; }
        
        public int? TotalCandidatesPassedRound1 { get; set; }
        
        public int? TotalCandidatesPassedRound2 { get; set; }
        
        public int? TotalCandidatesOnboarded { get; set; }
        
        public int? TotalCandidatesFailed { get; set; }
        
        public int? TotalCandidatesOnboardedWarranty { get; set; }
        
        public float? SuccessRate { get; set; }
    }
}
