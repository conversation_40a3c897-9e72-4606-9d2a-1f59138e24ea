using System;

namespace CollaboratorsGS.Application.DTOs.Contract
{
    public class ContractDto
    {
        public Guid ContractId { get; set; }
        public Guid CtvId { get; set; }
        public string CollaboratorName { get; set; } = string.Empty;
        public string ContractContent { get; set; } = string.Empty;
        public string Status { get; set; } = string.Empty;
        public DateTime? SignedAt { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime? UpdatedAt { get; set; }
    }
}
