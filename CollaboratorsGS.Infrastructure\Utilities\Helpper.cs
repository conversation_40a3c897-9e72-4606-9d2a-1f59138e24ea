using CollaboratorsGS.Application.DTOs;

namespace CollaboratorsGS.Infrastructure.Utilities;

public static class BusinessValidationHelper
{
    public static ErrorDetail CheckUniqueness(bool exists, string field, string friendlyName)
    {
        if (!exists) return null;

        return new ErrorDetail
        {
            Field = field,
            ErrorCode = MessageCodes.VE6004,
            Message = $"{friendlyName} already exists"
        };
    }
}

