using System;
using System.Collections.Generic;

namespace CollaboratorsGS.Application.DTOs.CollaboratorReport
{
    public class CollaboratorDashboardReportDto
    {
        public CollaboratorBasicInfoDto CollaboratorInfo { get; set; } = new();
        public CollaboratorKpiCurrentDto CurrentKpi { get; set; } = new();
        public CollaboratorDashboardKpiTargetDto CurrentTarget { get; set; } = new();
        public CollaboratorRewardSummaryDto RewardSummary { get; set; } = new();
        public List<CollaboratorKpiHistoryDto> KpiHistory { get; set; } = new();
        public CollaboratorNextLevelDto? NextLevel { get; set; }
        public List<CollaboratorCandidateDto> Candidates { get; set; } = new();
    }

    public class CollaboratorBasicInfoDto
    {
        public Guid CollaboratorId { get; set; }
        public string CollaboratorName { get; set; } = string.Empty;
        public Guid LevelId { get; set; }
        public string LevelName { get; set; } = string.Empty;
        public float? MinKpiThreshold { get; set; }
        public float? CommissionRate { get; set; }
        public decimal? Round1Bonus { get; set; }
        public decimal? Round2Bonus { get; set; }
        public decimal? OnboardBonus { get; set; }
        public DateTime CreatedAt { get; set; }
        public string Status { get; set; } = string.Empty;
    }

    public class CollaboratorKpiCurrentDto
    {
        public Guid KpiId { get; set; }
        public Guid CollaboratorId { get; set; }
        public string Period { get; set; } = string.Empty;
        public int TotalCandidatesImported { get; set; }
        public int TotalCandidatesPassedRound1 { get; set; }
        public int TotalCandidatesPassedRound2 { get; set; }
        public int TotalCandidatesOnboarded { get; set; }
        public int TotalCandidatesFailed { get; set; }
        public int TotalCandidatesOnboardedWarranty { get; set; }
        public float? SuccessRate { get; set; }
        public DateTime CalculatedAt { get; set; }
    }

    public class CollaboratorDashboardKpiTargetDto
    {
        public Guid TargetId { get; set; }
        public Guid CollaboratorId { get; set; }
        public string Period { get; set; } = string.Empty;
        public int TargetCandidatesImported { get; set; }
        public int TargetCandidatesPassedRound1 { get; set; }
        public int TargetCandidatesOnboarded { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime? UpdatedAt { get; set; }
    }

    public class CollaboratorRewardSummaryDto
    {
        public decimal TotalPaid { get; set; }
        public decimal TotalPending { get; set; }
        public int CountPaid { get; set; }
        public int CountPending { get; set; }
    }

    public class CollaboratorKpiHistoryDto
    {
        public string Period { get; set; } = string.Empty;
        public int TotalCandidatesImported { get; set; }
        public int TotalCandidatesPassedRound1 { get; set; }
        public int TotalCandidatesPassedRound2 { get; set; }
        public int TotalCandidatesOnboarded { get; set; }
        public int TotalCandidatesFailed { get; set; }
        public int TotalCandidatesOnboardedWarranty { get; set; }
    }

    public class CollaboratorNextLevelDto
    {
        public Guid LevelId { get; set; }
        public string LevelName { get; set; } = string.Empty;
        public float? MinKpiThreshold { get; set; }
        public float? CommissionRate { get; set; }
    }

    public class CollaboratorCandidateDto
    {
        public Guid CandidateId { get; set; }
        public string FullName { get; set; } = string.Empty;
        public string? ProfilePicture { get; set; }
        public DateTime ApplicationDate { get; set; }
        public string Position { get; set; } = string.Empty;
        public string Status { get; set; } = string.Empty;
        public decimal? RewardAmount { get; set; }
        public string RewardStatus { get; set; } = string.Empty;
        public string JobTitle { get; set; } = string.Empty;
    }
}
