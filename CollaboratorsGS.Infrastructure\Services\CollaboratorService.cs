using AutoMapper;
using CollaboratorsGS.Application.DTOs.Collaborator;
using CollaboratorsGS.Application.DTOs.CollaboratorLevel;
using CollaboratorsGS.Application.Interfaces;
using CollaboratorsGS.Domain.Entities;
using CollaboratorsGS.Domain.Repositories;
using CollaboratorsGS.Infrastructure.Utilities;

namespace CollaboratorsGS.Infrastructure.Services
{
    public class CollaboratorService : ICollaboratorService
    {
        private readonly ICollaboratorRepository _collaboratorRepository;
        private readonly IUserRepository _userRepository;
        private readonly IMapper _mapper;

        public CollaboratorService(
            ICollaboratorRepository collaboratorRepository,
            IUserRepository userRepository,
            IMapper mapper)
        {
            _collaboratorRepository = collaboratorRepository;
            _userRepository = userRepository;
            _mapper = mapper;
        }

        public async Task<CollaboratorDto?> GetByIdAsync(Guid ctvId)
        {
            var collaborator = await _collaboratorRepository.GetByIdAsync(ctvId);
            if (collaborator == null)
                return null;

            var collaboratorDto = _mapper.Map<CollaboratorDto>(collaborator);

            // Map additional properties
            if (collaborator.Level != null)
            {
                collaboratorDto.LevelName = collaborator.Level.LevelName;
            }

            if (collaborator.ApprovedBy.HasValue)
            {
                var approver = await _userRepository.GetByIdAsync(collaborator.ApprovedBy.Value);
                if (approver != null)
                {
                    collaboratorDto.ApproverName = approver.FullName;
                }
            }

            // Map profile information if available
            if (collaborator.Profile != null)
            {
                collaboratorDto.CitizenId = collaborator.Profile.CitizenId;
                collaboratorDto.PermanentAddress = collaborator.Profile.PermanentAddress;
                collaboratorDto.CurrentAddress = collaborator.Profile.CurrentAddress;
                collaboratorDto.IssueAuthority = collaborator.Profile.IdIssueAuthorityExtracted;
                collaboratorDto.IssueDate = collaborator.Profile.IdIssueDateExtracted;
                collaboratorDto.BankAccountName = collaborator.Profile.BankAccountName;
                collaboratorDto.BankName = collaborator.Profile.BankName;
                collaboratorDto.BankAccountNumber = collaborator.Profile.BankAccountNumber;
            }

            // Get statistics
            if (collaborator.Candidates != null)
            {
                collaboratorDto.TotalCandidates = collaborator.Candidates.Count;
                collaboratorDto.TotalOnboarded = collaborator.Candidates
                    .Count(c => c.Applications != null && c.Applications.Any(a => a.OnboardDate.HasValue));
            }

            return collaboratorDto;
        }

        public async Task<IEnumerable<CollaboratorDto>> GetAllAsync()
        {
            var collaborators = await _collaboratorRepository.GetAllAsync();
            var collaboratorDtos = _mapper.Map<IEnumerable<CollaboratorDto>>(collaborators);

            // Enrich DTOs with additional information
            foreach (var collaboratorDto in collaboratorDtos)
            {
                var collaborator = collaborators.FirstOrDefault(c => c.CollaboratorId == collaboratorDto.CollaboratorId);
                if (collaborator == null)
                    continue;

                // Map level name
                if (collaborator.Level != null)
                {
                    collaboratorDto.LevelName = collaborator.Level.LevelName;
                }

                // Map approver name
                if (collaborator.ApprovedBy.HasValue)
                {
                    var approver = await _userRepository.GetByIdAsync(collaborator.ApprovedBy.Value);
                    if (approver != null)
                    {
                        collaboratorDto.ApproverName = approver.FullName;
                    }
                }

                // Map profile information if available
                if (collaborator.Profile != null)
                {
                    collaboratorDto.CitizenId = collaborator.Profile.CitizenId;
                    collaboratorDto.PermanentAddress = collaborator.Profile.PermanentAddress;
                    collaboratorDto.CurrentAddress = collaborator.Profile.CurrentAddress;
                    collaboratorDto.IssueAuthority = collaborator.Profile.IdIssueAuthorityExtracted;
                    collaboratorDto.IssueDate = collaborator.Profile.IdIssueDateExtracted;
                    collaboratorDto.BankAccountName = collaborator.Profile.BankAccountName;
                    collaboratorDto.BankName = collaborator.Profile.BankName;
                    collaboratorDto.BankAccountNumber = collaborator.Profile.BankAccountNumber;
                }

                // Get statistics
                if (collaborator.Candidates != null)
                {
                    collaboratorDto.TotalCandidates = collaborator.Candidates.Count;
                    collaboratorDto.TotalOnboarded = collaborator.Candidates
                        .Count(c => c.Applications != null && c.Applications.Any(a => a.OnboardDate.HasValue));
                }
            }

            return collaboratorDtos;
        }

        public async Task<CollaboratorDto?> GetByUserIdAsync(Guid userId)
        {
            var collaborator = await _collaboratorRepository.GetByUserIdAsync(userId);
            if (collaborator == null)
                return null;

            var collaboratorDto = _mapper.Map<CollaboratorDto>(collaborator);

            // Map additional properties
            if (collaborator.Level != null)
            {
                collaboratorDto.LevelName = collaborator.Level.LevelName;
            }

            if (collaborator.ApprovedBy.HasValue)
            {
                var approver = await _userRepository.GetByIdAsync(collaborator.ApprovedBy.Value);
                if (approver != null)
                {
                    collaboratorDto.ApproverName = approver.FullName;
                }
            }

            // Map profile information if available
            if (collaborator.Profile != null)
            {
                collaboratorDto.CitizenId = collaborator.Profile.CitizenId;
                collaboratorDto.PermanentAddress = collaborator.Profile.PermanentAddress;
                collaboratorDto.CurrentAddress = collaborator.Profile.CurrentAddress;
                collaboratorDto.IssueAuthority = collaborator.Profile.IdIssueAuthorityExtracted;
                collaboratorDto.IssueDate = collaborator.Profile.IdIssueDateExtracted;
                collaboratorDto.BankAccountName = collaborator.Profile.BankAccountName;
                collaboratorDto.BankName = collaborator.Profile.BankName;
                collaboratorDto.BankAccountNumber = collaborator.Profile.BankAccountNumber;
                collaboratorDto.PhotoUrl = collaborator.Profile.Avatar;
            }

            // Get statistics
            if (collaborator.Candidates != null)
            {
                collaboratorDto.TotalCandidates = collaborator.Candidates.Count;
                collaboratorDto.TotalOnboarded = collaborator.Candidates
                    .Count(c => c.Applications != null && c.Applications.Any(a => a.OnboardDate.HasValue));
            }

            return collaboratorDto;
        }

        public async Task<IEnumerable<CollaboratorDto>> GetByStatusAsync(string status)
        {
            var collaborators = await _collaboratorRepository.GetByStatusAsync(status);
            var collaboratorDtos = _mapper.Map<IEnumerable<CollaboratorDto>>(collaborators);

            // Enrich DTOs with additional information
            foreach (var collaboratorDto in collaboratorDtos)
            {
                var collaborator = collaborators.FirstOrDefault(c => c.CollaboratorId == collaboratorDto.CollaboratorId);
                if (collaborator == null)
                    continue;

                // Map level name
                if (collaborator.Level != null)
                {
                    collaboratorDto.LevelName = collaborator.Level.LevelName;
                }

                // Map approver name
                if (collaborator.ApprovedBy.HasValue)
                {
                    var approver = await _userRepository.GetByIdAsync(collaborator.ApprovedBy.Value);
                    if (approver != null)
                    {
                        collaboratorDto.ApproverName = approver.FullName;
                    }
                }

                // Map profile information if available
                if (collaborator.Profile != null)
                {
                    collaboratorDto.CitizenId = collaborator.Profile.CitizenId;
                    collaboratorDto.PermanentAddress = collaborator.Profile.PermanentAddress;
                    collaboratorDto.CurrentAddress = collaborator.Profile.CurrentAddress;
                    collaboratorDto.IssueAuthority = collaborator.Profile.IdIssueAuthorityExtracted;
                    collaboratorDto.IssueDate = collaborator.Profile.IdIssueDateExtracted;
                    collaboratorDto.BankAccountName = collaborator.Profile.BankAccountName;
                    collaboratorDto.BankName = collaborator.Profile.BankName;
                    collaboratorDto.BankAccountNumber = collaborator.Profile.BankAccountNumber;
                }

                // Get statistics
                if (collaborator.Candidates != null)
                {
                    collaboratorDto.TotalCandidates = collaborator.Candidates.Count;
                    collaboratorDto.TotalOnboarded = collaborator.Candidates
                        .Count(c => c.Applications != null && c.Applications.Any(a => a.OnboardDate.HasValue));
                }
            }

            return collaboratorDtos;
        }

        public async Task<Guid> CreateCollaboratorAsync(Guid userId, CreateCollaboratorRequest request)
        {
            // Get existing user by userId
            var user = await _userRepository.GetByIdAsync(userId)
                ?? throw new InvalidOperationException($"User with ID {userId} not found");

            // Check if collaborator already exists for this user
            var existingCollaborator = await _collaboratorRepository.GetByUserIdAsync(userId);
            if (existingCollaborator != null)
            {
                // If collaborator already exists, just return the ID
                return existingCollaborator.CollaboratorId;
            }

            // Check if collaborator with same email or phone already exists
            if (!string.IsNullOrEmpty(user.Email))
            {
                var existingByEmail = await _collaboratorRepository.GetByEmailAsync(user.Email);
                if (existingByEmail != null)
                {
                    throw new InvalidOperationException($"Collaborator with email {user.Email} already exists");
                }
            }

            if (!string.IsNullOrEmpty(user.PhoneNumber))
            {
                var existingByPhone = await _collaboratorRepository.GetByPhoneNumberAsync(user.PhoneNumber);
                if (existingByPhone != null)
                {
                    throw new InvalidOperationException($"Collaborator with phone number {user.PhoneNumber} already exists");
                }
            }

            // Get default level (Level 1)
            // In a real application, you would get this from a repository or configuration
            var defaultLevelId = Guid.Parse("8606d208-a4b7-4116-9414-0c82ccbd16ad"); // Replace with your actual default level ID

            // Create collaborator using existing user
            var collaborator = new Collaborator
            {
                UserId = user.UserId,
                FullName = user.FullName ?? string.Empty,
                PhoneNumber = user.PhoneNumber ?? string.Empty,
                Email = user.Email ?? string.Empty,
                LevelId = defaultLevelId, // Use default level
                Status = "Hoạt động", // Set status to "Hoạt động" (Active)
                CreatedAt = DateTime.UtcNow
            };

            var ctvId = await _collaboratorRepository.CreateAsync(collaborator);

            // No profile is created at this stage - only the collaborator themselves can update their profile later
            // because profile contains sensitive information like bank details

            return ctvId;
        }

        public async Task<CollaboratorDto?> GetCreatedCollaboratorAsync(Guid ctvId)
        {
            return await GetByIdAsync(ctvId);
        }

        public async Task<CollaboratorDto?> UpdateCollaboratorAsync(UpdateCollaboratorRequest request)
        {
            var collaborator = await _collaboratorRepository.GetByIdAsync(request.CollaboratorId)
                ?? throw new InvalidOperationException($"Collaborator with ID {request.CollaboratorId} not found");

            // Update collaborator
            collaborator.FullName = request.FullName;
            collaborator.PhoneNumber = request.PhoneNumber;
            collaborator.Email = request.Email;
            collaborator.LevelId = request.LevelId;
            collaborator.Status = request.Status;
            collaborator.LastLevelUpdatedAt = DateTime.UtcNow;
            collaborator.UpdatedAt = DateTime.UtcNow;

            var result = await _collaboratorRepository.UpdateAsync(collaborator);

            if (!result)
                return null;

            // Get the updated collaborator
            return await GetByIdAsync(request.CollaboratorId);
        }

        public async Task<CollaboratorDto?> UpdateCollaboratorWithValidationAsync(UpdateCollaboratorRequest request)
        {
            var collaborator = await _collaboratorRepository.GetByIdAsync(request.CollaboratorId)
                ?? throw new InvalidOperationException($"Collaborator with ID {request.CollaboratorId} not found");

            // Check if email is being changed and if it's already in use
            if (collaborator.Email != request.Email)
            {
                var existingByEmail = await _collaboratorRepository.GetByEmailAsync(request.Email);
                if (existingByEmail != null && existingByEmail.CollaboratorId != request.CollaboratorId)
                {
                    throw new InvalidOperationException($"Collaborator with email {request.Email} already exists");
                }
            }

            // Check if phone number is being changed and if it's already in use
            if (collaborator.PhoneNumber != request.PhoneNumber)
            {
                var existingByPhone = await _collaboratorRepository.GetByPhoneNumberAsync(request.PhoneNumber);
                if (existingByPhone != null && existingByPhone.CollaboratorId != request.CollaboratorId)
                {
                    throw new InvalidOperationException($"Collaborator with phone number {request.PhoneNumber} already exists");
                }
            }

            // Update collaborator
            collaborator.FullName = request.FullName;
            collaborator.PhoneNumber = request.PhoneNumber;
            collaborator.Email = request.Email;
            collaborator.LevelId = request.LevelId;
            collaborator.Status = request.Status;
            collaborator.LastLevelUpdatedAt = DateTime.UtcNow;
            collaborator.UpdatedAt = DateTime.UtcNow;

            var result = await _collaboratorRepository.UpdateAsync(collaborator);

            if (!result)
                return null;

            // Get the updated collaborator
            return await GetByIdAsync(request.CollaboratorId);
        }

        public async Task<CollaboratorProfileDto?> UpdateCollaboratorProfileAsync(Guid collaboratorId, UpdateCollaboratorProfileRequest request)
        {
            // Validate required fields
            if (string.IsNullOrWhiteSpace(request.FullName))
                throw new InvalidOperationException("Full name is required");
            if (string.IsNullOrWhiteSpace(request.PhoneNumber))
                throw new InvalidOperationException("Phone number is required");
            if (string.IsNullOrWhiteSpace(request.Email))
                throw new InvalidOperationException("Email is required");

            // Check if collaborator exists
            var collaborator = await _collaboratorRepository.GetByIdAsync(collaboratorId)
                ?? throw new InvalidOperationException($"Collaborator with ID {collaboratorId} not found");

            // Check for duplicate email (only if email is being changed)
            if (collaborator.Email != request.Email)
            {
                var existingByEmail = await _collaboratorRepository.GetByEmailAsync(request.Email);
                if (existingByEmail != null && existingByEmail.CollaboratorId != collaboratorId)
                {
                    throw new InvalidOperationException($"Email {request.Email} is already in use by another collaborator");
                }
            }

            // Check for duplicate phone number (only if phone is being changed)
            if (collaborator.PhoneNumber != request.PhoneNumber)
            {
                var existingByPhone = await _collaboratorRepository.GetByPhoneNumberAsync(request.PhoneNumber);
                if (existingByPhone != null && existingByPhone.CollaboratorId != collaboratorId)
                {
                    throw new InvalidOperationException($"Phone number {request.PhoneNumber} is already in use by another collaborator");
                }
            }

            // Update collaborator basic info
            var updateCollaboratorRequest = new UpdateCollaboratorRequest
            {
                CollaboratorId = collaboratorId,
                FullName = request.FullName,
                PhoneNumber = request.PhoneNumber,
                Email = request.Email,
                LevelId = collaborator.LevelId, // Keep existing level
                Status = collaborator.Status // Keep existing status
            };

            var updatedCollaborator = await UpdateCollaboratorAsync(updateCollaboratorRequest);
            if (updatedCollaborator == null)
            {
                throw new InvalidOperationException($"Failed to update collaborator basic info for ID: {collaboratorId}");
            }

            // Update user information as well (only if changed and not conflicting)
            var user = await _userRepository.GetByIdAsync(collaborator.UserId);
            if (user != null)
            {
                bool needsUpdate = false;

                // Update full name (no unique constraint)
                if (user.FullName != request.FullName)
                {
                    user.FullName = request.FullName;
                    needsUpdate = true;
                }

                // Update email only if changed and not conflicting
                if (user.Email != request.Email)
                {
                    var existingUserByEmail = await _userRepository.GetByEmailAsync(request.Email);
                    if (existingUserByEmail == null || existingUserByEmail.UserId == user.UserId)
                    {
                        user.Email = request.Email;
                        needsUpdate = true;
                    }
                }

                // Update phone number only if changed and not conflicting
                if (user.PhoneNumber != request.PhoneNumber)
                {
                    var existingUserByPhone = await _userRepository.GetByPhoneNumberAsync(request.PhoneNumber);
                    if (existingUserByPhone == null || existingUserByPhone.UserId == user.UserId)
                    {
                        user.PhoneNumber = request.PhoneNumber;
                        needsUpdate = true;
                    }
                }

                if (needsUpdate)
                {
                    await _userRepository.UpdateAsync(user);
                }
            }

            // Handle profile information - Check if profile exists, if not create, if exists update
            try
            {
                var existingProfile = await _collaboratorRepository.GetProfileByCtvIdAsync(collaboratorId);

                if (existingProfile == null)
                {
                    // CREATE new profile
                    var newProfile = new CollaboratorProfile
                    {
                        CollaboratorId = collaboratorId,
                        CurrentAddress = request.CurrentAddress,
                        BankName = request.BankName,
                        BankAccountNumber = request.BankAccountNumber,
                        BankAccountName = request.BankAccountName,
                        DataSource = "manual",
                        CreatedAt = DateTime.UtcNow
                    };

                    var createResult = await _collaboratorRepository.CreateProfileAsync(newProfile);
                    if (!createResult)
                    {
                        throw new InvalidOperationException($"Failed to create new profile for CollaboratorId: {collaboratorId}");
                    }
                }
                else
                {
                    // UPDATE existing profile
                    existingProfile.CurrentAddress = request.CurrentAddress;
                    existingProfile.BankName = request.BankName;
                    existingProfile.BankAccountNumber = request.BankAccountNumber;
                    existingProfile.BankAccountName = request.BankAccountName;
                    existingProfile.DataSource = "manual";
                    existingProfile.UpdatedAt = DateTime.UtcNow;

                    var updateResult = await _collaboratorRepository.UpdateProfileAsync(existingProfile);
                    if (!updateResult)
                    {
                        throw new InvalidOperationException($"Failed to update existing profile for CollaboratorId: {collaboratorId}, ProfileId: {existingProfile.ProfileId}");
                    }
                }
            }
            catch (Exception ex) when (!(ex is InvalidOperationException))
            {
                throw new InvalidOperationException($"Error processing profile for CollaboratorId: {collaboratorId}. Error: {ex.Message}", ex);
            }

            // Get the updated collaborator with all information
            var finalCollaborator = await GetByIdAsync(collaboratorId);
            if (finalCollaborator == null)
                return null;

            // Map to CollaboratorProfileDto
            return new CollaboratorProfileDto
            {
                FullName = finalCollaborator.FullName,
                PhoneNumber = finalCollaborator.PhoneNumber,
                Email = finalCollaborator.Email,
                LevelId = finalCollaborator.LevelId,
                LevelName = finalCollaborator.LevelName,
                Status = finalCollaborator.Status,
                LastLevelUpdatedAt = finalCollaborator.LastLevelUpdatedAt,
                CreatedAt = finalCollaborator.CreatedAt,
                UpdatedAt = finalCollaborator.UpdatedAt,
                CitizenId = finalCollaborator.CitizenId,
                PermanentAddress = finalCollaborator.PermanentAddress,
                CurrentAddress = finalCollaborator.CurrentAddress,
                IdIssueDateExtracted = finalCollaborator.IssueDate,
                IdIssueAuthorityExtracted = finalCollaborator.IssueAuthority,
                BankAccountName = finalCollaborator.BankAccountName,
                BankName = finalCollaborator.BankName,
                BankAccountNumber = finalCollaborator.BankAccountNumber
            };
        }

        public async Task<CollaboratorDto?> ApproveCollaboratorAsync(ApproveCollaboratorRequest request)
        {
            // Check if collaborator exists
            var collaborator = await _collaboratorRepository.GetByIdAsync(request.CtvId)
                ?? throw new InvalidOperationException($"Collaborator with ID {request.CtvId} not found");

            // No need to check status since it's not a approval status
            // Just update the approval information

            var result = await _collaboratorRepository.ApproveAsync(request.CtvId, request.ApprovedBy);

            if (!result)
                return null;

            // Get the updated collaborator
            return await GetByIdAsync(request.CtvId);
        }

        public async Task<bool> DeleteCollaboratorAsync(Guid ctvId)
        {
            // Check if collaborator exists
            _ = await _collaboratorRepository.GetByIdAsync(ctvId)
                ?? throw new InvalidOperationException($"Collaborator with ID {ctvId} not found");

            return await _collaboratorRepository.DeleteAsync(ctvId);
        }

        async Task<CollaboratorProfileDto?> ICollaboratorService.UpdateInformationAsync(Guid collaboratorId, UpdateInformationRequest request)
        {
            // This method updates personal information across multiple tables:
            // 1. collaborators table: full_name, phone_number, email
            // 2. users table: full_name, phone_number, email (sync with collaborator)
            // 3. collaborator_profiles table: banking information (current_address, bank_name, bank_account_number, account_bank_name)

            // Validate required fields
            if (string.IsNullOrWhiteSpace(request.FullName))
                throw new InvalidOperationException("Full name is required");
            if (string.IsNullOrWhiteSpace(request.PhoneNumber))
                throw new InvalidOperationException("Phone number is required");
            if (string.IsNullOrWhiteSpace(request.Email))
                throw new InvalidOperationException("Email is required");

            // Get collaborator info (includes userId for user table sync)
            var collaborator = await _collaboratorRepository.GetByIdAsync(collaboratorId)
                ?? throw new InvalidOperationException($"Collaborator with ID {collaboratorId} not found");

            // Check for duplicate email (only if email is being changed)
            if (collaborator.Email != request.Email)
            {
                var existingByEmail = await _collaboratorRepository.GetByEmailAsync(request.Email);
                if (existingByEmail != null && existingByEmail.CollaboratorId != collaboratorId)
                {
                    throw new InvalidOperationException($"Email {request.Email} is already in use by another collaborator");
                }
            }

            // Check for duplicate phone number (only if phone is being changed)
            if (collaborator.PhoneNumber != request.PhoneNumber)
            {
                var existingByPhone = await _collaboratorRepository.GetByPhoneNumberAsync(request.PhoneNumber);
                if (existingByPhone != null && existingByPhone.CollaboratorId != collaboratorId)
                {
                    throw new InvalidOperationException($"Phone number {request.PhoneNumber} is already in use by another collaborator");
                }
            }

            // Update collaborator basic info
            var updateCollaboratorRequest = new UpdateCollaboratorRequest
            {
                CollaboratorId = collaboratorId,
                FullName = request.FullName,
                PhoneNumber = request.PhoneNumber,
                Email = request.Email,
                LevelId = collaborator.LevelId, // Keep existing level
                Status = collaborator.Status // Keep existing status
            };

            // Update collaborator
            var updatedCollaborator = await UpdateCollaboratorAsync(updateCollaboratorRequest);
            if (updatedCollaborator == null)
            {
                throw new InvalidOperationException($"Failed to update collaborator basic info for ID: {collaboratorId}");
            }

            // STEP 2: Sync changes to users table (based on collaborator.UserId)
            // This ensures consistency between collaborators and users tables
            var user = await _userRepository.GetByIdAsync(collaborator.UserId);
            if (user != null)
            {
                bool needsUpdate = false;

                // Update full name (no unique constraint)
                if (user.FullName != request.FullName)
                {
                    user.FullName = request.FullName;
                    needsUpdate = true;
                }

                // Update email only if changed and not conflicting with other users
                if (user.Email != request.Email)
                {
                    var existingUserByEmail = await _userRepository.GetByEmailAsync(request.Email);
                    if (existingUserByEmail == null || existingUserByEmail.UserId == user.UserId)
                    {
                        user.Email = request.Email;
                        needsUpdate = true;
                    }
                }

                // Update phone number only if changed and not conflicting with other users
                if (user.PhoneNumber != request.PhoneNumber)
                {
                    var existingUserByPhone = await _userRepository.GetByPhoneNumberAsync(request.PhoneNumber);
                    if (existingUserByPhone == null || existingUserByPhone.UserId == user.UserId)
                    {
                        user.PhoneNumber = request.PhoneNumber;
                        needsUpdate = true;
                    }
                }

                if (needsUpdate)
                {
                    await _userRepository.UpdateAsync(user);
                }
            }

            // STEP 3: Update collaborator_profiles table for banking information
            // This table stores additional profile data that's not in the main collaborators table
            if (!string.IsNullOrEmpty(request.CurrentAddress) ||
                !string.IsNullOrEmpty(request.BankName) ||
                !string.IsNullOrEmpty(request.BankAccountNumber) ||
                !string.IsNullOrEmpty(request.BankAccountName))
            {
                // Use new method that only updates information fields (not extraction fields)
                var profileUpdated = await _collaboratorRepository.UpdateInformationProfileAsync(
                    collaboratorId,
                    request.CurrentAddress,
                    request.BankName,
                    request.BankAccountNumber,
                    request.BankAccountName
                );

                if (!profileUpdated)
                {
                    throw new InvalidOperationException("Failed to update collaborator profile information");
                }
            }

            // STEP 4: Return updated information (no profile data was updated)
            // Get the updated collaborator data from collaborators table
            var finalCollaborator = await GetByIdAsync(collaboratorId);
            if (finalCollaborator == null)
                return null;

            // Return basic collaborator info without profile data since no profile fields were provided
            return new CollaboratorProfileDto
            {
                FullName = finalCollaborator.FullName,
                PhoneNumber = finalCollaborator.PhoneNumber,
                Email = finalCollaborator.Email,
                LevelId = finalCollaborator.LevelId,
                LevelName = finalCollaborator.LevelName,
                Status = finalCollaborator.Status,
                LastLevelUpdatedAt = finalCollaborator.LastLevelUpdatedAt,
                CreatedAt = finalCollaborator.CreatedAt,
                UpdatedAt = finalCollaborator.UpdatedAt,
                CitizenId = finalCollaborator.CitizenId,
                PermanentAddress = finalCollaborator.PermanentAddress,
                CurrentAddress = finalCollaborator.CurrentAddress,
                IdIssueAuthorityExtracted = finalCollaborator.IssueAuthority,
                IdIssueDateExtracted = finalCollaborator.IssueDate,
                BankAccountName = finalCollaborator.BankAccountName,
                BankName = finalCollaborator.BankName,
                BankAccountNumber = finalCollaborator.BankAccountNumber
            };
        }

        public async Task<CollaboratorProfileDto?> GetCollaboratorProfileAsync(Guid collaboratorId)
        {
            var collaborator = await GetByIdAsync(collaboratorId);
            if (collaborator == null)
            {
                return null;
            }

            // Get profile information for additional fields
            var profile = await _collaboratorRepository.GetProfileByCtvIdAsync(collaboratorId);

            // Map to CollaboratorProfileDto
            return new CollaboratorProfileDto
            {
                FullName = collaborator.FullName,
                PhoneNumber = collaborator.PhoneNumber,
                Email = collaborator.Email,
                LevelId = collaborator.LevelId,
                LevelName = collaborator.LevelName,
                Status = collaborator.Status,
                LastLevelUpdatedAt = collaborator.LastLevelUpdatedAt,
                CreatedAt = collaborator.CreatedAt,
                UpdatedAt = collaborator.UpdatedAt,
                CitizenId = collaborator.CitizenId,
                PermanentAddress = collaborator.PermanentAddress,
                CurrentAddress = profile?.CurrentAddress ?? collaborator.CurrentAddress,
                BankName = profile?.BankName ?? collaborator.BankName,
                BankAccountNumber = profile?.BankAccountNumber ?? collaborator.BankAccountNumber,
                BankAccountName = profile?.BankAccountName,

                // Avatar URL from profile
                Avatar = profile?.Avatar,

                // New fields from profile
                CitizenIdExtracted = profile?.CitizenIdExtracted,
                FullNameExtracted = profile?.FullNameExtracted,
                DateOfBirthExtracted = profile?.DateOfBirthExtracted,
                GenderExtracted = profile?.GenderExtracted,
                AddressExtracted = profile?.AddressExtracted,
                PersonalIdentificationExtracted = profile?.PersonalIdentificationExtracted,
                IdIssueDateExtracted = profile?.IdIssueDateExtracted,
                IdIssueAuthorityExtracted = profile?.IdIssueAuthorityExtracted,
                ExtractionTimestamp = profile?.ExtractionTimestamp,
                DataSource = profile?.DataSource
            };
        }

        public async Task<CollaboratorProfileDto?> UpdateProfileFromExtractionAsync(Guid collaboratorId, UpdateProfileFromExtractionRequest request)
        {
            // Validate collaborator exists
            var collaborator = await _collaboratorRepository.GetByIdAsync(collaboratorId)
                ?? throw new InvalidOperationException($"Collaborator with ID {collaboratorId} not found");

            var info = request.Info;

            // Use new repository method for extraction
            var success = await _collaboratorRepository.CreateOrUpdateProfileFromExtractionAsync(
                collaboratorId,
                info.CitizenId,
                info.FullName,
                info.DateOfBirth, // "13/01/2001"
                info.Gender, // This should be "sex" from request
                info.Address,
                info.PersonalIdentification,
                info.DateIssue, // "12/02/2023"
                info.IssueAuthority,
                System.Text.Json.JsonSerializer.Serialize(request)
            );

            if (!success)
            {
                throw new InvalidOperationException("Failed to update profile with extraction data");
            }

            // Return updated profile
            return await GetCollaboratorProfileAsync(collaboratorId);
        }

        public async Task UpdateAvatarAsync(Guid collaboratorId, string avatarUrl)
        {
            // Update avatar in collaborator profile
            await _collaboratorRepository.UpdateAvatarAsync(collaboratorId, avatarUrl);
        }

        public async Task<IEnumerable<ConbutritorReferalHistoryDto>?> GetReferalHistoryAsync(Guid userId)
        {
            var results = await _collaboratorRepository.GetReferalHistoryAsync(userId);

            if (results == null)
                return null;

            return results.Select(r => new ConbutritorReferalHistoryDto
            {
                CandidateId = r.candidate_id,
                FullNameCandidate = r.full_name_candidate,
                PhoneNumber = r.phone_number,
                ApplicationDate = r.application_date ?? DateTime.MinValue,
                ProfilePictureUrl = r.profile_picture_url,
                DocumentType = r.document_type,
                FileType = r.file_type,
                Status = r.status
            });
        }

        public async Task<CollaboratorLevelDetailDto> GetLevelDetailByUserIdAsync(Guid userId)
        {
            var result = await _collaboratorRepository.GetLevelDetailByUserIdAsync(userId);
            var data = (dynamic)result;

            var currentLevel = data.CurrentLevel;
            var benefits = data.Benefits as IEnumerable<dynamic>;
            var requirements = data.Requirements as IEnumerable<dynamic>;
            var nextLevel = data.NextLevel;
            var levelHistory = data.LevelHistory as IEnumerable<dynamic>;

            return new CollaboratorLevelDetailDto
            {
                CurrentLevel = new CollaboratorCurrentLevelDto
                {
                    LevelId = currentLevel?.level_id ?? Guid.Empty,
                    LevelName = currentLevel?.level_name ?? string.Empty,
                    LevelNumber = Convert.ToInt32(currentLevel?.level_number ?? 1),
                    Description = currentLevel?.description ?? string.Empty,
                    AchievedDate = currentLevel?.achieved_date ?? DateTime.MinValue,
                    StarRating = Convert.ToInt32(currentLevel?.star_rating ?? 1)
                },
                Benefits = benefits?.Select(b => new CollaboratorLevelBenefitDto
                {
                    Title = b.title ?? string.Empty,
                    Description = b.description ?? string.Empty,
                    IsActive = Convert.ToBoolean(b.is_active ?? false)
                }).ToList() ?? [],
                Requirements = requirements?.Select(r => new CollaboratorLevelRequirementDto
                {
                    Title = r.title ?? string.Empty,
                    Description = r.description ?? string.Empty,
                    CurrentValue = Convert.ToInt32(r.current_value ?? 0),
                    RequiredValue = Convert.ToInt32(r.required_value ?? 0),
                    ProgressPercentage = Convert.ToDecimal(r.progress_percentage ?? 0),
                    IsMet = Convert.ToBoolean(r.is_met ?? false)
                }).ToList() ?? [],
                NextLevel = nextLevel != null ? new CollaboratorNextLevelDto
                {
                    LevelId = nextLevel?.level_id ?? Guid.Empty,
                    LevelName = nextLevel?.level_name ?? string.Empty,
                    LevelNumber = Convert.ToInt32(nextLevel?.level_number ?? 1),
                    Description = nextLevel?.description ?? string.Empty,
                    Benefits = [], // Will be populated from object_data
                    CanUpgrade = Convert.ToBoolean(nextLevel?.can_upgrade ?? false)
                } : null,
                LevelHistory = levelHistory?.Select(h => new CollaboratorLevelHistoryDto
                {
                    LevelName = h.level_name ?? string.Empty,
                    AchievedDate = h.achieved_date ?? DateTime.MinValue,
                    Status = h.status ?? string.Empty
                }).ToList() ?? [],
                CanUpgrade = nextLevel != null && Convert.ToBoolean(nextLevel?.can_upgrade ?? false)
            };
        }

        public async Task<LevelUpgradeCheckDto> CheckLevelUpgradeEligibilityAsync(Guid userId)
        {
            var result = await _collaboratorRepository.CheckLevelUpgradeEligibilityAsync(userId);

            if (result == null)
            {
                return new LevelUpgradeCheckDto
                {
                    CanUpgrade = false,
                    Message = MessageCodes.ER4004
                };
            }

            return new LevelUpgradeCheckDto
            {
                CanUpgrade = Convert.ToBoolean(result.can_upgrade ?? false),
                Message = result.message?.ToString() ?? string.Empty
            };
        }

        public Task<Guid?> GetByUserId(Guid userId)
        {

            var collaboratorId = _collaboratorRepository.GetByUserId(userId);
            if (collaboratorId == null)
                throw new Exception("Collaborator not found");
            return collaboratorId;
        }
    }
}
