-- Stored Procedure to get detailed level information for collaborator
IF EXISTS (SELECT * FROM sys.objects WHERE type = 'P' AND name = 'sp_GetCollaboratorLevelDetail')
    DROP PROCEDURE sp_GetCollaboratorLevelDetail
GO

CREATE PROCEDURE sp_GetCollaboratorLevelDetail
    @UserId UNIQUEIDENTIFIER
AS
BEGIN
    DECLARE @CollaboratorId UNIQUEIDENTIFIER
    DECLARE @CurrentLevelId UNIQUEIDENTIFIER
    DECLARE @CurrentPeriod NVARCHAR(7) = FORMAT(GETDATE(), 'yyyy-MM')

    -- Get collaborator ID and current level from user ID
    SELECT 
        @CollaboratorId = c.collaborator_id,
        @CurrentLevelId = c.level_id
    FROM collaborators c 
    WHERE c.user_id = @UserId

    IF @CollaboratorId IS NULL
    BEGIN
        -- Return empty result sets if collaborator not found
        SELECT
            NEWID() as level_id,
            'Level 1' as level_name,
            1 as level_number,
            'Cộng tác viên mới' as description,
            GETDATE() as achieved_date,
            1 as star_rating
        WHERE 1=0

        SELECT
            'Benefit' as title,
            'Description' as description,
            0 as is_active
        WHERE 1=0

        SELECT
            'Requirement' as title,
            'Description' as description,
            0 as current_value,
            0 as required_value,
            0.0 as progress_percentage,
            0 as is_met
        WHERE 1=0

        SELECT 
            NEWID() as level_id,
            'Next Level' as level_name,
            2 as level_number,
            'Description' as description,
            0 as can_upgrade
        WHERE 1=0

        SELECT
            'Level' as level_name,
            GETDATE() as achieved_date,
            'Previous' as status
        WHERE 1=0

        RETURN
    END

    -- 1. Current Level Information
    SELECT
        cl.level_id,
        cl.level_name,
        CASE
            WHEN cl.level_name LIKE '%1%' THEN 1
            WHEN cl.level_name LIKE '%2%' THEN 2
            WHEN cl.level_name LIKE '%3%' THEN 3
            ELSE 1
        END as level_number,
        cl.description,
        COALESCE(c.last_level_updated_at, c.created_at) as achieved_date,
        CASE
            WHEN cl.level_name LIKE '%1%' THEN 1
            WHEN cl.level_name LIKE '%2%' THEN 2
            WHEN cl.level_name LIKE '%3%' THEN 3
            ELSE 1
        END as star_rating
    FROM collaborators c
    INNER JOIN collaborator_levels cl ON c.level_id = cl.level_id
    WHERE c.collaborator_id = @CollaboratorId

    -- 2. Level Benefits (from object_data table)
    SELECT
        CASE
            WHEN od.object_code = 'level1' THEN N'Quyền lợi Level 1'
            WHEN od.object_code = 'level2' THEN N'Quyền lợi Level 2'
            WHEN od.object_code = 'level3' THEN N'Quyền lợi Level 3'
            ELSE N'Quyền lợi'
        END as title,
        od.description as description,
        CASE
            WHEN cl.level_name LIKE '%' + SUBSTRING(od.object_code, 6, 1) + '%' THEN 1
            ELSE 0
        END as is_active
    FROM object_data od
    CROSS JOIN collaborators c
    INNER JOIN collaborator_levels cl ON c.level_id = cl.level_id
    WHERE od.object_type = 'level'
        AND c.collaborator_id = @CollaboratorId
        AND od.object_code IN ('level1', 'level2', 'level3')
        AND od.description IS NOT NULL
        AND od.description != ''

    -- 3. Requirements for Next Level
    DECLARE @NextLevelNumber INT
    DECLARE @CurrentLevelNumber INT
    
    SELECT @CurrentLevelNumber = CASE 
        WHEN cl.level_name LIKE '%1%' THEN 1
        WHEN cl.level_name LIKE '%2%' THEN 2
        WHEN cl.level_name LIKE '%3%' THEN 3
        ELSE 1
    END
    FROM collaborators c
    INNER JOIN collaborator_levels cl ON c.level_id = cl.level_id
    WHERE c.collaborator_id = @CollaboratorId

    -- 3. Determine next level name (sơ cấp → trung cấp → cao cấp)
    DECLARE @NextLevelName NVARCHAR(50)

    SELECT @NextLevelName = CASE
        WHEN cl.level_name LIKE N'%sơ cấp%' OR cl.level_name LIKE N'%Level 1%' THEN N'trung cấp'
        WHEN cl.level_name LIKE N'%trung cấp%' OR cl.level_name LIKE N'%Level 2%' THEN N'cao cấp'
        ELSE NULL -- Already at highest level
    END
    FROM collaborators c
    INNER JOIN collaborator_levels cl ON c.level_id = cl.level_id
    WHERE c.collaborator_id = @CollaboratorId

    -- 4. Requirements for Next Level
    IF @NextLevelName IS NOT NULL
    BEGIN
        SELECT
            CASE
                WHEN @NextLevelName = N'trung cấp' THEN N'Onboard thành công 10 ứng viên'
                WHEN @NextLevelName = N'cao cấp' THEN N'Onboard thành công 20 ứng viên'
                ELSE N'Hoàn thành yêu cầu KPI'
            END as title,
            CASE
                WHEN @NextLevelName = N'trung cấp' THEN N'Cần onboard đủ 10 ứng viên để lên cấp trung cấp'
                WHEN @NextLevelName = N'cao cấp' THEN N'Cần onboard đủ 20 ứng viên để lên cấp cao cấp'
                ELSE N'Hoàn thành các yêu cầu KPI để lên cấp'
            END as description,
            COALESCE(kpi.total_candidates_onboarded, 0) as current_value,
            CASE
                WHEN @NextLevelName = N'trung cấp' THEN 10
                WHEN @NextLevelName = N'cao cấp' THEN 20
                ELSE 10
            END as required_value,
            CASE
                WHEN @NextLevelName = N'trung cấp' THEN
                    CASE WHEN COALESCE(kpi.total_candidates_onboarded, 0) >= 10 THEN 100.0
                         ELSE CAST(COALESCE(kpi.total_candidates_onboarded, 0) AS FLOAT) / 10 * 100 END
                WHEN @NextLevelName = N'cao cấp' THEN
                    CASE WHEN COALESCE(kpi.total_candidates_onboarded, 0) >= 20 THEN 100.0
                         ELSE CAST(COALESCE(kpi.total_candidates_onboarded, 0) AS FLOAT) / 20 * 100 END
                ELSE 0.0
            END as progress_percentage,
            CASE
                WHEN @NextLevelName = N'trung cấp' AND COALESCE(kpi.total_candidates_onboarded, 0) >= 10 THEN 1
                WHEN @NextLevelName = N'cao cấp' AND COALESCE(kpi.total_candidates_onboarded, 0) >= 20 THEN 1
                ELSE 0
            END as is_met
        FROM collaborators c
        LEFT JOIN collaborator_kpis kpi ON c.collaborator_id = kpi.collaborator_id
            AND kpi.period = @CurrentPeriod
        WHERE c.collaborator_id = @CollaboratorId
    END
    ELSE
    BEGIN
        -- No requirements if already at max level
        SELECT
            N'Đã đạt cấp độ cao nhất' as title,
            N'Bạn đã đạt cấp độ cao nhất trong hệ thống' as description,
            1 as current_value,
            1 as required_value,
            100.0 as progress_percentage,
            1 as is_met
        WHERE 1=0 -- Return empty for max level
    END

    -- 5. Next Level Information
    DECLARE @CanUpgrade BIT = 0

    IF @NextLevelName IS NOT NULL
    BEGIN
        -- Check if can upgrade to next level
        SELECT @CanUpgrade = CASE
            WHEN @NextLevelName = N'trung cấp' AND EXISTS (
                SELECT 1 FROM collaborator_kpis kpi
                WHERE kpi.collaborator_id = @CollaboratorId
                    AND kpi.period = @CurrentPeriod
                    AND kpi.total_candidates_onboarded >= 10
            ) THEN 1
            WHEN @NextLevelName = N'cao cấp' AND EXISTS (
                SELECT 1 FROM collaborator_kpis kpi
                WHERE kpi.collaborator_id = @CollaboratorId
                    AND kpi.period = @CurrentPeriod
                    AND kpi.total_candidates_onboarded >= 20
            ) THEN 1
            ELSE 0
        END

        -- Return next level information
        SELECT
            COALESCE(nl.level_id, NEWID()) as level_id,
            COALESCE(nl.level_name, @NextLevelName) as level_name,
            CASE
                WHEN @NextLevelName = N'trung cấp' THEN 2
                WHEN @NextLevelName = N'cao cấp' THEN 3
                ELSE 1
            END as level_number,
            COALESCE(nl.description,
                CASE
                    WHEN @NextLevelName = N'trung cấp' THEN N'Cộng tác viên trung cấp với nhiều quyền lợi hơn'
                    WHEN @NextLevelName = N'cao cấp' THEN N'Cộng tác viên cao cấp với quyền lợi tối đa'
                    ELSE N'Cấp độ tiếp theo'
                END
            ) as description,
            @CanUpgrade as can_upgrade
        FROM collaborator_levels nl
        WHERE nl.level_name LIKE '%' + @NextLevelName + '%'
           OR nl.level_name LIKE '%Level ' + CAST(CASE WHEN @NextLevelName = N'trung cấp' THEN 2 ELSE 3 END AS VARCHAR) + '%'
    END
    ELSE
    BEGIN
        -- Already at max level
        SELECT
            NEWID() as level_id,
            N'Đã đạt cấp độ cao nhất' as level_name,
            3 as level_number,
            N'Bạn đã đạt cấp độ cao nhất trong hệ thống' as description,
            0 as can_upgrade
        WHERE 1=0 -- Return empty result for max level
    END

    -- 5. Level History (simplified to avoid duplicates)
    SELECT
        cl.level_name,
        COALESCE(c.last_level_updated_at, c.created_at) as achieved_date,
        'Current' as status
    FROM collaborators c
    INNER JOIN collaborator_levels cl ON c.level_id = cl.level_id
    WHERE c.collaborator_id = @CollaboratorId
    ORDER BY achieved_date DESC
END
GO
