namespace CollaboratorsGS.Application.DTOs
{
    public class CvScanResponse
    {
        public bool IsSuccess { get; set; }
        public string Message { get; set; } = string.Empty;
        public int StatusCode { get; set; }

        public string? FieldDuplicate { get; set; }
        public CandidateInfo? CandidateInfo { get; set; }
    }

    public class CandidateInfo
    {
        public string FullName { get; set; } = string.Empty;
        public string Email { get; set; } = string.Empty;
        public string PhoneNumber { get; set; } = string.Empty;
        public string Education { get; set; } = string.Empty;
        public string WorkExperience { get; set; } = string.Empty;
        public string Skills { get; set; } = string.Empty;
    }
}