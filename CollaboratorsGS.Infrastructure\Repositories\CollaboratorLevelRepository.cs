using CollaboratorsGS.Domain.Entities;
using CollaboratorsGS.Domain.Repositories;
using CollaboratorsGS.Infrastructure.Data;
using Dapper;
using System.Data;

namespace CollaboratorsGS.Infrastructure.Repositories
{
    public class CollaboratorLevelRepository : ICollaboratorLevelRepository
    {
        private readonly IConnectionFactory _connectionFactory;

        public CollaboratorLevelRepository(IConnectionFactory connectionFactory)
        {
            _connectionFactory = connectionFactory;
        }

        public async Task<CollaboratorLevel?> GetByIdAsync(Guid levelId)
        {
            using var connection = _connectionFactory.CreateConnection();

            return await connection.QueryFirstOrDefaultAsync<CollaboratorLevel>(
                "sp_GetCollaboratorLevelById",
                new { LevelId = levelId },
                commandType: CommandType.StoredProcedure
            );
        }

         public async Task<CollaboratorLevel?> GetByNameAsync(string name)
        {
            using var connection = _connectionFactory.CreateConnection();

            return await connection.QueryFirstOrDefaultAsync<CollaboratorLevel>(
                "sp_GetCollaboratorLevelByName",
                new { name },
                commandType: CommandType.StoredProcedure
            );
        }

        public async Task<IEnumerable<CollaboratorLevel>> GetAllAsync()
        {
            using var connection = _connectionFactory.CreateConnection();

            return await connection.QueryAsync<CollaboratorLevel>(
                "sp_GetAllCollaboratorLevels",
                commandType: CommandType.StoredProcedure
            );
        }

        public async Task<Guid> CreateAsync(CollaboratorLevel level)
        {
            // Generate a new UUID if not provided
            if (level.LevelId == Guid.Empty)
            {
                level.LevelId = Guid.NewGuid();
            }

            using var connection = _connectionFactory.CreateConnection();

            var parameters = new
            {
                level.LevelId,
                level.LevelName,
                level.MinKpiThreshold,
                level.CommissionRate,
                level.Round1Bonus,
                level.Round2Bonus,
                level.OnboardBonus,
                level.Description
            };

            await connection.ExecuteScalarAsync<int>(
                "sp_CreateCollaboratorLevel",
                parameters,
                commandType: CommandType.StoredProcedure
            );

            return level.LevelId;
        }

        public async Task<bool> UpdateAsync(CollaboratorLevel level)
        {
            using var connection = _connectionFactory.CreateConnection();

            var parameters = new
            {
                level.LevelId,
                level.LevelName,
                level.MinKpiThreshold,
                level.CommissionRate,
                level.Round1Bonus,
                level.Round2Bonus,
                level.OnboardBonus,
                level.Description
            };

            var result = await connection.ExecuteScalarAsync<int>(
                "sp_UpdateCollaboratorLevel",
                parameters,
                commandType: CommandType.StoredProcedure
            );

            return result > 0;
        }

        public async Task<bool> DeleteAsync(Guid levelId)
        {
            using var connection = _connectionFactory.CreateConnection();

            var result = await connection.ExecuteScalarAsync<int>(
                "sp_DeleteCollaboratorLevel",
                new { LevelId = levelId },
                commandType: CommandType.StoredProcedure
            );

            return result > 0;
        }
    }
}
