
namespace CollaboratorsGS.Domain.Entities
{
    public class CollaboratorProfile
    {
        public Guid ProfileId { get; set; }

        public Guid CollaboratorId { get; set; }

        // Manual profile information
        public string? CitizenId { get; set; }
        public string? PermanentAddress { get; set; }
        public string? CurrentAddress { get; set; }
        public string? BankName { get; set; }
        public string? BankAccountNumber { get; set; }
        public string? BankAccountName { get; set; } // Bank account owner name

        // Avatar URL
        public string? Avatar { get; set; }

        // Extracted citizen ID information
        public string? CitizenIdExtracted { get; set; }
        public string? FullNameExtracted { get; set; }
        public DateTime? DateOfBirthExtracted { get; set; }
        public string? GenderExtracted { get; set; }
        public string? AddressExtracted { get; set; }
        public string? PersonalIdentificationExtracted { get; set; }
        public DateTime? IdIssueDateExtracted { get; set; }
        public string? IdIssueAuthorityExtracted { get; set; }

        // Extraction metadata
        public string? ExtractionRawData { get; set; } // Full JSON response from extraction API
        public DateTime? ExtractionTimestamp { get; set; } // When extraction was performed
        public string? DataSource { get; set; } // 'manual' or 'extracted'

        public DateTime CreatedAt { get; set; }
        public DateTime? UpdatedAt { get; set; }

        // Navigation properties
        public Collaborator? Collaborator { get; set; }
    }
}
