using System;
using System.ComponentModel.DataAnnotations;

namespace CollaboratorsGS.Application.DTOs.Contract
{
    public class CreateContractRequest
    {
        [Required]
        public Guid CtvId { get; set; }
        
        [Required]
        public string ContractContent { get; set; } = string.Empty;
        
        [Required]
        public string Status { get; set; } = string.Empty;
        
        public DateTime? SignedAt { get; set; }
    }
}
