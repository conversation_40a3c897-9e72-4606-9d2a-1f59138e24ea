IF EXISTS (SELECT * FROM sys.objects WHERE type = 'P' AND name = 'sp_CreateCollaborator')
    DROP PROCEDURE sp_CreateCollaborator
GO

CREATE PROCEDURE [dbo].[sp_CreateCollaborator]
    @CollaboratorId UNIQUEIDENTIFIER,
    @UserId UNIQUEIDENTIFIER,
    @FullName NVARCHAR(255),
    @PhoneNumber VARCHAR(50),
    @Email VARCHAR(255),
    @LevelId UNIQUEIDENTIFIER,
    @Status VARCHAR(50),
    @LastLevelUpdatedAt DATETIME = NULL,
    @CreatedAt DATETIME,
    @ApprovedBy UNIQUEIDENTIFIER = NULL,
    @ApprovedDate DATE = NULL,
    @UpdatedAt DATETIME = NULL
AS
BEGIN
    INSERT INTO collaborators (
        collaborator_id, user_id, full_name, phone_number, email, level_id, status, 
        last_level_updated_at, created_at, approved_by, approved_date, updated_at
    )
    VALUES (
        @CollaboratorId, @UserId, @FullName, @PhoneNumber, @Email, @LevelId, @Status, 
        @LastLevelUpdatedAt, @CreatedAt, @ApprovedBy, @ApprovedDate, @UpdatedAt
    )
END
GO

-- Get All collaborators
IF EXISTS (SELECT * FROM sys.objects WHERE type = 'P' AND name = 'sp_GetAllCollaborators')
    DROP PROCEDURE sp_GetAllCollaborators

GO

CREATE PROCEDURE [dbo].[sp_GetAllCollaborators]
AS
BEGIN
    SELECT c.*, u.*, l.*, p.*
    FROM collaborators c
    LEFT JOIN users u ON c.user_id = u.user_id
    LEFT JOIN collaborator_levels l ON c.level_id = l.level_id
    LEFT JOIN collaborator_profiles p ON c.collaborator_id = p.collaborator_id
END

GO 
-- Get Collaborator by ID
GO

IF EXISTS (SELECT * FROM sys.objects WHERE type = 'P' AND name = 'sp_GetCollaboratorById')
    DROP PROCEDURE sp_GetCollaboratorById
GO
CREATE PROCEDURE [dbo].[sp_GetCollaboratorById]
    @Collaborator_Id UNIQUEIDENTIFIER
AS
BEGIN
    SELECT c.*, u.*, l.*, p.*
    FROM collaborators c
    LEFT JOIN users u ON c.user_id = u.user_id
    LEFT JOIN collaborator_levels l ON c.level_id = l.level_id
    LEFT JOIN collaborator_profiles p ON c.collaborator_id = p.collaborator_id
    WHERE c.collaborator_id = @Collaborator_Id
END

GO
-- Get Collaborator by User ID
IF EXISTS (SELECT * FROM sys.objects WHERE type = 'P' AND name = 'sp_GetCollaboratorByUserId')
    DROP PROCEDURE sp_GetCollaboratorByUserId
GO
CREATE PROCEDURE [dbo].[sp_GetCollaboratorByUserId]
    @UserId UNIQUEIDENTIFIER
AS
BEGIN
    SELECT c.*, u.*, l.*, p.*
    FROM collaborators c
    LEFT JOIN users u ON c.user_id = u.user_id
    LEFT JOIN collaborator_levels l ON c.level_id = l.level_id
    LEFT JOIN collaborator_profiles p ON c.collaborator_id = p.collaborator_id
    WHERE c.user_id = @UserId
END
GO
--Get Collaborator by PhoneNumber
IF EXISTS (SELECT * FROM sys.objects WHERE type = 'P' AND name = 'sp_GetCollaboratorByPhoneNumber')
    DROP PROCEDURE sp_GetCollaboratorByPhoneNumber
GO
CREATE PROCEDURE [dbo].[sp_GetCollaboratorByPhoneNumber]
    @PhoneNumber VARCHAR(50)
AS
BEGIN
    SELECT c.*, u.*, l.*, p.*
    FROM collaborators c
    LEFT JOIN users u ON c.user_id = u.user_id
    LEFT JOIN collaborator_levels l ON c.level_id = l.level_id
    LEFT JOIN collaborator_profiles p ON c.collaborator_id = p.collaborator_id
    WHERE c.phone_number = @PhoneNumber
END
GO
--Get Collaborator by Email
IF EXISTS (SELECT * FROM sys.objects WHERE type = 'P' AND name = 'sp_GetCollaboratorByEmail')
    DROP PROCEDURE sp_GetCollaboratorByEmail
GO
CREATE PROCEDURE [dbo].[sp_GetCollaboratorByEmail]
    @Email VARCHAR(255)
AS
BEGIN
    SELECT c.*, u.*, l.*, p.*
    FROM collaborators c
    LEFT JOIN users u ON c.user_id = u.user_id
    LEFT JOIN collaborator_levels l ON c.level_id = l.level_id
    LEFT JOIN collaborator_profiles p ON c.collaborator_id = p.collaborator_id
    WHERE c.Email = @Email
END
GO
-----------------------------------------------------------------
IF EXISTS (SELECT * FROM sys.objects WHERE type = 'P' AND name = 'sp_ApproveCollaborator')
    DROP PROCEDURE sp_ApproveCollaborator
GO
CREATE PROCEDURE [dbo].[sp_ApproveCollaborator]
    @CollaboratorId UNIQUEIDENTIFIER,
    @ApprovedBy UNIQUEIDENTIFIER,
    @ApprovedDate DATE,
    @Status VARCHAR(50),
    @UpdatedAt DATETIME
AS
BEGIN
    UPDATE collaborators
    SET approved_by = @ApprovedBy,
        approved_date = @ApprovedDate,
        status = @Status,
        updated_at = @UpdatedAt
    WHERE collaborator_id = @CollaboratorId

    SELECT @@ROWCOUNT AS RowsAffected
END
GO

-- Update Collaborator
IF EXISTS (SELECT * FROM sys.objects WHERE type = 'P' AND name = 'sp_UpdateCollaborator')
    DROP PROCEDURE sp_UpdateCollaborator
GO

CREATE PROCEDURE [dbo].[sp_UpdateCollaborator]
    @collaborator_id UNIQUEIDENTIFIER,
    @full_name NVARCHAR(255),
    @phone_number VARCHAR(50),
    @email VARCHAR(255),
    @level_id UNIQUEIDENTIFIER,
    @status VARCHAR(50),
    @last_level_updated_at DATETIME = NULL,
    @updated_at DATETIME
AS
BEGIN
    UPDATE collaborators
    SET full_name = @full_name,
        phone_number = @phone_number,
        email = @email,
        level_id = @level_id,
        status = @status,
        last_level_updated_at = @last_level_updated_at,
        updated_at = @updated_at
    WHERE collaborator_id = @collaborator_id

    SELECT @@ROWCOUNT AS RowsAffected
END
GO