
namespace CollaboratorsGS.Domain.Entities
{
    public class Authen
    {
        public Guid TokenId { get; set; }

        public Guid UserId { get; set; }    

        public string AccessToken { get; set; } = string.Empty;

        public string? RefreshToken { get; set; }

        public DateTime IssuedAt { get; set; }

        public DateTime ExpiresAt { get; set; }

    
        public string Status { get; set; } = string.Empty;

        // Navigation properties
        public User? User { get; set; }
    }
}