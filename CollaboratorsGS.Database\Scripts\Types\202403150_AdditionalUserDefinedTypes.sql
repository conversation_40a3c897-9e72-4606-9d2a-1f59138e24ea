/* Thêm các User-Defined Types cho hệ thống */

-- <PERSON><PERSON><PERSON> trạng thái hợp đồng CTV
IF NOT EXISTS (SELECT * FROM sys.types WHERE name = 'ContractStatusType' AND is_user_defined = 1)
BEGIN
    CREATE TYPE [dbo].[ContractStatusType] FROM VARCHAR(20);
END
GO

-- Loại hành động audit log
IF NOT EXISTS (SELECT * FROM sys.types WHERE name = 'AuditActionType' AND is_user_defined = 1)
BEGIN
    CREATE TYPE [dbo].[AuditActionType] FROM VARCHAR(15);
END
GO

-- <PERSON><PERSON><PERSON> thực thể cho audit
IF NOT EXISTS (SELECT * FROM sys.types WHERE name = 'EntityType' AND is_user_defined = 1)
BEGIN
    CREATE TYPE [dbo].[EntityType] FROM VARCHAR(50);
END
GO

-- <PERSON><PERSON><PERSON> phương thức tích hợp
IF NOT EXISTS (SELECT * FROM sys.types WHERE name = 'IntegrationType' AND is_user_defined = 1)
BEGIN
    CREATE TYPE [dbo].[IntegrationType] FROM VARCHAR(50);
END
GO