-- Create Candidate related tables

-- Create Candidates table
IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'candidates' AND schema_id = SCHEMA_ID('dbo'))
BEGIN
CREATE TABLE [dbo].[candidates] (
    [candidate_id] UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
    [full_name] NVARCHAR(255) NOT NULL,
    [phone_number] NVARCHAR(50) UNIQUE NOT NULL,
    [email] NVARCHAR(255) UNIQUE NULL,
    [education_level] NVARCHAR(500) NULL,
    [work_experience] NVARCHAR(500) NULL,
    [skills] NVARCHAR(500) NULL,
    [date_of_birth] DATE,
    [gender] NVARCHAR(10),
    [address] NVARCHAR(MAX),
    [profile_picture] NVARCHAR(255),
    [full_body_picture] NVARCHAR(255),
    [height_cm] INT,                        
    [weight_kg] INT,  
    [level] NVARCHAR(50),
    [source] NVARCHAR(50),
    [collaborator_id] UNIQUEIDENTIFIER,
    [citizen_id] NVARCHAR(12) NULL,
    [citizen_id_address] NVARCHAR(MAX) NULL,
    [citizen_id_issue_date] DATE NULL,
    [citizen_id_issue_place] NVARCHAR(500) NULL,
    [created_at] DATETIME NOT NULL DEFAULT GETDATE(),
    [updated_at] DATETIME,
    CONSTRAINT [FK_Candidates_Collaborator] FOREIGN KEY ([collaborator_id]) REFERENCES [dbo].[collaborators] ([collaborator_id])
)
END
GO

-- Create CandidateApplications table
IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'candidate_applications' AND schema_id = SCHEMA_ID('dbo'))
BEGIN
CREATE TABLE [dbo].[candidate_applications] (
    [application_id] UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
    [candidate_id] UNIQUEIDENTIFIER NOT NULL,
    [posting_id] UNIQUEIDENTIFIER NOT NULL,
    [application_date] DATE NOT NULL DEFAULT GETDATE(),
    [status] NVARCHAR(50) NOT NULL,
    [interview_round1_result] NVARCHAR(50),
    [interview_round1_date] DATE,
    [interview_round2_result] NVARCHAR(50),
    [interview_round2_date] DATE,
    [onboard_date] DATE,
    [warranty_end_date] DATE,
    [updated_at] DATETIME,
    CONSTRAINT [FK_CandidateApplications_Candidates] FOREIGN KEY ([candidate_id]) REFERENCES [dbo].[candidates] ([candidate_id]),
    CONSTRAINT [FK_CandidateApplications_Postings] FOREIGN KEY ([posting_id]) REFERENCES [dbo].[recruitment_postings] ([posting_id])
)
END
GO
CREATE UNIQUE INDEX UX_Candidate_Posting
ON candidate_applications (candidate_id, posting_id);
GO
-- Create CandidateEmploymentHistory table
IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'candidate_employment_history' AND schema_id = SCHEMA_ID('dbo'))
BEGIN
CREATE TABLE [dbo].[candidate_employment_history] (
    [history_id] UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
    [candidate_id] UNIQUEIDENTIFIER NOT NULL,
    [company_name] NVARCHAR(255) NOT NULL,
    [position] NVARCHAR(255) NOT NULL,
    [start_date] DATE NOT NULL,
    [end_date] DATE,
    [reason_for_leaving] NVARCHAR(MAX),
    CONSTRAINT [FK_CandidateEmploymentHistory_Candidates] FOREIGN KEY ([candidate_id]) REFERENCES [dbo].[Candidates] ([candidate_id])
);
END
GO

-- Create CandidateDocuments table
IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'candidate_documents' AND schema_id = SCHEMA_ID('dbo'))
BEGIN
CREATE TABLE [dbo].[candidate_documents] (
    [document_id] UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
    [candidate_id] UNIQUEIDENTIFIER NOT NULL,
    [document_type] NVARCHAR(50) NOT NULL,
    [file_path] NVARCHAR(255) NOT NULL,
    [file_type] NVARCHAR(50) NOT NULL,    
    [uploaded_at] DATETIME NOT NULL DEFAULT GETDATE(),
    CONSTRAINT [FK_CandidateDocuments_Candidates] FOREIGN KEY ([candidate_id]) REFERENCES [dbo].[candidates] ([candidate_id])
)
END
GO

-- Create indexes for frequently queried columns
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'ix_candidates_phone_number' AND object_id = OBJECT_ID('dbo.candidates'))
CREATE UNIQUE INDEX [ix_candidates_phone_number] ON [dbo].[candidates]([phone_number]);
GO

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'ix_candidates_email' AND object_id = OBJECT_ID('dbo.candidates'))
CREATE UNIQUE INDEX [ix_candidates_email] ON [dbo].[candidates]([email]) WHERE [email] IS NOT NULL;
GO

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'ix_candidates_citizen_id' AND object_id = OBJECT_ID('dbo.candidates'))
CREATE UNIQUE INDEX [ix_candidates_citizen_id] ON [dbo].[candidates]([citizen_id]) WHERE [citizen_id] IS NOT NULL;
GO

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'ix_candidate_applications_status' AND object_id = OBJECT_ID('dbo.candidate_applications'))
CREATE INDEX [ix_candidate_applications_status] ON [dbo].[candidate_applications]([status]);
GO

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'ix_candidate_applications_application_date' AND object_id = OBJECT_ID('dbo.candidate_applications'))
CREATE INDEX [ix_candidate_applications_application_date] ON [dbo].[candidate_applications]([application_date]);
GO

-- Add check constraints
-- Gender constraint removed as per user request

IF NOT EXISTS (SELECT * FROM sys.check_constraints WHERE name = 'chk_candidate_applications_status' AND parent_object_id = OBJECT_ID('dbo.candidate_applications'))
ALTER TABLE [dbo].[candidate_applications] ADD CONSTRAINT [chk_candidate_applications_status]
CHECK ([status] IN ('Pending', 'Interview Round 1', 'Interview Round 2', 'Passed', 'Failed', 'Onboarded', 'Onboarded Warranty'));
GO

