using CollaboratorsGS.Application.DTOs.Branch;

namespace CollaboratorsGS.Application.Interfaces
{
    public interface IBranchService
    {
        Task<BranchDto?> GetByIdAsync(Guid branchId);
        Task<IEnumerable<BranchDto>> GetAllAsync();
        Task<IEnumerable<BranchDto>> GetByCompanyAsync(Guid companyId);
        Task<BranchDto> CreateBranchAsync(CreateBranchRequest request);
        Task<BranchDto?> UpdateBranchAsync(UpdateBranchRequest request);
        Task<bool> DeleteBranchAsync(Guid branchId);
    }
}
