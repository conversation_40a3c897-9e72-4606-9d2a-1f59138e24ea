using CollaboratorsGS.Domain.Entities;


namespace CollaboratorsGS.Application.Interfaces
{
    public interface IPayslipService
    {
        Task<Payslip> GetPayslipAsync(int Month, int Year, int user_id, int approveview, int payroll_id);
        Task<string> GetPayslipUrlAsync(int version, string benefitName,string benefitPhone, int Month, int Year, int user_id, int approveview, int payroll_id);
    }
}
