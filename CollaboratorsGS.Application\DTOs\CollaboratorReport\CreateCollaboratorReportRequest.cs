using System;
using System.ComponentModel.DataAnnotations;

namespace CollaboratorsGS.Application.DTOs.CollaboratorReport
{
    public class CreateCollaboratorReportRequest
    {
        [Required]
        public Guid CollaboratorId { get; set; }
        
        [Required]
        [RegularExpression(@"^\d{4}-\d{2}$", ErrorMessage = "Report period must be in format YYYY-MM")]
        public string ReportPeriod { get; set; } = string.Empty;
        
        [Range(0, int.MaxValue, ErrorMessage = "Total candidates must be non-negative")]
        public int TotalCandidates { get; set; } = 0;
        
        [Range(0, double.MaxValue, ErrorMessage = "Total payment must be non-negative")]
        public decimal TotalPayment { get; set; } = 0;
        
        public string? Data { get; set; }
    }
}
