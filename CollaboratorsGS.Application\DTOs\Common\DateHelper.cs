using System.Globalization;

namespace CollaboratorsGS.Application.DTOs.Common
{
    public static class DateHelper
    {
        private static readonly string[] SupportedFormats = { "dd/MM/yyyy", "yyyy-MM-dd", "MM/dd/yyyy" };

        /// <summary>
        /// Parse date string in multiple supported formats to DateTime
        /// Supported formats: dd/MM/yyyy, yyyy-MM-dd, MM/dd/yyyy
        /// </summary>
        /// <param name="dateString">Date string in supported format</param>
        /// <returns>DateTime if parsing successful, null otherwise</returns>
        public static DateTime? ParseDateString(string? dateString)
        {
            if (string.IsNullOrEmpty(dateString))
                return null;

            // Try each supported format
            foreach (var format in SupportedFormats)
            {
                if (DateTime.TryParseExact(dateString, format, CultureInfo.InvariantCulture, DateTimeStyles.None, out DateTime result))
                    return result;
            }

            return null;
        }

        /// <summary>
        /// Format DateTime to dd/MM/yyyy string
        /// </summary>
        /// <param name="dateTime">DateTime to format</param>
        /// <returns>Formatted date string or null if input is null</returns>
        public static string? FormatDateString(DateTime? dateTime)
        {
            return dateTime?.ToString("dd/MM/yyyy");
        }

        /// <summary>
        /// Validate if date string is in any supported format
        /// Supported formats: dd/MM/yyyy, yyyy-MM-dd, MM/dd/yyyy
        /// </summary>
        /// <param name="dateString">Date string to validate</param>
        /// <returns>True if valid format, false otherwise</returns>
        public static bool IsValidDateFormat(string? dateString)
        {
            if (string.IsNullOrEmpty(dateString))
                return true; // null/empty is considered valid for optional fields

            // Try each supported format
            foreach (var format in SupportedFormats)
            {
                if (DateTime.TryParseExact(dateString, format, CultureInfo.InvariantCulture, DateTimeStyles.None, out _))
                    return true;
            }

            return false;
        }
    }
}
