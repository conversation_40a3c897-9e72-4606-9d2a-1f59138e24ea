using CollaboratorsGS.Domain.Entities;
using CollaboratorsGS.Domain.Repositories;
using CollaboratorsGS.Infrastructure.Data;
using Dapper;
using System.Data;

namespace CollaboratorsGS.Infrastructure.Repositories
{
    public class CollaboratorKpiTargetRepository : ICollaboratorKpiTargetRepository
    {
        private readonly IConnectionFactory _connectionFactory;

        public CollaboratorKpiTargetRepository(IConnectionFactory connectionFactory)
        {
            _connectionFactory = connectionFactory;
        }

        public async Task<CollaboratorKpiTarget?> GetByIdAsync(Guid targetId)
        {
            using var connection = _connectionFactory.CreateConnection();

            var parameters = new DynamicParameters();
            parameters.Add("@TargetId", targetId, DbType.Guid);

            return await connection.QuerySingleOrDefaultAsync<CollaboratorKpiTarget>(
                "sp_GetCollaboratorKpiTargetById",
                parameters,
                commandType: CommandType.StoredProcedure);
        }

        public async Task<IEnumerable<CollaboratorKpiTarget>> GetAllAsync()
        {
            using var connection = _connectionFactory.CreateConnection();

            return await connection.QueryAsync<CollaboratorKpiTarget>(
                "sp_GetAllCtvKpiTargets",
                commandType: CommandType.StoredProcedure);
        }

        public async Task<IEnumerable<CollaboratorKpiTarget>> GetByCollaboratorIdAsync(Guid collaboratorId)
        {
            using var connection = _connectionFactory.CreateConnection();

            var parameters = new DynamicParameters();
            parameters.Add("@CtvId", collaboratorId, DbType.Guid);

            return await connection.QueryAsync<CollaboratorKpiTarget>(
                "sp_GetCtvKpiTargetsByCtvId",
                parameters,
                commandType: CommandType.StoredProcedure);
        }

        public async Task<IEnumerable<CollaboratorKpiTarget>> GetByPeriodAsync(string period)
        {
            using var connection = _connectionFactory.CreateConnection();

            var parameters = new DynamicParameters();
            parameters.Add("@Period", period, DbType.String);

            return await connection.QueryAsync<CollaboratorKpiTarget>(
                "sp_GetCtvKpiTargetsByPeriod",
                parameters,
                commandType: CommandType.StoredProcedure);
        }

        public async Task<Guid> CreateAsync(CollaboratorKpiTarget target)
        {
            if (target.TargetId == Guid.Empty)
            {
                target.TargetId = Guid.NewGuid();
            }

            target.CreatedAt = DateTime.UtcNow;

            using var connection = _connectionFactory.CreateConnection();

            var parameters = new DynamicParameters();
            parameters.Add("@TargetId", target.TargetId, DbType.Guid);
            parameters.Add("@CtvId", target.CollaboratorId, DbType.Guid);
            parameters.Add("@Period", target.Period, DbType.String);
            parameters.Add("@TargetCandidatesImported", target.TargetCandidatesImported, DbType.Int32);
            parameters.Add("@TargetCandidatesPassedRound1", target.TargetCandidatesPassedRound1, DbType.Int32);
            parameters.Add("@TargetCandidatesOnboarded", target.TargetCandidatesOnboarded, DbType.Int32);
            parameters.Add("@CreatedAt", target.CreatedAt, DbType.DateTime);

            await connection.ExecuteAsync(
                "sp_CreateCtvKpiTarget",
                parameters,
                commandType: CommandType.StoredProcedure);

            return target.TargetId;
        }

        public async Task<bool> UpdateAsync(CollaboratorKpiTarget target)
        {
            target.UpdatedAt = DateTime.UtcNow;

            using var connection = _connectionFactory.CreateConnection();

            var parameters = new DynamicParameters();
            parameters.Add("@TargetId", target.TargetId, DbType.Guid);
            parameters.Add("@TargetCandidatesImported", target.TargetCandidatesImported, DbType.Int32);
            parameters.Add("@TargetCandidatesPassedRound1", target.TargetCandidatesPassedRound1, DbType.Int32);
            parameters.Add("@TargetCandidatesOnboarded", target.TargetCandidatesOnboarded, DbType.Int32);
            parameters.Add("@UpdatedAt", target.UpdatedAt, DbType.DateTime);

            var rowsAffected = await connection.ExecuteAsync(
                "sp_UpdateCtvKpiTarget",
                parameters,
                commandType: CommandType.StoredProcedure);

            return rowsAffected > 0;
        }

        public async Task<bool> DeleteAsync(Guid targetId)
        {
            using var connection = _connectionFactory.CreateConnection();

            var parameters = new DynamicParameters();
            parameters.Add("@TargetId", targetId, DbType.Guid);

            var rowsAffected = await connection.ExecuteAsync(
                "sp_DeleteCtvKpiTarget",
                parameters,
                commandType: CommandType.StoredProcedure);

            return rowsAffected > 0;
        }
    }
}
