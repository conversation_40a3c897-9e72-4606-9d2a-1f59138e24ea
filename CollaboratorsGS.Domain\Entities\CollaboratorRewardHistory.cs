
namespace CollaboratorsGS.Domain.Entities
{
    public class CollaboratorRewardHistory
    {
        public Guid HistoryId { get; set; }
        public Guid CollaboratorId { get; set; }
        public Guid RewardId { get; set; }
        public decimal Amount { get; set; }
        public DateTime PaymentDate { get; set; }
        public string PaymentMethod { get; set; } = string.Empty;
        public string Status { get; set; } = string.Empty;

        // Navigation properties
        public Collaborator? Collaborator { get; set; }
        public CollaboratorReward? Reward { get; set; }
    }
}
