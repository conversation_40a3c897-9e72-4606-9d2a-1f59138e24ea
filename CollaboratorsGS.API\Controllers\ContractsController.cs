using CollaboratorsGS.Application.Constants;
using CollaboratorsGS.Application.DTOs;
using CollaboratorsGS.Application.DTOs.Contract;
using CollaboratorsGS.Application.Interfaces;
using CollaboratorsGS.Infrastructure.Utilities;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace CollaboratorsGS.API.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    [Authorize]
    public class ContractsController : ControllerBase
    {
        private readonly IContractService _contractService;
        private readonly ILogger<ContractsController> _logger;

        public ContractsController(
            IContractService contractService,
            ILogger<ContractsController> logger)
        {
            _contractService = contractService;
            _logger = logger;
        }

        // GET: api/Contracts
        [HttpGet]
        [Authorize(Roles = RolesUser.AdminManager)]
        public async Task<IActionResult> GetAll()
        {
            try
            {
                var contracts = await _contractService.GetAllAsync();
                return Ok(ApiResponse<object>.SuccessResponse(
                    MessageCodes.SC2000,
                    "Get all contract successfully",
                    contracts));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting all contracts");
                return StatusCode(500, ApiResponse<object>.ErrorResponse(
                    MessageCodes.ER5000,
                    "Internal server error " + ex.Message,
                    500));
            }
        }

        // GET: api/Contracts/{id}
        [HttpGet("{id}")]
        public async Task<IActionResult> GetById(Guid id)
        {
            try
            {
                var contract = await _contractService.GetByIdAsync(id);
                
                if (contract == null)
                    return NotFound(ApiResponse<object>.ErrorResponse(
                         MessageCodes.ER4004,
                         "Contract not found",
                         404));
                
                return Ok(ApiResponse<object>.SuccessResponse(
                    MessageCodes.SC2000,
                    "Get contract successfully",
                    contract));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting contract with ID {ContractId}", id);
                return StatusCode(500, ApiResponse<object>.ErrorResponse(
                    MessageCodes.ER5000,
                    "Internal server error",
                    500));
            }
        }

        // GET: api/Contracts/collaborator/{ctvId}
        [HttpGet("collaborator/{collaboratorId}")]
        public async Task<IActionResult> GetByCollaborator(Guid collaboratorId)
        {
            try
            {
                var contracts = await _contractService.GetByCollaboratorIdAsync(collaboratorId);
                return Ok(ApiResponse<object>.SuccessResponse(
                    MessageCodes.SC2000,
                    "Get contract by collaborator id successfully",
                    contracts));
            }
            catch (InvalidOperationException ex)
            {
                _logger.LogWarning(ex, "Invalid operation: {Message}", ex.Message);
                return BadRequest(ApiResponse<object>.ErrorResponse(
                    MessageCodes.ER4005,
                    ex.Message,
                    400,
                    new List<ErrorDetail>
                    {
                        new ErrorDetail
                        {
                            Field = "collaboratorId",
                            ErrorCode = MessageCodes.ER4005,
                            Message = $"Error getting contracts for collaborator {collaboratorId}"
                        }
                    }));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting contracts for collaborator {collaboratorId}", collaboratorId);
                return StatusCode(500, ApiResponse<object>.ErrorResponse(
                    MessageCodes.ER5000,
                    "Internal server error " + ex.Message,
                    500));
            }
        }

        // GET: api/Contracts/status/{status}
        [HttpGet("status/{status}")]
        [Authorize(Roles = RolesUser.AdminManager)]
        public async Task<IActionResult> GetByStatus(string status)
        {
            try
            {
                var contracts = await _contractService.GetByStatusAsync(status);
                return Ok(ApiResponse<object>.SuccessResponse(
                    MessageCodes.SC2000,
                    "Get contract by status successfully",
                    contracts));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting contracts with status {Status}", status);
                return StatusCode(500, ApiResponse<object>.ErrorResponse(
                    MessageCodes.ER5000,
                    "Internal server error " + ex.Message,
                    500));
            }
        }

        // POST: api/Contracts
        [HttpPost]
        [Authorize(Roles = RolesUser.AdminManager)]
        public async Task<IActionResult> Create([FromBody] CreateContractRequest request)
        {
            try
            {
                var contractId = await _contractService.CreateContractAsync(request);
                return CreatedAtAction(nameof(GetById), new { id = contractId },
                ApiResponse<object>.SuccessResponse(
                        MessageCodes.SC2001,
                        "Collaborator contract created successfully",
                        contractId,
                        201));
            }
            catch (InvalidOperationException ex)
            {
                _logger.LogWarning(ex, "Invalid operation: {Message}", ex.Message);
                return BadRequest(ApiResponse<object>.ErrorResponse(
                    MessageCodes.ER4005,
                    ex.Message,
                    400,
                    new List<ErrorDetail>
                    {
                        new ErrorDetail
                        {
                            Field = "request",
                            ErrorCode = MessageCodes.ER4005,
                            Message = ex.Message
                        }
                    }));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating contract");
                return StatusCode(500, ApiResponse<object>.ErrorResponse(
                    MessageCodes.ER5000,
                    "Internal server error",
                    500));
            }
        }

        // PUT: api/Contracts/{id}
        [HttpPut("{id}")]
        [Authorize(Roles = RolesUser.AdminManager)]
        public async Task<IActionResult> Update(Guid id, [FromBody] UpdateContractRequest request)
        {
            try
            {
                if (id != request.ContractId)
                    return StatusCode(400, ApiResponse<object>.ErrorResponse(
                        MessageCodes.ER4005,
                        "Id contract in request body does not match ID in URL",
                        400));
                
                var result = await _contractService.UpdateContractAsync(request);
                
                if (!result)
                    return NotFound(ApiResponse<object>.ErrorResponse(
                         MessageCodes.ER4004,
                         "Collaborator contract id not found",
                         404));
                
                return Ok(ApiResponse<object>.SuccessResponse(
                    MessageCodes.SC2002,
                    "Update collaborator contract successfully",
                    result));
            }
            catch (InvalidOperationException ex)
            {
                _logger.LogWarning(ex, "Invalid operation: {Message}", ex.Message);
                                return BadRequest(ApiResponse<object>.ErrorResponse(
                    MessageCodes.ER4005,
                    ex.Message,
                    400,
                    new List<ErrorDetail>
                    {
                        new ErrorDetail
                        {
                            Field = "request",
                            ErrorCode = MessageCodes.ER4005,
                            Message = ex.Message
                        }
                    }));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating contract with ID {ContractId}", id);
                return StatusCode(500, ApiResponse<object>.ErrorResponse(
                    MessageCodes.ER5000,
                    "Internal server error",
                    500));
            }
        }

        // DELETE: api/Contracts/{id}
        [HttpDelete("{id}")]
        [Authorize(Roles = RolesUser.Admin)]
        public async Task<IActionResult> Delete(Guid id)
        {
            try
            {
                var result = await _contractService.DeleteContractAsync(id);
                
                if (!result)
                    return NotFound(ApiResponse<object>.ErrorResponse(
                         MessageCodes.ER4004,
                         "Collaborator contract id not found",
                         404));
                
                return Ok(ApiResponse<object>.SuccessResponse(
                    MessageCodes.SC2003,
                    "Delete collaborator contract successfully",
                    result));
            }
            catch (InvalidOperationException ex)
            {
                _logger.LogWarning(ex, "Invalid operation: {Message}", ex.Message);
                                return BadRequest(ApiResponse<object>.ErrorResponse(
                    MessageCodes.ER4005,
                    ex.Message,
                    400,
                    new List<ErrorDetail>
                    {
                        new ErrorDetail
                        {
                            Field = "request",
                            ErrorCode = MessageCodes.ER4005,
                            Message = ex.Message
                        }
                    }));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting contract with ID {ContractId}", id);
                return StatusCode(500, ApiResponse<object>.ErrorResponse(
                    MessageCodes.ER5000,
                    "Internal server error",
                    500));
            }
        }
    }
}
