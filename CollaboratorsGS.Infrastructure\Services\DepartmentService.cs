using AutoMapper;
using CollaboratorsGS.Application.DTOs.Department;
using CollaboratorsGS.Application.Interfaces;
using CollaboratorsGS.Domain.Entities;
using CollaboratorsGS.Domain.Repositories;
using Microsoft.Extensions.Logging;

namespace CollaboratorsGS.Infrastructure.Services
{
    public class DepartmentService : IDepartmentService
    {
        private readonly IDepartmentRepository _departmentRepository;
        private readonly IMapper _mapper;
        private readonly ILogger<DepartmentService> _logger;

        public DepartmentService(
            IDepartmentRepository departmentRepository,
            IMapper mapper,
            ILogger<DepartmentService> logger)
        {
            _departmentRepository = departmentRepository;
            _mapper = mapper;
            _logger = logger;
        }

        public async Task<DepartmentDto?> GetByIdAsync(Guid departmentId)
        {
            try
            {
                var department = await _departmentRepository.GetByIdAsync(departmentId);
                if (department == null)
                    return null;

                return _mapper.Map<DepartmentDto>(department);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting department with ID {DepartmentId}", departmentId);
                throw;
            }
        }

        public async Task<IEnumerable<DepartmentDto>> GetAllAsync()
        {
            try
            {
                var departments = await _departmentRepository.GetAllAsync();
                return _mapper.Map<IEnumerable<DepartmentDto>>(departments);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting all departments");
                throw;
            }
        }

        public async Task<Guid> CreateDepartmentAsync(CreateDepartmentRequest request)
        {
            try
            {
                var department = _mapper.Map<Department>(request);
                department.DepartmentId = Guid.NewGuid();

                return await _departmentRepository.CreateAsync(department);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating department");
                throw;
            }
        }

        public async Task<DepartmentDto?> GetCreatedDepartmentAsync(Guid departmentId)
        {
            return await GetByIdAsync(departmentId);
        }

        public async Task<DepartmentDto?> UpdateDepartmentAsync(UpdateDepartmentRequest request)
        {
            try
            {
                // Check if department exists
                var existingDepartment = await _departmentRepository.GetByIdAsync(request.DepartmentId);
                if (existingDepartment == null)
                {
                    _logger.LogWarning("Department with ID {DepartmentId} not found", request.DepartmentId);
                    return null;
                }

                var department = _mapper.Map<Department>(request);
                var result = await _departmentRepository.UpdateAsync(department);

                if (!result)
                    return null;

                // Get the updated department
                return await GetByIdAsync(request.DepartmentId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating department with ID {DepartmentId}", request.DepartmentId);
                throw;
            }
        }

        public async Task<bool> DeleteDepartmentAsync(Guid departmentId)
        {
            try
            {
                // Check if department exists
                var existingDepartment = await _departmentRepository.GetByIdAsync(departmentId);
                if (existingDepartment == null)
                {
                    _logger.LogWarning("Department with ID {DepartmentId} not found", departmentId);
                    return false;
                }

                return await _departmentRepository.DeleteAsync(departmentId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting department with ID {DepartmentId}", departmentId);
                throw;
            }
        }
    }
}
