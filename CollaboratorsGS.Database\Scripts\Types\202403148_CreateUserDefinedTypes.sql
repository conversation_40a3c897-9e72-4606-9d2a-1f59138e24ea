-- Create User-Defined Types for enums and statuses

-- Create RewardType enum
IF NOT EXISTS (SELECT * FROM sys.types WHERE name = 'RewardType' AND is_user_defined = 1)
BEGIN
    CREATE TYPE [dbo].[RewardType] FROM VARCHAR(50);
END
GO

-- Create ApplicationStatus enum
IF NOT EXISTS (SELECT * FROM sys.types WHERE name = 'ApplicationStatus' AND is_user_defined = 1)
BEGIN
    CREATE TYPE [dbo].[ApplicationStatus] FROM VARCHAR(50);
END
GO

-- Create GenderType enum
IF NOT EXISTS (SELECT * FROM sys.types WHERE name = 'GenderType' AND is_user_defined = 1)
BEGIN
    CREATE TYPE [dbo].[GenderType] FROM VARCHAR(10);
END
GO

-- Create PaymentStatus enum
IF NOT EXISTS (SELECT * FROM sys.types WHERE name = 'PaymentStatus' AND is_user_defined = 1)
BEGIN
    CREATE TYPE [dbo].[PaymentStatus] FROM VARCHAR(50);
END
GO

-- Create PaymentMethod enum
IF NOT EXISTS (SELECT * FROM sys.types WHERE name = 'PaymentMethod' AND is_user_defined = 1)
BEGIN
    CREATE TYPE [dbo].[PaymentMethod] FROM VARCHAR(50);
END
GO