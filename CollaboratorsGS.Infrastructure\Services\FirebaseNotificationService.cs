using CollaboratorsGS.Application.DTOs;
using CollaboratorsGS.Application.Interfaces;
using CollaboratorsGS.Domain.Repositories;
using CollaboratorsGS.Infrastructure.Configurations;
using FirebaseAdmin;
using FirebaseAdmin.Messaging;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;

namespace CollaboratorsGS.Infrastructure.Services
{
    public class FirebaseNotificationService : INotificationService
    {
        private readonly IDeviceTokenRepository _deviceTokenRepository;
        private readonly FirebaseConfig _firebaseConfig;
        private readonly ILogger<FirebaseNotificationService> _logger;
        private FirebaseMessaging? _firebaseMessaging;

        public FirebaseNotificationService(
            IDeviceTokenRepository deviceTokenRepository,
            IOptions<FirebaseConfig> firebaseConfig,
            ILogger<FirebaseNotificationService> logger)
        {
            _deviceTokenRepository = deviceTokenRepository;
            _firebaseConfig = firebaseConfig.Value;
            _logger = logger;
        }

        private void InitializeFirebaseAdminSdk()
        {
            // Skip initialization if already initialized
            if (FirebaseApp.DefaultInstance != null)
            {
                _firebaseMessaging = FirebaseMessaging.DefaultInstance;
                return;
            }

            try
            {
                // Initialize Firebase Admin SDK with default credentials
                FirebaseApp.Create(new AppOptions
                {
                    ProjectId = _firebaseConfig.ProjectId
                });

                _logger.LogInformation("Firebase Admin SDK initialized successfully");
                _firebaseMessaging = FirebaseMessaging.DefaultInstance;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error initializing Firebase Admin SDK");
                throw;
            }
        }

        public async Task<bool> SaveDeviceTokenAsync(DeviceTokenRequest request)
        {
            try
            {
                return await _deviceTokenRepository.SaveTokenAsync(request.Token, request.UserId, request.DeviceType);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error saving device token");
                return false;
            }
        }

        public async Task<bool> RemoveDeviceTokenAsync(string token)
        {
            try
            {
                return await _deviceTokenRepository.RemoveTokenAsync(token);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error removing device token");
                return false;
            }
        }

        public async Task<List<string>> GetUserDeviceTokensAsync(string userId)
        {
            try
            {
                return await _deviceTokenRepository.GetUserTokensAsync(userId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting user device tokens");
                return new List<string>();
            }
        }

        public async Task<bool> SendNotificationAsync(NotificationRequest request)
        {
            try
            {
                // Initialize Firebase Admin SDK if needed
                InitializeFirebaseAdminSdk();

                if ((request.Tokens == null || !request.Tokens.Any()) && string.IsNullOrEmpty(request.Topic))
                {
                    _logger.LogWarning("No tokens or topic provided for notification");
                    return false;
                }

                var message = new MulticastMessage
                {
                    Notification = new Notification
                    {
                        Title = request.Title,
                        Body = request.Body,
                        ImageUrl = request.ImageUrl
                    },
                    Data = request.Data ?? new Dictionary<string, string>(),
                    Webpush = new WebpushConfig
                    {
                        FcmOptions = new WebpushFcmOptions
                        {
                            Link = request.ClickAction
                        }
                    }
                };

                if (!string.IsNullOrEmpty(request.Topic))
                {
                    // Send to topic
                    var topicMessage = new Message
                    {
                        Notification = message.Notification,
                        Data = message.Data,
                        Webpush = message.Webpush,
                        Topic = request.Topic
                    };

                    var topicResponse = await _firebaseMessaging!.SendAsync(topicMessage);
                    _logger.LogInformation("Notification sent to topic {Topic}: {Response}", request.Topic, topicResponse);
                    return !string.IsNullOrEmpty(topicResponse);
                }
                else
                {
                    // Send to tokens
                    message.Tokens = request.Tokens!;
                    var response = await _firebaseMessaging!.SendMulticastAsync(message);
                    _logger.LogInformation("Notification sent to {SuccessCount} devices, failed: {FailureCount}",
                        response.SuccessCount, response.FailureCount);

                    // Handle failed tokens
                    if (response.FailureCount > 0)
                    {
                        var failedTokens = new List<string>();
                        for (var i = 0; i < response.Responses.Count; i++)
                        {
                            if (!response.Responses[i].IsSuccess)
                            {
                                var failedToken = request.Tokens![i];
                                _logger.LogWarning("Failed to send notification to token: {Token}, Error: {Error}",
                                    failedToken, response.Responses[i].Exception?.Message);

                                // Check if token is invalid and should be removed
                                if (response.Responses[i].Exception is FirebaseMessagingException messagingEx &&
                                    (messagingEx.MessagingErrorCode == MessagingErrorCode.Unregistered ||
                                     messagingEx.MessagingErrorCode == MessagingErrorCode.InvalidArgument))
                                {
                                    failedTokens.Add(failedToken);
                                }
                            }
                        }

                        // Remove invalid tokens
                        foreach (var token in failedTokens)
                        {
                            await RemoveDeviceTokenAsync(token);
                        }
                    }

                    return response.SuccessCount > 0;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error sending notification");
                return false;
            }
        }

        public async Task<bool> SubscribeToTopicAsync(string token, string topic)
        {
            try
            {
                // Initialize Firebase Admin SDK if needed
                InitializeFirebaseAdminSdk();

                var response = await _firebaseMessaging!.SubscribeToTopicAsync(new List<string> { token }, topic);
                _logger.LogInformation("Subscribed token to topic {Topic}: Success: {SuccessCount}, Failures: {FailureCount}",
                    topic, response.SuccessCount, response.FailureCount);
                return response.SuccessCount > 0;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error subscribing to topic");
                return false;
            }
        }

        public async Task<bool> UnsubscribeFromTopicAsync(string token, string topic)
        {
            try
            {
                // Initialize Firebase Admin SDK if needed
                InitializeFirebaseAdminSdk();

                var response = await _firebaseMessaging!.UnsubscribeFromTopicAsync(new List<string> { token }, topic);
                _logger.LogInformation("Unsubscribed token from topic {Topic}: Success: {SuccessCount}, Failures: {FailureCount}",
                    topic, response.SuccessCount, response.FailureCount);
                return response.SuccessCount > 0;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error unsubscribing from topic");
                return false;
            }
        }
    }
}
