using AutoMapper;
using CollaboratorsGS.Application.DTOs;
using CollaboratorsGS.Application.Interfaces;
using CollaboratorsGS.Domain.Repositories;

namespace CollaboratorsGS.Infrastructure.Services
{
    public class UserService : IUserService
    {
        private readonly IUserRepository _userRepository;
        private readonly IRoleRepository _roleRepository;
        private readonly IMapper _mapper;

        public UserService(IUserRepository userRepository, IRoleRepository roleRepository, IMapper mapper)
        {
            _userRepository = userRepository;
            _roleRepository = roleRepository;
            _mapper = mapper;
        }

        public async Task<UserDto?> GetByIdAsync(Guid userId)
        {
            var user = await _userRepository.GetByIdAsync(userId);
            return user != null ? _mapper.Map<UserDto>(user) : null;
        }

        public async Task<IEnumerable<UserDto>> GetAllAsync()
        {
            var users = await _userRepository.GetAllAsync();
            return _mapper.Map<IEnumerable<UserDto>>(users);
        }

        public async Task<Guid> CreateUserAsync(CreateUserRequest request)
        {
            // Check if username already exists
            var existingUser = await _userRepository.GetByUsernameAsync(request.Username);
            if (existingUser != null)
                throw new InvalidOperationException("Username already exists");

            // Check if email already exists
            if (!string.IsNullOrEmpty(request.Email))
            {
                existingUser = await _userRepository.GetByEmailAsync(request.Email);
                if (existingUser != null)
                    throw new InvalidOperationException("Email already exists");
            }

            // Check if role exists
            var role = await _roleRepository.GetByIdAsync(request.RoleId);
            if (role == null)
                throw new InvalidOperationException("Role does not exist");

            // Create new user
            var user = new Domain.Entities.User
            {
                Username = request.Username,
                Password = BCrypt.Net.BCrypt.HashPassword(request.Password),
                RoleId = request.RoleId,
                FullName = request.FullName,
                Email = request.Email,
                PhoneNumber = request.PhoneNumber,
                CreatedAt = DateTime.UtcNow,
                IsActive = true
            };

            return await _userRepository.CreateAsync(user);
        }

        public async Task<bool> UpdateUserAsync(Guid userId, UpdateUserRequest request)
        {
            var existingUser = await _userRepository.GetByIdAsync(userId);
            if (existingUser == null)
                return false;

            // Check if role exists
            var role = await _roleRepository.GetByIdAsync(request.RoleId);
            if (role == null)
                throw new InvalidOperationException("Role does not exist");

            // Check if email already exists (if changed)
            if (!string.IsNullOrEmpty(request.Email) && request.Email != existingUser.Email)
            {
                var userWithSameEmail = await _userRepository.GetByEmailAsync(request.Email);
                if (userWithSameEmail != null && userWithSameEmail.UserId != userId)
                    throw new InvalidOperationException("Email already exists");
            }

            // Update fields
            existingUser.RoleId = request.RoleId;
            existingUser.FullName = request.FullName;
            existingUser.Email = request.Email;
            existingUser.PhoneNumber = request.PhoneNumber;
            existingUser.IsActive = request.IsActive;

            return await _userRepository.UpdateAsync(existingUser);
        }

        public async Task<bool> DeleteUserAsync(Guid userId)
        {
            return await _userRepository.DeleteAsync(userId);
        }

        public async Task<bool> ChangePasswordAsync(ChangePasswordRequest request)
        {
            var user = await _userRepository.GetByIdAsync(request.UserId);
            if (user == null)
                return false;

            // Verify current password
            if (!BCrypt.Net.BCrypt.Verify(request.CurrentPassword, user.Password))
                return false;

            // Update password
            user.Password = BCrypt.Net.BCrypt.HashPassword(request.NewPassword);
            return await _userRepository.UpdateAsync(user);
        }

        public async Task<bool> AdminChangePasswordAsync(AdminChangePasswordRequest request)
        {
            var user = await _userRepository.GetByIdAsync(request.UserId);
            if (user == null)
                return false;

            // Update password
            user.Password = BCrypt.Net.BCrypt.HashPassword(request.NewPassword);
            return await _userRepository.UpdateAsync(user);
        }
    }
}
