using System;
using System.ComponentModel.DataAnnotations;

namespace CollaboratorsGS.Application.DTOs.CollaboratorReport
{
    public class UpdateCollaboratorReportRequest
    {
        [Required]
        public Guid ReportId { get; set; }
        
        [Range(0, int.MaxValue, ErrorMessage = "Total candidates must be non-negative")]
        public int TotalCandidates { get; set; }
        
        [Range(0, double.MaxValue, ErrorMessage = "Total payment must be non-negative")]
        public decimal TotalPayment { get; set; }
        
        public string? Data { get; set; }
    }
}
