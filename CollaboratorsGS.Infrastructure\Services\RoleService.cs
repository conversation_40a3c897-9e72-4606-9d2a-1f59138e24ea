using AutoMapper;
using CollaboratorsGS.Application.DTOs;
using CollaboratorsGS.Application.Interfaces;
using CollaboratorsGS.Domain.Entities;
using CollaboratorsGS.Domain.Repositories;

namespace CollaboratorsGS.Infrastructure.Services
{
    public class RoleService : IRoleService
    {
        private readonly IRoleRepository _roleRepository;
        private readonly IRolePermissionRepository _rolePermissionRepository;
        private readonly IPermissionRepository _permissionRepository;
        private readonly IMapper _mapper;

        public RoleService(
            IRoleRepository roleRepository,
            IRolePermissionRepository rolePermissionRepository,
            IPermissionRepository permissionRepository,
            IMapper mapper)
        {
            _roleRepository = roleRepository;
            _rolePermissionRepository = rolePermissionRepository;
            _permissionRepository = permissionRepository;
            _mapper = mapper;
        }

        public async Task<IEnumerable<RoleDto>> GetAllRolesAsync()
        {
            var roles = await _roleRepository.GetAllAsync();
            return _mapper.Map<IEnumerable<RoleDto>>(roles);
        }

        public async Task<RoleDto?> GetRoleByIdAsync(Guid roleId)
        {
            var role = await _roleRepository.GetByIdAsync(roleId);
            return role != null ? _mapper.Map<RoleDto>(role) : null;
        }

        public async Task<RoleWithPermissionsDto?> GetRoleWithPermissionsAsync(Guid roleId)
        {
            var role = await _roleRepository.GetByIdAsync(roleId);
            if (role == null)
                return null;

            var roleWithPermissions = _mapper.Map<RoleWithPermissionsDto>(role);
            
            var permissions = await _roleRepository.GetPermissionsByRoleIdAsync(roleId);
            roleWithPermissions.Permissions = _mapper.Map<List<PermissionDto>>(permissions);
            
            return roleWithPermissions;
        }

        public async Task<Guid> CreateRoleAsync(CreateRoleRequest request)
        {
            var role = new Role
            {
                RoleName = request.RoleName,
                Description = request.Description
            };

            return await _roleRepository.CreateAsync(role);
        }

        public async Task<bool> UpdateRoleAsync(RoleDto roleDto)
        {
            var role = await _roleRepository.GetByIdAsync(roleDto.RoleId);
            if (role == null)
                return false;

            role.RoleName = roleDto.RoleName;
            role.Description = roleDto.Description;

            return await _roleRepository.UpdateAsync(role);
        }

        public async Task<bool> DeleteRoleAsync(Guid roleId)
        {
            // First remove all permissions from the role
            await _rolePermissionRepository.RemoveAllPermissionsFromRoleAsync(roleId);
            
            // Then delete the role
            return await _roleRepository.DeleteAsync(roleId);
        }

        public async Task<bool> AssignPermissionsToRoleAsync(AssignPermissionsRequest request)
        {
            // First remove all existing permissions
            await _rolePermissionRepository.RemoveAllPermissionsFromRoleAsync(request.RoleId);
            
            // Then add the new permissions
            foreach (var permissionId in request.PermissionIds)
            {
                await _rolePermissionRepository.AssignPermissionToRoleAsync(request.RoleId, permissionId);
            }
            
            return true;
        }
    }
}
