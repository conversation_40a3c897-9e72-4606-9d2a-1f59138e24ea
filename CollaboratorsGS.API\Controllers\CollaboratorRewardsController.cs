using CollaboratorsGS.Application.Constants;
using CollaboratorsGS.Application.DTOs;
using CollaboratorsGS.Application.DTOs.CollaboratorReward;
using CollaboratorsGS.Application.Interfaces;
using CollaboratorsGS.Infrastructure.Utilities;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace CollaboratorsGS.API.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class CollaboratorRewardsController : ControllerBase
    {
        private readonly ICollaboratorRewardService _collaboratorRewardService;
        private readonly ICollaboratorService _collaboratorService;
        private readonly ILogger<CollaboratorRewardsController> _logger;

        public CollaboratorRewardsController(
            ICollaboratorRewardService collaboratorRewardService,
            ICollaboratorService collaboratorService,
            ILogger<CollaboratorRewardsController> logger)
        {
            _collaboratorRewardService = collaboratorRewardService;
            _collaboratorService = collaboratorService;
            _logger = logger;
        }

        // GET: api/CtvRewards
        [HttpGet]
        [Authorize(Roles = RolesUser.AdminManagerRecruiter)]
        public async Task<IActionResult> GetAll()
        {
            try
            {
                var rewards = await _collaboratorRewardService.GetAllAsync();
                return Ok(ApiResponse<object>.SuccessResponse(
                    MessageCodes.SC2000,
                    "Get all collaborator rewards successfully",
                    rewards));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting all CTV rewards");
                return StatusCode(500, ApiResponse<object>.ErrorResponse(
                    MessageCodes.ER5000,
                    ex.Message,
                    500));
            }
        }

        // GET: api/CtvRewards/5
        [HttpGet("{id}")]
        [Authorize(Roles = RolesUser.AdminManagerRecruiterCollaborator)]
        public async Task<IActionResult> GetById(Guid id)
        {
            try
            {
                var reward = await _collaboratorRewardService.GetByIdAsync(id);
                if (reward == null)
                {
                    return NotFound(ApiResponse<object>.ErrorResponse(
                        MessageCodes.ER4004,
                        "Collaborator reward not found",
                        404));
                }

                // If user is a collaborator, check if they are the owner of the reward
                if (User.IsInRole("Collaborator"))
                {
                    var userIdClaim = User.FindFirst(System.Security.Claims.ClaimTypes.NameIdentifier)
                        ?? User.FindFirst("sub");

                    if (userIdClaim == null || !Guid.TryParse(userIdClaim.Value, out var userId))
                    {
                        return BadRequest(ApiResponse<object>.ErrorResponse(
                            MessageCodes.ER4001,
                            "User ID not found in token or invalid",
                            400,
                            new List<ErrorDetail>
                            {
                                new ErrorDetail
                                {
                                    Field = "token",
                                    ErrorCode = MessageCodes.ER4001,
                                    Message = "Invalid user ID in token"
                                }
                            }));
                    }

                    // Get the collaborator ID for this user
                    // This would require a service method to get collaborator by user ID
                    // For now, we'll just check if the reward's CtvId matches the one in the token
                    if (reward.CollaboratorId.ToString() != userIdClaim.Value)
                    {
                        return StatusCode(403, ApiResponse<object>.ErrorResponse(
                            MessageCodes.ER4003,
                            "Access token denied",
                            403));
                    }
                }

                return Ok(ApiResponse<object>.SuccessResponse(
                    MessageCodes.SC2000,
                    "Get collaborator reward successfully",
                    reward));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting CTV reward by ID: {Id}", id);
                return StatusCode(500, ApiResponse<object>.ErrorResponse(
                    MessageCodes.ER5000,
                    ex.Message,
                    500));
            }
        }

        // GET: api/CtvRewards/collaborator/5
        [HttpGet("collaborator/{collaboratorId}")]
        [Authorize(Roles = RolesUser.AdminManagerRecruiterCollaborator)]
        public async Task<IActionResult> GetByCollaboratorId(Guid collaboratorId)
        {
            try
            {
                // If user is a collaborator, check if they are the owner of the rewards
                if (User.IsInRole("Collaborator"))
                {
                    var userIdClaim = User.FindFirst(System.Security.Claims.ClaimTypes.NameIdentifier)
                        ?? User.FindFirst("sub");

                    if (userIdClaim == null || !Guid.TryParse(userIdClaim.Value, out var userId))
                    {
                        return BadRequest(ApiResponse<object>.ErrorResponse(
                            MessageCodes.ER4001,
                            "User ID not found in token or invalid",
                            400,
                            new List<ErrorDetail>
                            {
                                new ErrorDetail
                                {
                                    Field = "token",
                                    ErrorCode = MessageCodes.ER4001,
                                    Message = "Invalid user ID in token"
                                }
                            }));
                    }
                    var collaborator = await _collaboratorService.GetByIdAsync(collaboratorId);
                    if (collaborator == null)
                    {
                        return BadRequest(ApiResponse<object>.ErrorResponse(
                            MessageCodes.ER4004,
                            "No collaborator found for the current user",
                            400));
                    }

                    // Get the collaborator ID for this user
                    // This would require a service method to get collaborator by user ID
                    // For now, we'll just check if the requested CtvId matches the one in the token
                    if (collaborator.UserId.ToString() != userIdClaim.Value)
                    {
                        return StatusCode(403, ApiResponse<object>.ErrorResponse(
                            MessageCodes.ER4003,
                            "Access token denied",
                            403));
                    }
                }

                var rewards = await _collaboratorRewardService.GetByCollaboratorIdAsync(collaboratorId);
                return Ok(ApiResponse<object>.SuccessResponse(
                    MessageCodes.SC2000,
                    "Get rewards by collaborator successfully",
                    rewards));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting CTV rewards by CTV ID: {CtvId}", collaboratorId);
                return StatusCode(500, ApiResponse<object>.ErrorResponse(
                    MessageCodes.ER5000,
                    ex.Message,
                    500));
            }
        }

        // GET: api/CtvRewards/status/pending
        [HttpGet("status/{status}")]
        [Authorize(Roles = RolesUser.AdminManagerRecruiter)]
        public async Task<IActionResult> GetByStatus(string status)
        {
            try
            {
                var rewards = await _collaboratorRewardService.GetByStatusAsync(status);
                return Ok(ApiResponse<object>.SuccessResponse(
                    MessageCodes.SC2000,
                    "Get rewards by status successfully",
                    rewards));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting CTV rewards by status: {Status}", status);
                return StatusCode(500, ApiResponse<object>.ErrorResponse(
                    MessageCodes.ER5000,
                    ex.Message,
                    500));
            }
        }

        // POST: api/CtvRewards
        [HttpPost]
        [Authorize(Roles = RolesUser.AdminManager)]
        public async Task<IActionResult> Create([FromBody] CreateCollaboratorRewardRequest request)
        {
            try
            {
                var rewardId = await _collaboratorRewardService.CreateCollaboratorRewardAsync(request);
                return CreatedAtAction(nameof(GetById), new { id = rewardId },
                ApiResponse<object>.SuccessResponse(
                        MessageCodes.SC2001,
                        "Report created successfully",
                        new { id = rewardId },
                        201));
            }
            catch (InvalidOperationException ex)
            {
                _logger.LogWarning(ex, "Validation error creating CTV reward");
                return BadRequest(ApiResponse<object>.ErrorResponse(
                    MessageCodes.ER4005,
                    ex.Message,
                    400,
                    new List<ErrorDetail>
                    {
                        new ErrorDetail
                        {
                            Field = "request",
                            ErrorCode = MessageCodes.ER4005,
                            Message = ex.Message
                        }
                    }));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating CTV reward");
                return StatusCode(500, ApiResponse<object>.ErrorResponse(
                    MessageCodes.ER5000,
                    "Internal server error",
                    500));
            }
        }

        // PUT: api/CtvRewards
        [HttpPut]
        [Authorize(Roles = RolesUser.AdminManager)]
        public async Task<IActionResult> Update([FromBody] UpdateCollaboratorRewardRequest request)
        {
            try
            {
                var result = await _collaboratorRewardService.UpdateCollaboratorRewardAsync(request);
                if (result == null)
                {
                    return NotFound(ApiResponse<object>.ErrorResponse(
                        MessageCodes.ER4004,
                        "Report not found",
                        404));
                }
                return Ok(ApiResponse<object>.SuccessResponse(
                    MessageCodes.SC2000,
                    "Update collaborator reward successfully",
                    result));
            }
            catch (InvalidOperationException ex)
            {
                _logger.LogWarning(ex, "Validation error updating CTV reward");
                return BadRequest(ApiResponse<object>.ErrorResponse(
                        MessageCodes.ER4005,
                        ex.Message,
                        400,
                        new List<ErrorDetail>
                        {
                            new ErrorDetail
                            {
                                Field = "request",
                                ErrorCode = MessageCodes.ER4005,
                                Message = ex.Message
                            }
                        }));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating CTV reward");
                return StatusCode(500, ApiResponse<object>.ErrorResponse(
                    MessageCodes.ER5000,
                    "Internal server error " + ex.Message,
                    500));
            }
        }

        // DELETE: api/CtvRewards/5
        [HttpDelete("{id}")]
        [Authorize(Roles = RolesUser.Admin)]
        public async Task<IActionResult> Delete(Guid id)
        {
            try
            {
                var result = await _collaboratorRewardService.DeleteCollaboratorRewardAsync(id);
                if (!result)
                {
                    return BadRequest(ApiResponse<object>.ErrorResponse(MessageCodes.ER4005,
                        "Cannot delete reward because it has payment history",
                        400,
                        new List<ErrorDetail>
                        {
                            new ErrorDetail{
                            Field = "Id",
                            ErrorCode = MessageCodes.ER4005,
                            Message = "Cannot delete reward because it has payment history"
                            }
                        }

                    ));
                }
                return Ok(ApiResponse<object>.SuccessResponse(
                    MessageCodes.SC2000,
                    "Delete reports by collaborator successfully",
                    result));
            }
            catch (InvalidOperationException ex)
            {
                _logger.LogWarning(ex, "Validation error deleting CTV reward");
                return BadRequest(ApiResponse<object>.ErrorResponse(
                    MessageCodes.ER4005,
                    ex.Message,
                    400,
                    new List<ErrorDetail>
                    {
                        new ErrorDetail
                        {
                            Field = "Id",
                            ErrorCode = MessageCodes.ER4005,
                            Message = ex.Message
                        }
                    }));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting CTV reward");
                return StatusCode(500, ApiResponse<object>.ErrorResponse(
                    MessageCodes.ER5000,
                    "Internal server error",
                    500));
            }
        }

        // GET: api/Colla/5/history
        [HttpGet("{id}/history")]
        [Authorize(Roles = RolesUser.AdminManagerRecruiterCollaborator)]
        public async Task<IActionResult> GetHistory(Guid id)
        {
            try
            {
                var reward = await _collaboratorRewardService.GetByIdAsync(id);
                if (reward == null)
                {
                    return NotFound(ApiResponse<object>.ErrorResponse(
                        MessageCodes.ER4004,
                        "History reward not found",
                        404));
                }

                // If user is a collaborator, check if they are the owner of the reward
                if (User.IsInRole("Collaborator"))
                {
                    var userIdClaim = User.FindFirst(System.Security.Claims.ClaimTypes.NameIdentifier)
                        ?? User.FindFirst("sub");

                    if (userIdClaim == null || !Guid.TryParse(userIdClaim.Value, out var userId))
                    {
                        return BadRequest(ApiResponse<object>.ErrorResponse(
                            MessageCodes.ER4005,
                            "User ID not found in token or invalid",
                            400,
                            null));
                    }
                    var collaborator = await _collaboratorService.GetByIdAsync(reward.CollaboratorId);
                    if (collaborator == null)
                    {
                        return BadRequest(ApiResponse<object>.ErrorResponse(
                        MessageCodes.ER4005,
                        "No collaborator found for the current user",
                        400,
                        null));
                    }

                    // Get the collaborator ID for this user
                    // This would require a service method to get collaborator by user ID
                    // For now, we'll just check if the reward's CtvId matches the one in the token
                    if (collaborator.UserId.ToString() != userIdClaim.Value)
                    {
                        return StatusCode(403, ApiResponse<object>.ErrorResponse(
                            MessageCodes.ER4003,
                            "Access token denied",
                            403));
                    }
                }

                var history = await _collaboratorRewardService.GetHistoryByRewardIdAsync(id);
                return Ok(ApiResponse<object>.SuccessResponse(
                    MessageCodes.SC2000,
                    "Get collaborator history reward by Id successfully",
                    history));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting CTV reward history by reward ID: {Id}", id);
                return StatusCode(500, ApiResponse<object>.ErrorResponse(
                    MessageCodes.ER5000,
                    "Internal server error " + ex.Message,
                    500));
            }
        }

        // POST: api/CtvRewards/history
        [HttpPost("history")]
        [Authorize(Roles = RolesUser.AdminManager)]
        public async Task<IActionResult> CreateHistory([FromBody] CreateCollaboratorRewardHistoryRequest request)
        {
            try
            {
                var historyId = await _collaboratorRewardService.CreateCollaboratorRewardHistoryAsync(request);
                return CreatedAtAction(nameof(GetHistory), new { id = request.RewardId },
                ApiResponse<object>.SuccessResponse(
                        MessageCodes.SC2001,
                        "History rewards created successfully",
                        new { id = request.RewardId },
                        201));
            }
            catch (InvalidOperationException ex)
            {
                _logger.LogWarning(ex, "Validation error creating CTV reward history");
                return BadRequest(ApiResponse<object>.ErrorResponse(
                    MessageCodes.ER4005,
                    ex.Message,
                    400,
                    new List<ErrorDetail>
                    {
                        new ErrorDetail
                        {
                            Field = "request",
                            ErrorCode = MessageCodes.ER4005,
                            Message = ex.Message
                        }
                    }));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating CTV reward history");
                return StatusCode(500, ApiResponse<object>.ErrorResponse(
                    MessageCodes.ER5000,
                    "Internal server error",
                    500));
            }
        }

        // GET: api/CtvRewards/operations/{collaboratorId}?action=Summary&month=1&year=2024 (Admin/Manager only)
        [HttpGet("operations/{collaboratorId}")]
        [Authorize(Roles = RolesUser.AdminManager)]
        public async Task<IActionResult> GetRewardOperations(
            Guid collaboratorId,
            [FromQuery] string action,
            [FromQuery] int? month = null,
            [FromQuery] int? year = null)
        {
            try
            {
                // Validate action parameter
                var validActions = new[] { "Summary", "ByType", "UpcomingPayment", "RewardDetails", "RewardHistory", "All" };
                if (string.IsNullOrEmpty(action) || !validActions.Contains(action, StringComparer.OrdinalIgnoreCase))
                {
                    return StatusCode(400, ApiResponse<object>.ErrorResponse(
                    MessageCodes.ER4005,
                    $"Invalid action. Valid actions are: {string.Join(", ", validActions)}",
                    500));
                }

                // Check if collaborator exists
                var collaborator = await _collaboratorService.GetByIdAsync(collaboratorId);
                if (collaborator == null)
                {
                    return NotFound(ApiResponse<object>.ErrorResponse(
                        MessageCodes.ER4004,
                        "Collaborator not found",
                        404));
                }

                var result = await _collaboratorRewardService.GetRewardOperationsAsync(collaboratorId, month, year, action);
                return Ok(ApiResponse<object>.SuccessResponse(
                    MessageCodes.SC2002,
                    "Collaborator updated successfully",
                    result));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting reward operations for collaborator {CollaboratorId} with action {Action}", collaboratorId, action);
                return StatusCode(500, ApiResponse<object>.ErrorResponse(
                    MessageCodes.ER5000,
                    "Internal server error",
                    500));
            }
        }

        // GET: api/CtvRewards/operations?action=Summary&month=1&year=2024 (Collaborator only)
        [HttpGet("operations")]
        [Authorize(Roles = RolesUser.Collaborator)]
        public async Task<IActionResult> GetMyRewardOperations(
            [FromQuery] string action,
            [FromQuery] int? month = null,
            [FromQuery] int? year = null)
        {
            try
            {
                // Validate action parameter
                var validActions = new[] { "Summary", "ByType", "UpcomingPayment", "RewardDetails", "RewardHistory", "All" };
                if (string.IsNullOrEmpty(action) || !validActions.Contains(action, StringComparer.OrdinalIgnoreCase))
                {
                    return BadRequest(ApiResponse<object>.ErrorResponse(
                            MessageCodes.ER4005,
                            $"Invalid action. Valid actions are: {string.Join(", ", validActions)}",
                            400,
                            null));
                }

                // Get userId from token claims
                var userIdClaim = User.FindFirst(System.Security.Claims.ClaimTypes.NameIdentifier)
                    ?? User.FindFirst("sub");

                if (userIdClaim == null || !Guid.TryParse(userIdClaim.Value, out var userId))
                {
                    return BadRequest(ApiResponse<object>.ErrorResponse(
                            MessageCodes.ER4005,
                            "User ID not found in token or invalid",
                            400,
                            null));
                }

                // Find collaborator by userId
                var collaborator = await _collaboratorService.GetByUserIdAsync(userId);
                if (collaborator == null)
                {
                    return NotFound(ApiResponse<object>.ErrorResponse(
                        MessageCodes.ER4004,
                        "Collaborator not found for current user",
                        404));
                }

                var result = await _collaboratorRewardService.GetRewardOperationsAsync(collaborator.CollaboratorId, month, year, action);
                return Ok(ApiResponse<object>.SuccessResponse(
                    MessageCodes.SC2000,
                    "Get operations successfully",
                    result));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting reward operations for user {UserId} with action {Action}", User.FindFirst("sub")?.Value, action);
                return StatusCode(500, ApiResponse<object>.ErrorResponse(
                    MessageCodes.ER5000,
                    "Internal server error",
                    500));
            }
        }
    }
}
