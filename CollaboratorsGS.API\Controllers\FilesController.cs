using CollaboratorsGS.Application.DTOs;
using CollaboratorsGS.Application.Interfaces;
using CollaboratorsGS.Infrastructure.Services;
using CollaboratorsGS.Infrastructure.Utilities;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.IO;
using System.Net.Http;
using System.Threading.Tasks;

namespace CollaboratorsGS.API.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class FilesController : ControllerBase
    {
        private readonly IFileStorageService _fileStorageService;
        private readonly ILogger<FilesController> _logger;
        private readonly FileExtensionService _fileExtensionService;
        private readonly Dictionary<string, string> _extensionToDocumentType;

        public FilesController(
            IFileStorageService fileStorageService,
            ILogger<FilesController> logger,
            FileExtensionService fileExtensionService
            )
        {
            _fileStorageService = fileStorageService;
            _logger = logger;
            _fileExtensionService = fileExtensionService;

            // Khởi tạo dictionary để map extension sang document type
            _extensionToDocumentType = new Dictionary<string, string>(StringComparer.OrdinalIgnoreCase)
            {
                // Images
                { ".jpg", "image" },
                { ".jpeg", "image" },
                { ".png", "image" },
               
                
                // Documents
                { ".pdf", "document" },
                { ".doc", "document" },
                { ".docx", "document" },
                
                
                // Text
                { ".txt", "text" },
                { ".rtf", "text" },
                { ".md", "text" },
                { ".csv", "text" }
            };
        }

        [HttpPost("upload")]
        public async Task<IActionResult> UploadFile(IFormFile file)
        {
            if (file == null || file.Length == 0)
                return BadRequest(ApiResponse<object>.ErrorResponse(
                        MessageCodes.ER4001,
                        "No file was uploaded",
                        400,
                       null));

            try
            {
                var fileName = await _fileStorageService.UploadFileAsync(file);

                var fileId = Path.GetFileNameWithoutExtension(fileName);
                var extension = Path.GetExtension(fileName);

                _fileExtensionService.AddFileExtension(fileId, extension);

                // Xác định document type và file type
                string documentType = "other";
                if (!string.IsNullOrEmpty(extension) && _extensionToDocumentType.TryGetValue(extension, out var type))
                {
                    documentType = type;
                }

                string fileType = !string.IsNullOrEmpty(extension) ? extension.TrimStart('.') : "unknown";

                var fileUrl = $"{Request.Scheme}://{Request.Host}/api/files/{fileId}";


                var response = new FileUploadResponse
                {
                    IsSuccess = true,
                    Message = "File uploaded successfully",
                    FileUrl = fileUrl,
                    FileName = fileName,
                    FileId = fileId,
                    FileSize = file.Length,
                    ContentType = file.ContentType,
                    DocumentType = documentType,
                    FileType = fileType
                };
                return Ok(ApiResponse<object>.SuccessResponse(
                    MessageCodes.SC2000,
                    response.Message,
                    response));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error uploading file");
                return StatusCode(500, ApiResponse<object>.ErrorResponse(
                    MessageCodes.ER5000,
                    "Internal server error",
                    500));
            }
        }

        [HttpDelete("{fileId}")]
        public async Task<IActionResult> DeleteFile(string fileId, [FromQuery] string? extension = null)
        {
            if (string.IsNullOrEmpty(fileId))
                return BadRequest("File ID is required");

            try
            {
                if (string.IsNullOrEmpty(extension))
                {
                    extension = _fileExtensionService.GetFileExtension(fileId);
                }
                else if (!extension.StartsWith('.'))
                {
                    extension = $".{extension}";
                }

                var fileName = $"{fileId}{extension}";

                var result = await _fileStorageService.DeleteFileAsync(fileName);

                if (result)
                {
                    _fileExtensionService.RemoveFileExtension(fileId);
                    return Ok(ApiResponse<object>.SuccessResponse(
                        MessageCodes.SC2000,
                        "File deleted successfully",
                        null ?? new { IsSuccess = true, Message = "File deleted successfully" }));
                }
                else
                    return NotFound(ApiResponse<object>.ErrorResponse(
                        MessageCodes.ER4004,
                        "File not found",
                        404));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting file");
                return StatusCode(500, ApiResponse<object>.ErrorResponse(
                    MessageCodes.ER5000,
                    $"Error deleting file: {ex.Message}",
                    500));
            }
        }



        [HttpGet("{fileId}")]
        public async Task<IActionResult> ViewOrDownloadFile(string fileId, [FromQuery] string? extension = null, [FromQuery] bool download = false)
        {
            if (string.IsNullOrEmpty(fileId))
                return BadRequest(ApiResponse<object>.ErrorResponse(
                    MessageCodes.ER4001,
                    "File ID is required",
                    400));

            try
            {
                if (string.IsNullOrEmpty(extension))
                {
                    extension = _fileExtensionService.GetFileExtension(fileId);
                }
                else if (!extension.StartsWith('.'))
                {
                    extension = $".{extension}";
                }

                var fileName = $"{fileId}{extension}";
                var fileUrl = await _fileStorageService.GetTemporaryUrlAsync(fileName);

                using (var httpClient = new HttpClient())
                {
                    var response = await httpClient.GetAsync(fileUrl);
                    if (!response.IsSuccessStatusCode)
                    {
                        return StatusCode((int)response.StatusCode, new { Message = $"Error fetching file: {response.ReasonPhrase}" });
                    }

                    var contentType = response.Content.Headers.ContentType?.MediaType ?? "application/octet-stream";
                    var fileBytes = await response.Content.ReadAsByteArrayAsync();

                    // if download = true and file is image, show image instead of download
                    if (download)
                    {
                        return File(fileBytes, contentType, fileName);
                    }

                    if (!download)
                    {
                        return Ok(new { FileUrl = fileUrl });
                    }
                    return Ok(ApiResponse<object>.SuccessResponse(
                    MessageCodes.SC2000,
                    "Get file successfully",
                    File(fileBytes, contentType)));

                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error viewing/downloading file");
                return StatusCode(500, ApiResponse<object>.ErrorResponse(
                    MessageCodes.ER5000,
                    $"Error deleting file: {ex.Message}",
                    500));
            }
        }
    }
}