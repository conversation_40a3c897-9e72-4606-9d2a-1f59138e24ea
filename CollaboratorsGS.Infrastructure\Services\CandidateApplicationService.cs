using AutoMapper;
using CollaboratorsGS.Application.DTOs.CandidateApplication;
using CollaboratorsGS.Application.Interfaces;
using CollaboratorsGS.Domain.Entities;
using CollaboratorsGS.Domain.Repositories;
using Microsoft.Extensions.Logging;

namespace CollaboratorsGS.Infrastructure.Services
{
    public class CandidateApplicationService : ICandidateApplicationService
    {
        private readonly ICandidateApplicationRepository _applicationRepository;
        private readonly IRecruitmentPostingRepository _recruitmentPostingRepository;
        private readonly IMapper _mapper;
        private readonly ILogger<CandidateApplicationService> _logger;

        public CandidateApplicationService(
            ICandidateApplicationRepository applicationRepository,
            IRecruitmentPostingRepository recruitmentPostingRepository,
            IMapper mapper,
            ILogger<CandidateApplicationService> logger)
        {
            _applicationRepository = applicationRepository;
            _recruitmentPostingRepository = recruitmentPostingRepository;
            _mapper = mapper;
            _logger = logger;
        }

        public async Task<CandidateApplicationDto?> GetByIdAsync(Guid applicationId)
        {
            try
            {
                var application = await _applicationRepository.GetByIdAsync(applicationId);
                if (application == null)
                    return null;

                return _mapper.Map<CandidateApplicationDto>(application);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting candidate application with ID {ApplicationId}", applicationId);
                throw;
            }
        }

        public async Task<IEnumerable<CandidateApplicationDto>> GetAllAsync()
        {
            try
            {
                var applications = await _applicationRepository.GetAllAsync();
                return _mapper.Map<IEnumerable<CandidateApplicationDto>>(applications);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting all candidate applications");
                throw;
            }
        }

        public async Task<IEnumerable<CandidateApplicationDto>> GetByCandidateAsync(Guid candidateId)
        {
            try
            {
                var applications = await _applicationRepository.GetByCandidateAsync(candidateId);
                return _mapper.Map<IEnumerable<CandidateApplicationDto>>(applications);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting applications for candidate {CandidateId}", candidateId);
                throw;
            }
        }

        public async Task<IEnumerable<CandidateApplicationDto>> GetByPostingAsync(Guid postingId)
        {
            try
            {
                var applications = await _applicationRepository.GetByPostingAsync(postingId);
                return _mapper.Map<IEnumerable<CandidateApplicationDto>>(applications);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting applications for posting {PostingId}", postingId);
                throw;
            }
        }

        public async Task<IEnumerable<CandidateApplicationDto>> GetByStatusAsync(string status)
        {
            try
            {
                var applications = await _applicationRepository.GetByStatusAsync(status);
                return _mapper.Map<IEnumerable<CandidateApplicationDto>>(applications);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting applications with status {Status}", status);
                throw;
            }
        }

        public async Task<Guid> CreateApplicationAsync(CreateCandidateApplicationRequest request)
        {
            try
            {
                var application = _mapper.Map<CandidateApplication>(request);
                application.ApplicationId = Guid.NewGuid();
                application.ApplicationDate = DateTime.UtcNow;
                application.Status = "Pending";
                
                return await _applicationRepository.CreateAsync(application);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating candidate application");
                throw;
            }
        }

        public async Task<bool> UpdateApplicationAsync(UpdateCandidateApplicationRequest request)
        {
            try
            {
                // Check if application exists
                var existingApplication = await _applicationRepository.GetByIdAsync(request.ApplicationId);
                if (existingApplication == null)
                {
                    _logger.LogWarning("Candidate application with ID {ApplicationId} not found", request.ApplicationId);
                    return false;
                }
                
                // Update only the fields that are provided in the request
                existingApplication.Status = request.Status;
                existingApplication.InterviewRound1Result = request.InterviewRound1Result ?? existingApplication.InterviewRound1Result;
                existingApplication.InterviewRound1Date = request.InterviewRound1Date ?? existingApplication.InterviewRound1Date;
                existingApplication.InterviewRound2Result = request.InterviewRound2Result ?? existingApplication.InterviewRound2Result;
                existingApplication.InterviewRound2Date = request.InterviewRound2Date ?? existingApplication.InterviewRound2Date;
                existingApplication.OnboardDate = request.OnboardDate ?? existingApplication.OnboardDate;
                existingApplication.WarrantyEndDate = request.WarrantyEndDate ?? existingApplication.WarrantyEndDate;
                existingApplication.UpdatedAt = DateTime.UtcNow;
                
                return await _applicationRepository.UpdateAsync(existingApplication);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating candidate application with ID {ApplicationId}", request.ApplicationId);
                throw;
            }
        }

        public async Task<bool> DeleteApplicationAsync(Guid applicationId)
        {
            try
            {
                // Check if application exists
                var existingApplication = await _applicationRepository.GetByIdAsync(applicationId);
                if (existingApplication == null)
                {
                    _logger.LogWarning("Candidate application with ID {ApplicationId} not found", applicationId);
                    return false;
                }

                return await _applicationRepository.DeleteAsync(applicationId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting candidate application with ID {ApplicationId}", applicationId);
                throw;
            }
        }

        public async Task<IEnumerable<CandidateAppliedHistoryDto>> GetCandidateAppliedHistoriesAsync(Guid candidateId, Guid? postingId = null)
        {
            try
            {
                var applications = await _applicationRepository.GetByCandidateAsync(candidateId);

                // Filter by posting ID if provided
                if (postingId.HasValue)
                {
                    applications = applications.Where(a => a.PostingId == postingId.Value);
                }

                var result = new List<CandidateAppliedHistoryDto>();

                foreach (var application in applications)
                {
                    var posting = await _recruitmentPostingRepository.GetByIdAsync(application.PostingId);
                    if (posting != null)
                    {
                        result.Add(new CandidateAppliedHistoryDto
                        {
                            ApplicationId = application.ApplicationId,
                            PostingId = application.PostingId,
                            JobTitle = posting.Title,
                            JobCode = posting.ReferCode,
                            Level = posting.Level,
                            Position = posting.Position,
                            WorkingLocation = posting.WorkingLocation,
                            SalaryFrom = posting.SalaryFrom,
                            SalaryTo = posting.SalaryTo,
                            ApplicationDate = application.ApplicationDate,
                            Status = application.Status,
                            ExpiredAt = posting.ExpiredAt
                        });
                    }
                }

                return result.OrderByDescending(x => x.ApplicationDate);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting applied positions for candidate {CandidateId}, posting {PostingId}", candidateId, postingId);
                throw;
            }
        }

        public async Task<IEnumerable<CandidateApplicationDetailDto>> GetCandidateApplicationDetailAsync(Guid candidateId, Guid? postingId = null)
        {
            try
            {
                var applications = await _applicationRepository.GetByCandidateAsync(candidateId);

                // Filter by posting ID if provided
                if (postingId.HasValue)
                {
                    applications = applications.Where(a => a.PostingId == postingId.Value);
                }

                var result = new List<CandidateApplicationDetailDto>();

                foreach (var application in applications)
                {
                    var posting = await _recruitmentPostingRepository.GetByIdAsync(application.PostingId);
                    if (posting != null)
                    {
                        var statusHistory = new List<ApplicationStatusDetailDto>();

                        // Add application submitted status
                        statusHistory.Add(new ApplicationStatusDetailDto
                        {
                            Status = "Submitted",
                            StatusDisplayName = "Đã nộp",
                            Date = application.ApplicationDate,
                            Description = "Hồ sơ đã được nộp thành công"
                        });

                        // Add interview round 1 if exists
                        if (application.InterviewRound1Date.HasValue)
                        {
                            statusHistory.Add(new ApplicationStatusDetailDto
                            {
                                Status = "Interview1",
                                StatusDisplayName = "Phỏng vấn vòng 1",
                                Date = application.InterviewRound1Date.Value,
                                Description = $"Kết quả: {application.InterviewRound1Result ?? "Chưa có kết quả"}"
                            });
                        }

                        // Add interview round 2 if exists
                        if (application.InterviewRound2Date.HasValue)
                        {
                            statusHistory.Add(new ApplicationStatusDetailDto
                            {
                                Status = "Interview2",
                                StatusDisplayName = "Phỏng vấn vòng 2",
                                Date = application.InterviewRound2Date.Value,
                                Description = $"Kết quả: {application.InterviewRound2Result ?? "Chưa có kết quả"}"
                            });
                        }

                        // Add onboard status if exists
                        if (application.OnboardDate.HasValue)
                        {
                            statusHistory.Add(new ApplicationStatusDetailDto
                            {
                                Status = "Onboard",
                                StatusDisplayName = "Đạt",
                                Date = application.OnboardDate.Value,
                                Description = "Đã vượt qua vòng phỏng vấn, đang chờ offer"
                            });
                        }

                        result.Add(new CandidateApplicationDetailDto
                        {
                            ApplicationId = application.ApplicationId,
                            PostingId = application.PostingId,
                            JobTitle = posting.Title,
                            JobCode = posting.ReferCode,
                            StatusHistory = statusHistory.OrderBy(x => x.Date).ToList()
                        });
                    }
                }

                return result.OrderByDescending(x => x.StatusHistory.FirstOrDefault()?.Date ?? DateTime.MinValue);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting application history for candidate {CandidateId}, posting {PostingId}", candidateId, postingId);
                throw;
            }
        }
    }
}
