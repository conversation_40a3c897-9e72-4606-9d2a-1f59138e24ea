using AutoMapper;
using CollaboratorsGS.Application.DTOs.CollaboratorLevel;
using CollaboratorsGS.Application.Interfaces;
using CollaboratorsGS.Domain.Entities;
using CollaboratorsGS.Domain.Repositories;

namespace CollaboratorsGS.Infrastructure.Services
{
    public class CollaboratorLevelService : ICollaboratorLevelService
    {
        private readonly ICollaboratorLevelRepository _collaboratorLevelRepository;
        private readonly IMapper _mapper;

        public CollaboratorLevelService(
            ICollaboratorLevelRepository collaboratorLevelRepository,
            IMapper mapper)
        {
            _collaboratorLevelRepository = collaboratorLevelRepository;
            _mapper = mapper;
        }

        public async Task<CollaboratorLevelDto?> GetByIdAsync(Guid levelId)
        {
            var level = await _collaboratorLevelRepository.GetByIdAsync(levelId);
            return _mapper.Map<CollaboratorLevelDto>(level);
        }

        public async Task<IEnumerable<CollaboratorLevelDto>> GetAllAsync()
        {
            var levels = await _collaboratorLevelRepository.GetAllAsync();
            return _mapper.Map<IEnumerable<CollaboratorLevelDto>>(levels);
        }

        public async Task<Guid> CreateCollaboratorLevelAsync(CreateCollaboratorLevelRequest request)
        {
            // Map request to entity
            var level = new CollaboratorLevel
            {
                LevelName = request.LevelName,
                MinKpiThreshold = request.MinKpiThreshold,
                CommissionRate = request.CommissionRate,
                Round1Bonus = request.Round1Bonus,
                Round2Bonus = request.Round2Bonus,
                OnboardBonus = request.OnboardBonus,
                Description = request.Description
            };

            // Create level
            return await _collaboratorLevelRepository.CreateAsync(level);
        }

        public async Task<CollaboratorLevelDto?> GetCreatedCollaboratorLevelAsync(Guid levelId)
        {
            return await GetByIdAsync(levelId);
        }

        public async Task<CollaboratorLevelDto?> UpdateCollaboratorLevelAsync(UpdateCollaboratorLevelRequest request)
        {
            // Check if level exists
            var existingLevel = await _collaboratorLevelRepository.GetByIdAsync(request.LevelId);
            if (existingLevel == null)
            {
                throw new InvalidOperationException($"Level with ID {request.LevelId} not found");
            }

            // Map request to entity
            var level = new CollaboratorLevel
            {
                LevelId = request.LevelId,
                LevelName = request.LevelName,
                MinKpiThreshold = request.MinKpiThreshold,
                CommissionRate = request.CommissionRate,
                Round1Bonus = request.Round1Bonus,
                Round2Bonus = request.Round2Bonus,
                OnboardBonus = request.OnboardBonus,
                Description = request.Description
            };

            // Update level
            var result = await _collaboratorLevelRepository.UpdateAsync(level);

            if (!result)
                return null;

            // Get the updated level
            return await GetByIdAsync(request.LevelId);
        }

        public async Task<bool> DeleteCollaboratorLevelAsync(Guid levelId)
        {
            // Check if level exists
            var existingLevel = await _collaboratorLevelRepository.GetByIdAsync(levelId);
            if (existingLevel == null)
            {
                throw new InvalidOperationException($"Level with ID {levelId} not found");
            }

            // Delete level
            return await _collaboratorLevelRepository.DeleteAsync(levelId);
        }
    }
}
