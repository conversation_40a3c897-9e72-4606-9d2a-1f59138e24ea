using AutoMapper;
using CollaboratorsGS.Application.DTOs.CollaboratorViolation;
using CollaboratorsGS.Application.Interfaces;
using CollaboratorsGS.Domain.Entities;
using CollaboratorsGS.Domain.Repositories;

namespace CollaboratorsGS.Infrastructure.Services
{
    public class CollaboratorViolationService : ICollaboratorViolationService
    {
        private readonly ICollaboratorViolationRepository _violationRepository;
        private readonly ICollaboratorRepository _collaboratorRepository;
        private readonly IUserRepository _userRepository;
        private readonly IMapper _mapper;

        public CollaboratorViolationService(
            ICollaboratorViolationRepository violationRepository,
            ICollaboratorRepository collaboratorRepository,
            IUserRepository userRepository,
            IMapper mapper)
        {
            _violationRepository = violationRepository;
            _collaboratorRepository = collaboratorRepository;
            _userRepository = userRepository;
            _mapper = mapper;
        }

        public async Task<IEnumerable<CollaboratorViolationDto>> GetAllAsync()
        {
            var violations = await _violationRepository.GetAllAsync();
            return _mapper.Map<IEnumerable<CollaboratorViolationDto>>(violations);
        }

        public async Task<CollaboratorViolationDto?> GetByIdAsync(Guid violationId)
        {
            var violation = await _violationRepository.GetByIdAsync(violationId);
            return violation != null ? _mapper.Map<CollaboratorViolationDto>(violation) : null;
        }

        public async Task<IEnumerable<CollaboratorViolationDto>> GetByCollaboratorIdAsync(Guid collaboratorId)
        {
            // Check if collaborator exists
            var collaborator = await _collaboratorRepository.GetByIdAsync(collaboratorId);
            if (collaborator == null)
                throw new InvalidOperationException($"Collaborator with ID {collaboratorId} not found");

            var violations = await _violationRepository.GetByCtvIdAsync(collaboratorId);
            return _mapper.Map<IEnumerable<CollaboratorViolationDto>>(violations);
        }

        public async Task<IEnumerable<CollaboratorViolationDto>> GetByTypeAsync(string violationType)
        {
            var violations = await _violationRepository.GetByTypeAsync(violationType);
            return _mapper.Map<IEnumerable<CollaboratorViolationDto>>(violations);
        }

        public async Task<Guid> CreateViolationAsync(CreateCollaboratorViolationRequest request)
        {
            // Check if collaborator exists
            var collaborator = await _collaboratorRepository.GetByIdAsync(request.CtvId);
            if (collaborator == null)
                throw new InvalidOperationException($"Collaborator with ID {request.CtvId} not found");

            // Check if handler exists if provided
            if (request.HandledBy.HasValue)
            {
                var handler = await _userRepository.GetByIdAsync(request.HandledBy.Value);
                if (handler == null)
                    throw new InvalidOperationException($"User with ID {request.HandledBy.Value} not found");
            }

            var violation = _mapper.Map<CollaboratorViolation>(request);
            violation.CreatedAt = DateTime.UtcNow;
            
            return await _violationRepository.CreateAsync(violation);
        }

        public async Task<bool> UpdateViolationAsync(UpdateCollaboratorViolationRequest request)
        {
            var existingViolation = await _violationRepository.GetByIdAsync(request.ViolationId);
            if (existingViolation == null)
                throw new InvalidOperationException($"Violation with ID {request.ViolationId} not found");

            // Check if handler exists if provided
            if (request.HandledBy.HasValue)
            {
                var handler = await _userRepository.GetByIdAsync(request.HandledBy.Value);
                if (handler == null)
                    throw new InvalidOperationException($"User with ID {request.HandledBy.Value} not found");
            }

            // Update only the fields that are provided
            if (!string.IsNullOrEmpty(request.ViolationType))
                existingViolation.ViolationType = request.ViolationType;
            
            if (!string.IsNullOrEmpty(request.Description))
                existingViolation.Description = request.Description;
            
            if (request.HandledBy.HasValue)
                existingViolation.HandledBy = request.HandledBy;
            
            if (request.HandledAt.HasValue)
                existingViolation.HandledAt = request.HandledAt;

            return await _violationRepository.UpdateAsync(existingViolation);
        }

        public async Task<bool> DeleteViolationAsync(Guid violationId)
        {
            var existingViolation = await _violationRepository.GetByIdAsync(violationId);
            if (existingViolation == null)
                throw new InvalidOperationException($"Violation with ID {violationId} not found");

            return await _violationRepository.DeleteAsync(violationId);
        }
    }
}
