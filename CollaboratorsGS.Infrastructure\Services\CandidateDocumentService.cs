using AutoMapper;
using CollaboratorsGS.Application.DTOs.CandidateDocument;
using CollaboratorsGS.Application.Interfaces;
using CollaboratorsGS.Domain.Entities;
using CollaboratorsGS.Domain.Repositories;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace CollaboratorsGS.Infrastructure.Services
{
    public class CandidateDocumentService : ICandidateDocumentService
    {
        private readonly ICandidateDocumentRepository _documentRepository;
        private readonly ICandidateRepository _candidateRepository;
        private readonly IMapper _mapper;

        public CandidateDocumentService(
            ICandidateDocumentRepository documentRepository,
            ICandidateRepository candidateRepository,
            IMapper mapper)
        {
            _documentRepository = documentRepository;
            _candidateRepository = candidateRepository;
            _mapper = mapper;
        }

        public async Task<Guid> CreateDocumentAsync(CreateCandidateDocumentRequest request)
        {
            // Validate candidate exists
            var candidate = await _candidateRepository.GetByIdAsync(request.CandidateId);
            if (candidate == null)
            {
                throw new InvalidOperationException($"Candidate with ID {request.CandidateId} not found");
            }

            var document = _mapper.Map<CandidateDocument>(request);
            document.UploadedAt = DateTime.UtcNow;

            return await _documentRepository.CreateAsync(document);
        }

        public async Task<CandidateDocumentDto?> GetByIdAsync(Guid documentId)
        {
            var document = await _documentRepository.GetByIdAsync(documentId);
            if (document == null)
                return null;

            return _mapper.Map<CandidateDocumentDto>(document);
        }

        public async Task<IEnumerable<CandidateDocumentDto>> GetByCandidateIdAsync(Guid candidateId)
        {
            var documents = await _documentRepository.GetByCandidateIdAsync(candidateId);
            return _mapper.Map<IEnumerable<CandidateDocumentDto>>(documents);
        }

        public async Task<bool> DeleteDocumentAsync(Guid documentId)
        {
            return await _documentRepository.DeleteAsync(documentId);
        }
    }
}

