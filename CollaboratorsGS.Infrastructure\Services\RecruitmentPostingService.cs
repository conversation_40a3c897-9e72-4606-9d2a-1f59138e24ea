using AutoMapper;
using CollaboratorsGS.Application.DTOs.RecruitmentPosting;
using CollaboratorsGS.Application.Interfaces;
using CollaboratorsGS.Domain.Entities;
using CollaboratorsGS.Domain.Repositories;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using System.Security.Claims;

namespace CollaboratorsGS.Infrastructure.Services
{
    public class RecruitmentPostingService : IRecruitmentPostingService
    {
        private readonly IRecruitmentPostingRepository _recruitmentPostingRepository;
        private readonly IActivityLogService _activityLogService;
        private readonly IHttpContextAccessor _httpContextAccessor;
        private readonly IMapper _mapper;
        private readonly ILogger<RecruitmentPostingService> _logger;

        public RecruitmentPostingService(
            IRecruitmentPostingRepository recruitmentPostingRepository,
            IActivityLogService activityLogService,
            IHttpContextAccessor httpContextAccessor,
            I<PERSON><PERSON><PERSON> mapper,
            ILogger<RecruitmentPostingService> logger)
        {
            _recruitmentPostingRepository = recruitmentPostingRepository;
            _activityLogService = activityLogService;
            _httpContextAccessor = httpContextAccessor;
            _mapper = mapper;
            _logger = logger;
        }

        public async Task<RecruitmentPostingDto?> GetByIdAsync(Guid postingId)
        {
            try
            {
                var posting = await _recruitmentPostingRepository.GetByIdAsync(postingId);
                if (posting == null)
                    return null;

                return _mapper.Map<RecruitmentPostingDto>(posting);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting recruitment posting with ID {PostingId}", postingId);
                throw;
            }
        }

        public async Task<RecruitmentPostingDetailDto?> GetDetailByIdAsync(Guid postingId)
        {
            try
            {
                var posting = await _recruitmentPostingRepository.GetByIdWithDetailAsync(postingId);
                if (posting == null)
                    return null;

                return _mapper.Map<RecruitmentPostingDetailDto>(posting);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting recruitment posting detail with ID {PostingId}", postingId);
                throw;
            }
        }

        public async Task<IEnumerable<RecruitmentPostingDto>> GetAllAsync()
        {
            try
            {
                var postings = await _recruitmentPostingRepository.GetAllAsync();
                return _mapper.Map<IEnumerable<RecruitmentPostingDto>>(postings);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting all recruitment postings");
                throw;
            }
        }

        public async Task<IEnumerable<RecruitmentPostingDto>> GetByReferCodeAsync(string referCode)
        {
            try
            {
                var postings = await _recruitmentPostingRepository.GetByReferCodeAsync(referCode);
                return _mapper.Map<IEnumerable<RecruitmentPostingDto>>(postings);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting recruitment postings by refer code {ReferCode}", referCode);
                throw;
            }
        }

        public async Task<SearchRecruitmentPostingResponse> SearchAsync(SearchRecruitmentPostingRequest request)
        {
            try
            {
                var (data, totalCount) = await _recruitmentPostingRepository.SearchAsync(
                    keyword: request.keyword,
                    level: request.level,
                    workingLocation: request.working_location,
                    salaryFrom: request.salary_from,
                    salaryTo: request.salary_to,
                    isUrgent: request.is_urgent,
                    isHot: request.is_hot,
                    status: request.status,
                    page: request.page,
                    pageSize: request.page_size,
                    sortBy: request.sort_by ?? "created_at",
                    sortOrder: request.sort_order ?? "desc");

                var mappedData = _mapper.Map<List<RecruitmentPostingDto>>(data);

                var totalPages = (int)Math.Ceiling((double)totalCount / request.page_size);

                return new SearchRecruitmentPostingResponse
                {
                    data = mappedData,
                    total_count = totalCount,
                    page = request.page,
                    page_size = request.page_size,
                    total_pages = totalPages,
                    has_next_page = request.page < totalPages,
                    has_previous_page = request.page > 1
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error searching recruitment postings");
                throw;
            }
        }

        public async Task<Guid> CreateAsync(CreateRecruitmentPostingRequest request)
        {
            try
            {
                var posting = _mapper.Map<RecruitmentPosting>(request);

                // Set additional properties
                posting.PostingId = Guid.NewGuid();
                posting.CreatedAt = DateTime.UtcNow;
                posting.ViewCount = 0;
                posting.ReferralCount = 0;

                var postingId = await _recruitmentPostingRepository.CreateAsync(posting);

                // Log activity
                var newValue = $"Title: {posting.Title}, Position: {posting.Position}, Level: {posting.Level}";
                await LogActivityAsync("Create", "RecruitmentPosting", postingId, null, newValue);

                return postingId;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating recruitment posting");
                throw;
            }
        }

        public async Task<bool> UpdateAsync(Guid postingId, UpdateRecruitmentPostingRequest request)
        {
            try
            {
                // Check if posting exists
                var existingPosting = await _recruitmentPostingRepository.GetByIdAsync(postingId);
                if (existingPosting == null)
                {
                    _logger.LogWarning("Recruitment posting with ID {PostingId} not found", postingId);
                    return false;
                }

                // Store old values for audit log
                var oldValue = $"Title: {existingPosting.Title}, Position: {existingPosting.Position}, Level: {existingPosting.Level}";

                // Use AutoMapper to map only non-null properties from request to existing entity
                _mapper.Map(request, existingPosting);

                // Always update the timestamp
                existingPosting.UpdatedAt = DateTime.UtcNow;

                var result = await _recruitmentPostingRepository.UpdateAsync(existingPosting);

                if (result)
                {
                    // Log activity with old and new values
                    var newValue = $"Title: {existingPosting.Title}, Position: {existingPosting.Position}, Level: {existingPosting.Level}";
                    await LogActivityAsync("Update", "RecruitmentPosting", postingId, oldValue, newValue);
                }

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating recruitment posting with ID {PostingId}", postingId);
                throw;
            }
        }

        public async Task<bool> DeleteAsync(Guid postingId)
        {
            try
            {
                // Check if posting exists
                var existingPosting = await _recruitmentPostingRepository.GetByIdAsync(postingId);
                if (existingPosting == null)
                {
                    _logger.LogWarning("Recruitment posting with ID {PostingId} not found", postingId);
                    return false;
                }

                return await _recruitmentPostingRepository.DeleteAsync(postingId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting recruitment posting with ID {PostingId}", postingId);
                throw;
            }
        }

        public async Task<bool> IncrementViewCountAsync(Guid postingId)
        {
            try
            {
                return await _recruitmentPostingRepository.IncrementViewCountAsync(postingId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error incrementing view count for posting {PostingId}", postingId);
                throw;
            }
        }

        public async Task<bool> IncrementReferralCountAsync(Guid postingId)
        {
            try
            {
                return await _recruitmentPostingRepository.IncrementReferralCountAsync(postingId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error incrementing referral count for posting {PostingId}", postingId);
                throw;
            }
        }

        private async Task LogActivityAsync(string action, string entityType, Guid entityId, string? oldValue = null, string? newValue = null)
        {
            try
            {
                var userIdClaim = _httpContextAccessor.HttpContext?.User?.FindFirst(ClaimTypes.NameIdentifier)
                    ?? _httpContextAccessor.HttpContext?.User?.FindFirst("sub");

                if (userIdClaim != null && Guid.TryParse(userIdClaim.Value, out var userId))
                {
                    // Get IP address from HttpContext
                    var ipAddress = GetClientIpAddress();

                    await _activityLogService.LogActivityAsync(userId, action, entityType, entityId, oldValue, newValue, ipAddress);
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Failed to log activity for {Action} on {EntityType} {EntityId}", action, entityType, entityId);
                // Don't throw - logging failure shouldn't break the main operation
            }
        }

        private string GetClientIpAddress()
        {
            var context = _httpContextAccessor.HttpContext;
            if (context == null) return "Unknown";

            // Try to get IP from various headers (same logic as RequestLoggingMiddleware)
            var headerNames = new[]
            {
                "X-Forwarded-For",
                "Forwarded",
                "X-Real-IP",
                "CF-Connecting-IP",
                "True-Client-IP",
                "X-Client-IP"
            };

            foreach (var headerName in headerNames)
            {
                if (context.Request.Headers.TryGetValue(headerName, out var headerValue) &&
                    !string.IsNullOrEmpty(headerValue))
                {
                    var ip = headerValue.ToString().Split(',')[0].Trim();
                    if (!string.IsNullOrEmpty(ip))
                    {
                        return ip;
                    }
                }
            }

            // Fallback to RemoteIpAddress
            return context.Connection.RemoteIpAddress?.ToString() ?? "Unknown";
        }
    }
}
