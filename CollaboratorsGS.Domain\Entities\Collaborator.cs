
namespace CollaboratorsGS.Domain.Entities
{
    public class Collaborator
    {
        // Primary key and basic information
        public Guid CollaboratorId { get; set; }
        public Guid UserId { get; set; }
        public string FullName { get; set; } = string.Empty;
        public string PhoneNumber { get; set; } = string.Empty;
        public string Email { get; set; } = string.Empty;
        public Guid LevelId { get; set; }
        public string Status { get; set; } = string.Empty;
        public DateTime? LastLevelUpdatedAt { get; set; }
        public DateTime CreatedAt { get; set; }
        public Guid? ApprovedBy { get; set; }
        public DateTime? ApprovedDate { get; set; }
        public DateTime? UpdatedAt { get; set; }

        // Navigation properties
        public User? User { get; set; }
        public User? Approver { get; set; }
        public CollaboratorLevel? Level { get; set; }
        public CollaboratorProfile? Profile { get; set; }
        public ICollection<Candidate>? Candidates { get; set; }
        public ICollection<CollaboratorKpi>? KpiRecords { get; set; }

    }
}
