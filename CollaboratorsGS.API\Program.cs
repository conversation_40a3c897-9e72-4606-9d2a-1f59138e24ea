using CollaboratorsGS.Infrastructure;
using CollaboratorsGS.Infrastructure.Configurations;
using CollaboratorsGS.Infrastructure.Extensions;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Options;
using Minio;
using OfficeOpenXml;

var builder = WebApplication.CreateBuilder(args);
Dapper.DefaultTypeMap.MatchNamesWithUnderscores = true;


// Add service defaults (OpenTelemetry, health checks, etc.)
builder.AddServiceDefaults();


// Add services to the container.
builder.Services.AddControllers(options =>
{
    // Add custom model binder for DateTime parsing
    options.ModelBinderProviders.Insert(0, new CollaboratorsGS.API.ModelBinders.DateTimeModelBinderProvider());
});

// Add Infrastructure layer
builder.Services.AddInfrastructure(builder.Configuration);

// Add Infrastructure extensions (AutoMapper, JWT, CORS, Swagger)
builder.Services.AddInfrastructureExtensions(builder.Configuration);
builder.Services.Configure<FileStorageOptions>(
    builder.Configuration.GetSection("FileStorage"));

builder.WebHost.ConfigureKestrel(options =>
{
    options.ListenAnyIP(80); // Changed from port 80 to 5000
});

var app = builder.Build();

// Configure the HTTP request pipeline.
app.UseInfrastructureMiddlewares();

app.UseHttpsRedirection();
app.UseRouting();
app.UseCors();

// Enable serving static files and create directory if it doesn't exist
var uploadsPath = Path.Combine(builder.Environment.WebRootPath ?? Path.Combine(Directory.GetCurrentDirectory(), "wwwroot"), "uploads");
if (!Directory.Exists(uploadsPath))
{
    Directory.CreateDirectory(uploadsPath);
}
app.UseStaticFiles();

app.UseAuthentication();
app.UseAuthorization();

app.UseRequestLogging();

app.MapControllers();

// Map health check endpoints
app.MapDefaultEndpoints();

// Seed the database
try
{
    // Uncomment this when you have fixed the seeding issues
    // await DependencyInjection.SeedDatabaseAsync(app.Services);
}
catch (Exception ex)
{
    var logger = app.Services.GetRequiredService<ILogger<Program>>();
    logger.LogError(ex, "An error occurred while seeding the database.");
}

app.Run();

