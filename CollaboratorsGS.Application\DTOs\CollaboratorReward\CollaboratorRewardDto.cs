using System;

namespace CollaboratorsGS.Application.DTOs.CollaboratorReward
{
    public class CollaboratorRewardDto
    {
        public Guid RewardId { get; set; }
        public Guid CollaboratorId { get; set; }
        public string CollaboratorName { get; set; } = string.Empty;
        public Guid ApplicationId { get; set; }
        public string RewardType { get; set; } = string.Empty;
        public decimal Amount { get; set; }
        public Guid LevelId { get; set; }
        public string LevelName { get; set; } = string.Empty;
        public DateTime RewardDate { get; set; }
        public DateTime ScheduledPaymentDate { get; set; }
        public string Status { get; set; } = string.Empty;
    }
}
