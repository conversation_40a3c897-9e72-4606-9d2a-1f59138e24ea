using CollaboratorsGS.Domain.Entities;

namespace CollaboratorsGS.Domain.Repositories
{
    public interface IObjectDataRepository
    {
        Task<ObjectData?> GetByIdAsync(Guid objectId); // Changed from int to Guid
        Task<IEnumerable<ObjectData>> GetAllAsync();
        Task<IEnumerable<ObjectData>> GetByTypeAsync(string objectType);
        Task<ObjectData?> GetByCodeAsync(string objectCode);
        Task<IEnumerable<ObjectData>> GetByValueAsync(string objectValue);
        Task<Guid> CreateAsync(ObjectData objectData); // Changed return type from int to Guid
        Task<bool> UpdateAsync(ObjectData objectData);
        Task<bool> DeleteAsync(Guid objectId); // Changed from int to Guid
    }
}
