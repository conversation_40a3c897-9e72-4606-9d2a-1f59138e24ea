-- Create base tables that have no dependencies

-- Create Roles table
IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'roles' AND schema_id = SCHEMA_ID('dbo'))
BEGIN
CREATE TABLE [dbo].[roles] (
    [role_id] UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
    [role_name] NVARCHAR(255) NOT NULL,
    [description] NVARCHAR(255)
);
END
-- Tạo bảng companies
IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'companies' AND schema_id = SCHEMA_ID('dbo'))
BEGIN
CREATE TABLE [dbo].[companies] (
    [company_id] UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
    [company_name] NVARCHAR(255) UNIQUE NOT NULL,
    [phone_number] NVARCHAR(20),
    [email] NVARCHAR(255),
    [created_at] DATETIME DEFAULT GETDATE(),
    [updated_at] DATETIME
);
END
GO
GO
-- <PERSON>ạ<PERSON> bảng branches
IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'branches' AND schema_id = SCHEMA_ID('dbo'))
BEGIN
CREATE TABLE [dbo].[branch_idranches] (
    [branch_id] UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
    [company_id] UNIQUEIDENTIFIER NOT NULL,
    [branch_name] NVARCHAR(255) NOT NULL,
    [phone_number] NVARCHAR(20),
    [email] NVARCHAR(255),
    [created_at] DATETIME DEFAULT GETDATE(),
    [updated_at] DATETIME,
    CONSTRAINT [fk_branches_companies] FOREIGN KEY ([company_id]) REFERENCES [dbo].[Companies] ([company_id])
);
END 
GO
-- Create Permissions table
IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'permissions' AND schema_id = SCHEMA_ID('dbo'))
BEGIN
CREATE TABLE [dbo].[permissions] (
    [permission_id] UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
    [permission_name] NVARCHAR(255) NOT NULL,
    [description] NVARCHAR(255)
);
END
GO

-- Create CtvLevels table
IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'collaborator_levels' AND schema_id = SCHEMA_ID('dbo'))
BEGIN
CREATE TABLE [dbo].[CollaboratorLevels] (
    [level_id] UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
    [level_name] NVARCHAR(50) NOT NULL,
    [min_kpi_threshold] FLOAT,
    [commission_rate] FLOAT,
    [round1_bonus] DECIMAL(18, 2),
    [round2_bonus] DECIMAL(18, 2),
    [onboard_bonus] DECIMAL(18, 2),
    [description] NVARCHAR(255)
);
END
GO
-- Create Users table
IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'users' AND schema_id = SCHEMA_ID('dbo'))
BEGIN
CREATE TABLE [dbo].[users] (
    [user_id] UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
    [username] NVARCHAR(255) NOT NULL UNIQUE,
    [password] NVARCHAR(255) NOT NULL,
    [role_id] UNIQUEIDENTIFIER NOT NULL,
    [full_name] NVARCHAR(255),
    [email] NVARCHAR(255) UNIQUE,
    [phone_number] NVARCHAR(50) UNIQUE,
    [created_at] DATETIME NOT NULL DEFAULT GETDATE(),
    [last_login] DATETIME,
    [is_active] BIT DEFAULT 1,
    CONSTRAINT [FK_Users_Roles] FOREIGN KEY ([role_id]) REFERENCES [dbo].[Roles] ([role_id])
);
END
GO

-- Create Departments table
IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'departments' AND schema_id = SCHEMA_ID('dbo'))
BEGIN
CREATE TABLE [dbo].[departments] (
    [department_id] UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
    [department_name] NVARCHAR(255) NOT NULL,
    [manager_id] UNIQUEIDENTIFIER NULL,
    [branch_id] UNIQUEIDENTIFIER NULL,
    CONSTRAINT [fk_departments_users] FOREIGN KEY ([manager_id]) REFERENCES [dbo].[Users] ([user_id]),
    CONSTRAINT [fk_departments_branches] FOREIGN KEY ([branch_id]) REFERENCES [dbo].[Branches] ([branch_id])  
);
END
GO

-- Create administrative_units table
IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'administrative_units' AND schema_id = SCHEMA_ID('dbo'))
BEGIN
CREATE TABLE [dbo].[administrative_units] (
    unit_id UNIQUEIDENTIFIER PRIMARY KEY,  
    parent_unit_id UNIQUEIDENTIFIER,
    unit_name NVARCHAR(100),
    unit_type NVARCHAR(50),
    [id] [int] NULL,
	[code_id] [varchar](20) NULL,
    FOREIGN KEY (parent_unit_id) REFERENCES administrative_units(unit_id)
);
END
GO
