namespace CollaboratorsGS.Application.DTOs
{
    public class ApiResponse<T>
    {
        public bool Success { get; set; }
        public string MessageCode { get; set; } = default!;
        public string Message { get; set; } = default!;
        public int StatusCode { get; set; }
        public T? Data { get; set; }
        public List<ErrorDetail> Errors { get; set; } = new();

        public static ApiResponse<T> SuccessResponse(string MessageCode, string Message, T data, int statusCode = 200)
        {
            return new ApiResponse<T>
            {
                Success = true,
                MessageCode = MessageCode,
                Message = Message,
                Data = data,
                StatusCode = statusCode
            };
        }

        public static ApiResponse<T> SuccessResponse(string MessageCode, string Message, int statusCode = 200)
        {
            return new ApiResponse<T>
            {
                Success = true,
                MessageCode = MessageCode,
                Message = Message,
                StatusCode = statusCode
            };
        }

        public static ApiResponse<T> ErrorResponse(string MessageCode, string Message, int statusCode = 400, List<ErrorDetail>? errors = null)
        {
            return new ApiResponse<T>
            {
                Success = false,
                MessageCode = MessageCode,
                Message = Message,
                StatusCode = statusCode,
                Errors = errors ?? new()
            };
        }


    }

    public class ErrorDetail
    {
        public string Field { get; set; } = string.Empty;
        public string ErrorCode { get; set; }= string.Empty;
        public string Message { get; set; }= string.Empty;
    }

}