IF NOT EXISTS (SELECT TOP 1 1 FROM CollaboratorLevels)
BEGIN
    PRINT 'Seeding CollaboratorLevels with UUID...';
    declare @defaultLevelId uniqueidentifier = 'F7D324D8-3949-44E2-9FA7-0DEA85A6B3D4';
    -- Insert Roles with explicit UUIDs
   INSERT INTO [dbo].[CollaboratorLevels]
           ([level_id]
           ,[level_name]
           ,[min_kpi_threshold]
           ,[commission_rate]
           ,[round1_bonus]
           ,[round2_bonus]
           ,[onboard_bonus]
           ,[description])
VALUES
           (@defaultLevelId, N'Sơ cấp',     0,   0.05, 100000, 150000, 200000, N'CTV mới bắt đầu, chưa có kinh nghiệm'),
           (NEWID(), N'Trung cấp',  50,  0.10, 200000, 250000, 300000, N'CTV đã có kinh nghiệm, đạt chỉ tiêu trung bình'),
           (NEWID(), N'<PERSON> cấp',    100, 0.15, 300000, 350000, 400000, N'CTV nhiều kinh nghiệm, đạt hiệu suất cao nhất')
  
PRINT 'CollaboratorLevels seeded successfully.';
END
ELSE
BEGIN
    PRINT 'CollaboratorLevels already exist. Skipping...';
END

