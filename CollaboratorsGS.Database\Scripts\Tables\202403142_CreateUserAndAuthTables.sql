-- Create Users and Authentication related tables
-- Create Roles table
IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'roles' AND schema_id = SCHEMA_ID('dbo'))
BEGIN
CREATE TABLE [dbo].[roles] (
    [role_id] UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
    [role_name] NVARCHAR(50) NOT NULL UNIQUE,
    [description] NVARCHAR(255)
);
END
GO

-- Create Permissions table
IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'permissions' AND schema_id = SCHEMA_ID('dbo'))
BEGIN
CREATE TABLE [dbo].[permissions] (
    [permission_id] UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
    [permission_name] NVARCHAR(100) NOT NULL UNIQUE,
    [description] NVARCHAR(255)
);
END
GO

-- Create Users table
IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'users' AND schema_id = SCHEMA_ID('dbo'))
BEGIN
CREATE TABLE [dbo].[users] (
    [user_id] UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
    [username] NVARCHAR(255) NOT NULL UNIQUE,
    [password] NVARCHAR(255) NOT NULL,
    [role_id] UNIQUEIDENTIFIER NOT NULL,
    [full_name] NVARCHAR(255),
    [email] NVARCHAR(255) UNIQUE,
    [phone_number] NVARCHAR(50) UNIQUE,
    [created_at] DATETIME NOT NULL DEFAULT GETDATE(),
    [last_login] DATETIME,
    [is_active] BIT DEFAULT 1,
    CONSTRAINT [FK_Users_Roles] FOREIGN KEY ([role_id]) REFERENCES [dbo].[Roles] ([role_id])
);
END
GO

-- Create RolePermissions table
IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'role_permissions' AND schema_id = SCHEMA_ID('dbo'))
BEGIN
CREATE TABLE [dbo].[role_permissions] (
    [role_id] UNIQUEIDENTIFIER NOT NULL,
    [permission_id] UNIQUEIDENTIFIER NOT NULL,
    PRIMARY KEY ([role_id], [permission_id]),
    CONSTRAINT [FK_RolePermissions_Roles] FOREIGN KEY ([role_id]) REFERENCES [dbo].[roles] ([role_id]),
    CONSTRAINT [FK_RolePermissions_Permissions] FOREIGN KEY ([permission_id]) REFERENCES [dbo].[permissions] ([permission_id])
);
END
GO

-- Create Authen table
IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'authen' AND schema_id = SCHEMA_ID('dbo'))
BEGIN
CREATE TABLE [dbo].[authen] (
    [token_id] UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
    [user_id] UNIQUEIDENTIFIER NOT NULL,
    [access_token] NVARCHAR(MAX) NOT NULL,
    [refresh_token] NVARCHAR(MAX),
    [issued_at] DATETIME NOT NULL DEFAULT GETDATE(),
    [expires_at] DATETIME NOT NULL,
    [status] NVARCHAR(50) NOT NULL,
    CONSTRAINT [FK_Authen_Users] FOREIGN KEY ([user_id]) REFERENCES [dbo].[users] ([user_id])
);
END
GO

-- Create PasswordResetTokens table
IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'password_reset_tokens' AND schema_id = SCHEMA_ID('dbo'))
BEGIN
CREATE TABLE [dbo].[password_reset_tokens] (
    [token_id] UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
    [user_id] UNIQUEIDENTIFIER NOT NULL,
    [token] NVARCHAR(255) NOT NULL UNIQUE,
    [expires_at] DATETIME NOT NULL,
    [created_at] DATETIME NOT NULL DEFAULT GETDATE(),
    CONSTRAINT [FK_PasswordResetTokens_Users] FOREIGN KEY ([user_id]) REFERENCES [dbo].[users] ([user_id])
);
END
GO


