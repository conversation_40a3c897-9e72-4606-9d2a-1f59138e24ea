using CollaboratorsGS.Domain.Entities;
using CollaboratorsGS.Domain.Repositories;
using CollaboratorsGS.Infrastructure.Data;
using Dapper;
using System.Data;

namespace CollaboratorsGS.Infrastructure.Repositories
{
    public class CollaboratorRewardRepository : ICollaboratorRewardRepository
    {
        private readonly IConnectionFactory _connectionFactory;

        public CollaboratorRewardRepository(IConnectionFactory connectionFactory)
        {
            _connectionFactory = connectionFactory;
        }

        public async Task<CollaboratorReward?> GetByIdAsync(Guid rewardId)
        {
            using var connection = _connectionFactory.CreateConnection();

            var parameters = new DynamicParameters();
            parameters.Add("@RewardId", rewardId, DbType.Guid);

            return await connection.QuerySingleOrDefaultAsync<CollaboratorReward>(
                "sp_GetCollaboratorRewardById",
                parameters,
                commandType: CommandType.StoredProcedure);
        }

        public async Task<IEnumerable<CollaboratorReward>> GetAllAsync()
        {
            using var connection = _connectionFactory.CreateConnection();

            return await connection.QueryAsync<CollaboratorReward>(
                "sp_GetAllCollaboratorRewards",
                commandType: CommandType.StoredProcedure);
        }

        public async Task<IEnumerable<CollaboratorReward>> GetByCollaboratorIdAsync(Guid collaboratorId)
        {
            using var connection = _connectionFactory.CreateConnection();

            var parameters = new DynamicParameters();
            parameters.Add("@CollaboratorId", collaboratorId, DbType.Guid);

            return await connection.QueryAsync<CollaboratorReward>(
                "sp_GetCollaboratorRewardsByCollaboratorId",
                parameters,
                commandType: CommandType.StoredProcedure);
        }

        public async Task<IEnumerable<CollaboratorReward>> GetByStatusAsync(string status)
        {
            using var connection = _connectionFactory.CreateConnection();

            var parameters = new DynamicParameters();
            parameters.Add("@Status", status, DbType.String);

            return await connection.QueryAsync<CollaboratorReward>(
                "sp_GetCollaboratorRewardsByStatus",
                parameters,
                commandType: CommandType.StoredProcedure);
        }

        public async Task<Guid> CreateAsync(CollaboratorReward reward)
        {
            if (reward.RewardId == Guid.Empty)
            {
                reward.RewardId = Guid.NewGuid();
            }

            using var connection = _connectionFactory.CreateConnection();

            var parameters = new DynamicParameters();
            parameters.Add("@RewardId", reward.RewardId, DbType.Guid);
            parameters.Add("@CollaboratorId", reward.CollaboratorId, DbType.Guid);
            parameters.Add("@ApplicationId", reward.ApplicationId, DbType.Guid);
            parameters.Add("@RewardType", reward.RewardType, DbType.String);
            parameters.Add("@Amount", reward.Amount, DbType.Decimal);
            parameters.Add("@LevelId", reward.LevelId, DbType.Guid);
            parameters.Add("@RewardDate", reward.RewardDate, DbType.DateTime);
            parameters.Add("@ScheduledPaymentDate", reward.ScheduledPaymentDate, DbType.DateTime);
            parameters.Add("@Status", reward.Status, DbType.String);

            await connection.ExecuteAsync(
                "sp_CreateCollaboratorReward",
                parameters,
                commandType: CommandType.StoredProcedure);

            return reward.RewardId;
        }

        public async Task<bool> UpdateAsync(CollaboratorReward reward)
        {
            using var connection = _connectionFactory.CreateConnection();

            var parameters = new DynamicParameters();
            parameters.Add("@RewardId", reward.RewardId, DbType.Guid);
            parameters.Add("@Amount", reward.Amount, DbType.Decimal);
            parameters.Add("@ScheduledPaymentDate", reward.ScheduledPaymentDate, DbType.DateTime);
            parameters.Add("@Status", reward.Status, DbType.String);

            var rowsAffected = await connection.ExecuteAsync(
                "sp_UpdateCollaboratorReward",
                parameters,
                commandType: CommandType.StoredProcedure);

            return rowsAffected > 0;
        }

        public async Task<bool> DeleteAsync(Guid rewardId)
        {
            using var connection = _connectionFactory.CreateConnection();

            var parameters = new DynamicParameters();
            parameters.Add("@RewardId", rewardId, DbType.Guid);

            var rowsAffected = await connection.ExecuteAsync(
                "sp_DeleteCollaboratorReward",
                parameters,
                commandType: CommandType.StoredProcedure);

            return rowsAffected > 0;
        }

        public async Task<IEnumerable<CollaboratorRewardHistory>> GetHistoryByRewardIdAsync(Guid rewardId)
        {
            using var connection = _connectionFactory.CreateConnection();

            var parameters = new DynamicParameters();
            parameters.Add("@RewardId", rewardId, DbType.Guid);

            return await connection.QueryAsync<CollaboratorRewardHistory>(
                "sp_GetCollaboratorRewardHistoryByRewardId",
                parameters,
                commandType: CommandType.StoredProcedure);
        }

        public async Task<Guid> CreateHistoryAsync(CollaboratorRewardHistory history)
        {
            if (history.HistoryId == Guid.Empty)
            {
                history.HistoryId = Guid.NewGuid();
            }

            using var connection = _connectionFactory.CreateConnection();

            var parameters = new DynamicParameters();
            parameters.Add("@HistoryId", history.HistoryId, DbType.Guid);
            parameters.Add("@CollaboratorId", history.CollaboratorId, DbType.Guid);
            parameters.Add("@RewardId", history.RewardId, DbType.Guid);
            parameters.Add("@Amount", history.Amount, DbType.Decimal);
            parameters.Add("@PaymentDate", history.PaymentDate, DbType.DateTime);
            parameters.Add("@PaymentMethod", history.PaymentMethod, DbType.String);
            parameters.Add("@Status", history.Status, DbType.String);

            await connection.ExecuteAsync(
                "sp_CreateCollaboratorRewardHistory",
                parameters,
                commandType: CommandType.StoredProcedure);

            return history.HistoryId;
        }

        public async Task<object> GetRewardOperationsAsync(Guid collaboratorId, int? month, int? year, string action)
        {
            using var connection = _connectionFactory.CreateConnection();

            var parameters = new DynamicParameters();
            parameters.Add("@CollaboratorId", collaboratorId, DbType.Guid);
            parameters.Add("@Month", month, DbType.Int32);
            parameters.Add("@Year", year, DbType.Int32);
            parameters.Add("@Action", action, DbType.String);

            var results = await connection.QueryAsync<dynamic>(
                "sp_CollaboratorRewardOperations",
                parameters,
                commandType: CommandType.StoredProcedure);

            return results;
        }
    }
}
