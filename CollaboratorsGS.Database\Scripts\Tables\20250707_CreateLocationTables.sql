-- DROP TABLE IF EXISTS wards;
IF OBJECT_ID('wards', 'U') IS NOT NULL DROP TABLE wards;
-- DROP TABLE IF EXISTS districts;
IF OBJECT_ID('districts', 'U') IS NOT NULL DROP TABLE districts;
-- DROP TABLE IF EXISTS provinces;
IF OBJECT_ID('provinces', 'U') IS NOT NULL DROP TABLE provinces;
-- DROP TABLE IF EXISTS administrative_units;
IF OBJECT_ID('administrative_units', 'U') IS NOT NULL DROP TABLE administrative_units;
-- DROP TABLE IF EXISTS administrative_regions;
IF OBJECT_ID('administrative_regions', 'U') IS NOT NULL DROP TABLE administrative_regions;

-- CREATE administrative_regions TABLE
CREATE TABLE administrative_regions (
    id UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
    name NVARCHAR(255) NOT NULL,
    name_en NVARCHAR(255) NOT NULL,
    code_name NVARCHAR(255) NULL,
    code_name_en NVARCHAR(255) NULL
);

-- CREATE administrative_units TABLE
CREATE TABLE administrative_units (
    id UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
    full_name NVARCHAR(255) NULL,
    full_name_en NVARCHAR(255) NULL,
    short_name NVARCHAR(255) NULL,
    short_name_en NVARCHAR(255) NULL,
    code_name NVARCHAR(255) NULL,
    code_name_en NVARCHAR(255) NULL
);

-- CREATE provinces TABLE
CREATE TABLE provinces (
    code VARCHAR(20) NOT NULL,
    name NVARCHAR(255) NOT NULL,
    name_en NVARCHAR(255) NULL, -- Fixed typo from 'Donation' to 'NVARCHAR(255)'
    full_name NVARCHAR(255) NOT NULL,
    full_name_en NVARCHAR(255) NULL,
    code_name NVARCHAR(255) NULL,
    administrative_unit_id UNIQUEIDENTIFIER NULL,
    administrative_region_id UNIQUEIDENTIFIER NULL,
    CONSTRAINT provinces_pkey PRIMARY KEY (code)
);

-- provinces foreign keys
ALTER TABLE provinces ADD CONSTRAINT provinces_administrative_region_id_fkey FOREIGN KEY (administrative_region_id) REFERENCES administrative_regions(id);
ALTER TABLE provinces ADD CONSTRAINT provinces_administrative_unit_id_fkey FOREIGN KEY (administrative_unit_id) REFERENCES administrative_units(id);

CREATE INDEX idx_provinces_region ON provinces(administrative_region_id);
CREATE INDEX idx_provinces_unit ON provinces(administrative_unit_id);

-- CREATE districts TABLE
CREATE TABLE districts (
    code VARCHAR(20) NOT NULL,
    name NVARCHAR(255) NOT NULL,
    name_en NVARCHAR(255) NULL,
    full_name NVARCHAR(255) NULL,
    full_name_en NVARCHAR(255) NULL,
    code_name NVARCHAR(255) NULL,
    province_code VARCHAR(20) NULL,
    administrative_unit_id UNIQUEIDENTIFIER NULL,
    CONSTRAINT districts_pkey PRIMARY KEY (code)
);

-- districts foreign keys
ALTER TABLE districts ADD CONSTRAINT districts_administrative_unit_id_fkey FOREIGN KEY (administrative_unit_id) REFERENCES administrative_units(id);
ALTER TABLE districts ADD CONSTRAINT districts_province_code_fkey FOREIGN KEY (province_code) REFERENCES provinces(code);

CREATE INDEX idx_districts_province ON districts(province_code);
CREATE INDEX idx_districts_unit ON districts(administrative_unit_id);

-- CREATE wards TABLE
CREATE TABLE wards (
    code VARCHAR(20) NOT NULL,
    name NVARCHAR(255) NOT NULL,
    name_en NVARCHAR(255) NULL,
    full_name NVARCHAR(255) NULL,
    full_name_en NVARCHAR(255) NULL,
    code_name NVARCHAR(255) NULL,
    district_code VARCHAR(20) NULL,
    administrative_unit_id UNIQUEIDENTIFIER NULL,
    CONSTRAINT wards_pkey PRIMARY KEY (code)
);

-- wards foreign keys
ALTER TABLE wards ADD CONSTRAINT wards_administrative_unit_id_fkey FOREIGN KEY (administrative_unit_id) REFERENCES administrative_units(id);
ALTER TABLE wards ADD CONSTRAINT wards_district_code_fkey FOREIGN KEY (district_code) REFERENCES districts(code);

ALTER TABLE provinces ADD name_v2 NVARCHAR(255) NULL;
ALTER TABLE provinces ADD full_name_v2 NVARCHAR(255) NULL;
ALTER TABLE provinces ADD code_name_v2 NVARCHAR(255) NULL;
ALTER TABLE provinces ADD is_deleted BIT DEFAULT 0;
ALTER TABLE provinces ADD merged_into_code VARCHAR(20) NULL;
ALTER TABLE provinces ADD note NVARCHAR(512) NULL;

ALTER TABLE districts ADD name_v2 NVARCHAR(255) NULL;
ALTER TABLE districts ADD full_name_v2 NVARCHAR(255) NULL;
ALTER TABLE districts ADD code_name_v2 NVARCHAR(255) NULL;
ALTER TABLE districts ADD is_deleted BIT DEFAULT 0;
ALTER TABLE districts ADD merged_into_code VARCHAR(20) NULL;
ALTER TABLE districts ADD note NVARCHAR(512) NULL;


ALTER TABLE wards ADD name_v2 NVARCHAR(255) NULL;
ALTER TABLE wards ADD full_name_v2 NVARCHAR(255) NULL;
ALTER TABLE wards ADD code_name_v2 NVARCHAR(255) NULL;
ALTER TABLE wards ADD is_deleted BIT DEFAULT 0;
ALTER TABLE wards ADD merged_into_code VARCHAR(20) NULL;
ALTER TABLE wards ADD note NVARCHAR(512) NULL;

CREATE INDEX idx_wards_district ON wards(district_code);
CREATE INDEX idx_wards_unit ON wards(administrative_unit_id);

