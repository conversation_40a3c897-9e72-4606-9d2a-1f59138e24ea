using CollaboratorsGS.Application.Interfaces;
using CollaboratorsGS.Infrastructure.Configurations;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Options;

namespace CollaboratorsGS.Infrastructure.Services
{
    public class LocalFileStorageService : IFileStorageService
    {
        private readonly FileStorageOptions _options;
        private readonly IWebHostEnvironment _environment;

        public LocalFileStorageService(
            IOptions<FileStorageOptions> options,
            IWebHostEnvironment environment)
        {
            _options = options.Value;
            _environment = environment;
        }

        public async Task<string> UploadFileAsync(IFormFile file, string? fileName = null)
        {
            if (file == null || file.Length == 0)
                throw new ArgumentException("File is empty or null", nameof(file));

            var uploadPath = Path.Combine(_environment.WebRootPath, _options.LocalBasePath);
            if (!Directory.Exists(uploadPath))
                Directory.CreateDirectory(uploadPath);

            if (string.IsNullOrEmpty(fileName))
            {
                var extension = Path.GetExtension(file.FileName);
                fileName = $"{Guid.NewGuid()}{extension}";
            }
            var filePath = Path.Combine(uploadPath, fileName);

            using (var stream = new FileStream(filePath, FileMode.Create))
            {
                await file.CopyToAsync(stream);
            }

            return fileName;
        }

        public Task<bool> DeleteFileAsync(string fileName)
        {
            try
            {
                var filePath = Path.Combine(_environment.WebRootPath, _options.LocalBasePath, fileName);

                if (File.Exists(filePath))
                {
                    File.Delete(filePath);
                    return Task.FromResult(true);
                }

                return Task.FromResult(false);
            }
            catch
            {
                return Task.FromResult(false);
            }
        }

        public Task<string> GetTemporaryUrlAsync(string fileName, int expiryMinutes = 60)
        {
            return Task.FromResult($"{_options.LocalBaseUrl}/{fileName}");
        }
    }
}
