using AutoMapper;
using CollaboratorsGS.Application.DTOs;
using CollaboratorsGS.Application.DTOs.Branch;
using CollaboratorsGS.Application.DTOs.Candidate;
using CollaboratorsGS.Application.DTOs.CandidateApplication;
using CollaboratorsGS.Application.DTOs.Collaborator;
using CollaboratorsGS.Application.DTOs.CollaboratorProfile;
using CollaboratorsGS.Application.DTOs.CollaboratorViolation;
using CollaboratorsGS.Application.DTOs.Company;
using CollaboratorsGS.Application.DTOs.Contract;
using CollaboratorsGS.Application.DTOs.CollaboratorKpi;
using CollaboratorsGS.Application.DTOs.CollaboratorKpiTarget;
using CollaboratorsGS.Application.DTOs.CollaboratorLevel;
using CollaboratorsGS.Application.DTOs.CollaboratorReport;
using CollaboratorsGS.Application.DTOs.CollaboratorReward;
using CollaboratorsGS.Application.DTOs.Department;
using CollaboratorsGS.Application.DTOs.Position;
using CollaboratorsGS.Application.DTOs.RecruitmentPosting;
using CollaboratorsGS.Application.DTOs.ObjectData;
using CollaboratorsGS.Domain.Entities;
using CollaboratorsGS.Application.DTOs.CandidateDocument;
using CollaboratorProfileDto = CollaboratorsGS.Application.DTOs.Collaborator.CollaboratorProfileDto;
using CollaboratorsGS.Application.DTOs.Common;
using CollaboratorsGS.Application.DTOs.AddressDto;

namespace CollaboratorsGS.Application.Mappings
{
    public class MappingProfile : Profile
    {
        public MappingProfile()
        {
            // Address mappings
            CreateMap<Province, ProvinceDto>();
            CreateMap<District, DistrictDto>();
            CreateMap<Ward, WardDto>();

            CreateMap<User, UserDto>()
                .ForMember(dest => dest.role, opt => opt.MapFrom(src => src.Role != null ? src.Role.RoleName : string.Empty))
                .ForMember(dest => dest.user_id, opt => opt.MapFrom(src => src.UserId))
                .ForMember(dest => dest.user_name, opt => opt.MapFrom(src => src.Username))
                .ForMember(dest => dest.full_name, opt => opt.MapFrom(src => src.FullName))
                .ForMember(dest => dest.email, opt => opt.MapFrom(src => src.Email))
                .ForMember(dest => dest.phone_number, opt => opt.MapFrom(src => src.PhoneNumber))
                .ForMember(dest => dest.created_at, opt => opt.MapFrom(src => src.CreatedAt))
                .ForMember(dest => dest.last_login, opt => opt.MapFrom(src => src.LastLogin))
                .ForMember(dest => dest.is_active, opt => opt.MapFrom(src => src.IsActive))
                .ForMember(dest => dest.group, opt => opt.MapFrom(src => src.Group))
                .ForMember(dest => dest.employee_id, opt => opt.MapFrom(src => src.EmployeeId));
            // Role mappings
            CreateMap<Role, RoleDto>();
            CreateMap<Role, RoleWithPermissionsDto>();

            // Permission mappings
            CreateMap<Permission, PermissionDto>();

            // Candidate mappings
            CreateMap<Candidate, CandidateDto>()
                .ForMember(dest => dest.DateOfBirth, opt => opt.MapFrom(src => DateHelper.FormatDateString(src.DateOfBirth)))
                .ForMember(dest => dest.CitizenIdIssueDate, opt => opt.MapFrom(src => DateHelper.FormatDateString(src.CitizenIdIssueDate)));
            CreateMap<CreateCandidateRequest, Candidate>()
                .ForMember(dest => dest.Documents, opt => opt.Ignore());
            CreateMap<UpdateCandidateRequest, Candidate>();
            CreateMap<dynamic, CandidateDetailDto>();

            // Collaborator mappings
            CreateMap<Collaborator, CollaboratorDto>()
                .ForMember(dest => dest.LevelName, opt => opt.Ignore())
                .ForMember(dest => dest.ApproverName, opt => opt.Ignore())
                .ForMember(dest => dest.CitizenId, opt => opt.Ignore())
                .ForMember(dest => dest.PermanentAddress, opt => opt.Ignore())
                .ForMember(dest => dest.CurrentAddress, opt => opt.Ignore())
                .ForMember(dest => dest.BankName, opt => opt.Ignore())
                .ForMember(dest => dest.BankAccountNumber, opt => opt.Ignore())
                .ForMember(dest => dest.TotalCandidates, opt => opt.Ignore())
                .ForMember(dest => dest.IssueAuthority, opt => opt.Ignore())
                .ForMember(dest => dest.IssueDate, opt => opt.Ignore())
                .ForMember(dest => dest.BankAccountName, opt => opt.Ignore())
                .ForMember(dest => dest.TotalOnboarded, opt => opt.Ignore())
                .ForMember(dest => dest.TotalEarnings, opt => opt.Ignore())
                .ForMember(dest => dest.PhotoUrl, opt => opt.Ignore());

            CreateMap<CreateCollaboratorRequest, Collaborator>();
            CreateMap<UpdateCollaboratorRequest, Collaborator>();
            CreateMap<CollaboratorsGS.Application.DTOs.Collaborator.UpdateCollaboratorProfileRequest, CollaboratorProfile>();

            // CollaboratorProfile mappings
            CreateMap<CollaboratorProfile, CollaboratorDto>();
            CreateMap<CollaboratorsGS.Application.DTOs.CollaboratorProfile.UpdateCollaboratorProfileRequest, CollaboratorProfile>();
            CreateMap<CollaboratorProfile, CollaboratorProfileDto>();
            // Contract mappings
            CreateMap<Contract, ContractDto>();
            CreateMap<CreateContractRequest, Contract>();
            CreateMap<UpdateContractRequest, Contract>();

            // CollaboratorViolation mappings
            CreateMap<CollaboratorViolation, CollaboratorViolationDto>();
            CreateMap<CreateCollaboratorViolationRequest, CollaboratorViolation>();
            CreateMap<UpdateCollaboratorViolationRequest, CollaboratorViolation>();

            // CollaboratorKpi mappings
            CreateMap<CollaboratorKpi, CollaboratorKpiDto>();
            CreateMap<CreateCollaboratorKpiRequest, CollaboratorKpi>();
            CreateMap<UpdateCollaboratorKpiRequest, CollaboratorKpi>();

            // CollaboratorKpiTarget mappings
            CreateMap<CollaboratorKpiTarget, CollaboratorKpiTargetDto>();
            CreateMap<CreateCollaboratorKpiTargetRequest, CollaboratorKpiTarget>();
            CreateMap<UpdateCollaboratorKpiTargetRequest, CollaboratorKpiTarget>();

            // CollaboratorReport mappings
            CreateMap<CollaboratorReport, CollaboratorReportDto>();
            CreateMap<CreateCollaboratorReportRequest, CollaboratorReport>();
            CreateMap<UpdateCollaboratorReportRequest, CollaboratorReport>();

            // CollaboratorLevel mappings
            CreateMap<CollaboratorLevel, CollaboratorLevelDto>();
            CreateMap<CreateCollaboratorLevelRequest, CollaboratorLevel>();
            CreateMap<UpdateCollaboratorLevelRequest, CollaboratorLevel>();

            // CollaboratorReward mappings
            CreateMap<CollaboratorReward, CollaboratorRewardDto>()
                .ForMember(dest => dest.CollaboratorName, opt => opt.Ignore())
                .ForMember(dest => dest.LevelName, opt => opt.Ignore());
            CreateMap<CreateCollaboratorRewardRequest, CollaboratorReward>();
            CreateMap<UpdateCollaboratorRewardRequest, CollaboratorReward>();

            // CollaboratorRewardHistory mappings
            CreateMap<CollaboratorRewardHistory, CollaboratorRewardHistoryDto>()
                .ForMember(dest => dest.CollaboratorName, opt => opt.Ignore());
            CreateMap<CreateCollaboratorRewardHistoryRequest, CollaboratorRewardHistory>();

            // RecruitmentPosting mappings
            CreateMap<RecruitmentPosting, RecruitmentPostingDto>()
                .ForMember(dest => dest.posting_id, opt => opt.MapFrom(src => src.PostingId))
                .ForMember(dest => dest.refer_code, opt => opt.MapFrom(src => src.ReferCode))
                .ForMember(dest => dest.title, opt => opt.MapFrom(src => src.Title))
                .ForMember(dest => dest.project, opt => opt.MapFrom(src => src.Project))
                .ForMember(dest => dest.level, opt => opt.MapFrom(src => src.Level))
                .ForMember(dest => dest.position, opt => opt.MapFrom(src => src.Position))
                .ForMember(dest => dest.salary_from, opt => opt.MapFrom(src => src.SalaryFrom))
                .ForMember(dest => dest.salary_to, opt => opt.MapFrom(src => src.SalaryTo))
                .ForMember(dest => dest.commission, opt => opt.MapFrom(src => src.Commission))
                .ForMember(dest => dest.commission_warranty_months, opt => opt.MapFrom(src => src.CommissionWarrantyMonths))
                .ForMember(dest => dest.working_location, opt => opt.MapFrom(src => src.WorkingLocation))
                .ForMember(dest => dest.working_time, opt => opt.MapFrom(src => src.WorkingTime))
                .ForMember(dest => dest.view_count, opt => opt.MapFrom(src => src.ViewCount))
                .ForMember(dest => dest.referral_count, opt => opt.MapFrom(src => src.ReferralCount))
                .ForMember(dest => dest.is_urgent, opt => opt.MapFrom(src => src.IsUrgent))
                .ForMember(dest => dest.is_hot, opt => opt.MapFrom(src => src.IsHot))
                .ForMember(dest => dest.is_saved, opt => opt.MapFrom(src => src.IsSaved))
                .ForMember(dest => dest.status, opt => opt.MapFrom(src => src.Status))
                .ForMember(dest => dest.created_at, opt => opt.MapFrom(src => src.CreatedAt))
                .ForMember(dest => dest.updated_at, opt => opt.MapFrom(src => src.UpdatedAt))
                .ForMember(dest => dest.expired_at, opt => opt.MapFrom(src => src.ExpiredAt));

            CreateMap<RecruitmentPosting, RecruitmentPostingDetailDto>()
                .ForMember(dest => dest.posting_id, opt => opt.MapFrom(src => src.PostingId))
                .ForMember(dest => dest.refer_code, opt => opt.MapFrom(src => src.ReferCode))
                .ForMember(dest => dest.title, opt => opt.MapFrom(src => src.Title))
                .ForMember(dest => dest.project, opt => opt.MapFrom(src => src.Project))
                .ForMember(dest => dest.level, opt => opt.MapFrom(src => src.Level))
                .ForMember(dest => dest.position, opt => opt.MapFrom(src => src.Position))
                .ForMember(dest => dest.salary_from, opt => opt.MapFrom(src => src.SalaryFrom))
                .ForMember(dest => dest.salary_to, opt => opt.MapFrom(src => src.SalaryTo))
                .ForMember(dest => dest.commission, opt => opt.MapFrom(src => src.Commission))
                .ForMember(dest => dest.commission_warranty_months, opt => opt.MapFrom(src => src.CommissionWarrantyMonths))
                .ForMember(dest => dest.working_location, opt => opt.MapFrom(src => src.WorkingLocation))
                .ForMember(dest => dest.working_time, opt => opt.MapFrom(src => src.WorkingTime))
                .ForMember(dest => dest.view_count, opt => opt.MapFrom(src => src.ViewCount))
                .ForMember(dest => dest.referral_count, opt => opt.MapFrom(src => src.ReferralCount))
                .ForMember(dest => dest.is_urgent, opt => opt.MapFrom(src => src.IsUrgent))
                .ForMember(dest => dest.is_hot, opt => opt.MapFrom(src => src.IsHot))
                .ForMember(dest => dest.is_saved, opt => opt.MapFrom(src => src.IsSaved))
                .ForMember(dest => dest.status, opt => opt.MapFrom(src => src.Status))
                .ForMember(dest => dest.job_detail_json, opt => opt.MapFrom(src => src.JobDetailJson))
                .ForMember(dest => dest.created_at, opt => opt.MapFrom(src => src.CreatedAt))
                .ForMember(dest => dest.updated_at, opt => opt.MapFrom(src => src.UpdatedAt))
                .ForMember(dest => dest.expired_at, opt => opt.MapFrom(src => src.ExpiredAt));

            CreateMap<CreateRecruitmentPostingRequest, RecruitmentPosting>()
                .ForMember(dest => dest.ReferCode, opt => opt.MapFrom(src => src.refer_code))
                .ForMember(dest => dest.Title, opt => opt.MapFrom(src => src.title))
                .ForMember(dest => dest.Project, opt => opt.MapFrom(src => src.project))
                .ForMember(dest => dest.Level, opt => opt.MapFrom(src => src.level))
                .ForMember(dest => dest.Position, opt => opt.MapFrom(src => src.position))
                .ForMember(dest => dest.SalaryFrom, opt => opt.MapFrom(src => src.salary_from))
                .ForMember(dest => dest.SalaryTo, opt => opt.MapFrom(src => src.salary_to))
                .ForMember(dest => dest.Commission, opt => opt.MapFrom(src => src.commission))
                .ForMember(dest => dest.CommissionWarrantyMonths, opt => opt.MapFrom(src => src.commission_warranty_months))
                .ForMember(dest => dest.WorkingLocation, opt => opt.MapFrom(src => src.working_location))
                .ForMember(dest => dest.WorkingTime, opt => opt.MapFrom(src => src.working_time))
                .ForMember(dest => dest.IsUrgent, opt => opt.MapFrom(src => src.is_urgent))
                .ForMember(dest => dest.IsHot, opt => opt.MapFrom(src => src.is_hot))
                .ForMember(dest => dest.Status, opt => opt.MapFrom(src => src.status))
                .ForMember(dest => dest.JobDetailJson, opt => opt.MapFrom(src => src.job_detail_json))
                .ForMember(dest => dest.ExpiredAt, opt => opt.MapFrom(src => src.expired_at))
                .ForMember(dest => dest.PostingId, opt => opt.Ignore())
                .ForMember(dest => dest.ViewCount, opt => opt.Ignore())
                .ForMember(dest => dest.ReferralCount, opt => opt.Ignore())
                .ForMember(dest => dest.IsSaved, opt => opt.Ignore())
                .ForMember(dest => dest.CreatedAt, opt => opt.Ignore())
                .ForMember(dest => dest.UpdatedAt, opt => opt.Ignore());

            CreateMap<UpdateRecruitmentPostingRequest, RecruitmentPosting>()
                .ForMember(dest => dest.ReferCode, opt => opt.MapFrom(src => src.refer_code))
                .ForMember(dest => dest.Title, opt => opt.MapFrom(src => src.title))
                .ForMember(dest => dest.Project, opt => opt.MapFrom(src => src.project))
                .ForMember(dest => dest.Level, opt => opt.MapFrom(src => src.level))
                .ForMember(dest => dest.Position, opt => opt.MapFrom(src => src.position))
                .ForMember(dest => dest.SalaryFrom, opt => opt.MapFrom(src => src.salary_from))
                .ForMember(dest => dest.SalaryTo, opt => opt.MapFrom(src => src.salary_to))
                .ForMember(dest => dest.Commission, opt => opt.MapFrom(src => src.commission))
                .ForMember(dest => dest.CommissionWarrantyMonths, opt => opt.MapFrom(src => src.commission_warranty_months))
                .ForMember(dest => dest.WorkingLocation, opt => opt.MapFrom(src => src.working_location))
                .ForMember(dest => dest.WorkingTime, opt => opt.MapFrom(src => src.working_time))
                .ForMember(dest => dest.IsUrgent, opt => opt.MapFrom(src => src.is_urgent))
                .ForMember(dest => dest.IsHot, opt => opt.MapFrom(src => src.is_hot))
                .ForMember(dest => dest.Status, opt => opt.MapFrom(src => src.status))
                .ForMember(dest => dest.JobDetailJson, opt => opt.MapFrom(src => src.job_detail_json))
                .ForMember(dest => dest.ExpiredAt, opt => opt.MapFrom(src => src.expired_at))
                .ForMember(dest => dest.PostingId, opt => opt.Ignore())
                .ForMember(dest => dest.ViewCount, opt => opt.Ignore())
                .ForMember(dest => dest.ReferralCount, opt => opt.Ignore())
                .ForMember(dest => dest.IsSaved, opt => opt.Ignore())
                .ForMember(dest => dest.CreatedAt, opt => opt.Ignore())
                .ForMember(dest => dest.UpdatedAt, opt => opt.Ignore())
                .ForAllMembers(opt => opt.Condition((src, dest, srcMember) => srcMember != null));

            // ObjectData mappings
            CreateMap<ObjectData, ObjectDataDto>();
            CreateMap<CreateObjectDataRequest, ObjectData>();
            CreateMap<UpdateObjectDataRequest, ObjectData>();

            // Department mappings
            CreateMap<Department, DepartmentDto>();
            CreateMap<CreateDepartmentRequest, Department>();
            CreateMap<UpdateDepartmentRequest, Department>();

            // Position mappings
            CreateMap<Position, PositionDto>();
            CreateMap<CreatePositionRequest, Position>();
            CreateMap<UpdatePositionRequest, Position>();

            // CandidateApplication mappings
            CreateMap<CandidateApplication, CandidateApplicationDto>();
            CreateMap<CreateCandidateApplicationRequest, CandidateApplication>();
            CreateMap<UpdateCandidateApplicationRequest, CandidateApplication>();

            // Company mappings
            CreateMap<Company, CompanyDto>();
            CreateMap<CreateCompanyRequest, Company>();
            CreateMap<UpdateCompanyRequest, Company>();

            // Branch mappings
            CreateMap<Branch, BranchDto>();
            CreateMap<CreateBranchRequest, Branch>();
            CreateMap<UpdateBranchRequest, Branch>();
            // CandidateDocument mappings
            CreateMap<CandidateDocument, CandidateDocumentDto>();
            CreateMap<CreateCandidateDocumentRequest, CandidateDocument>();
        }
    }
}
