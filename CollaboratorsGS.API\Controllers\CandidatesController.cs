using CollaboratorsGS.Application.DTOs.Candidate;
using CollaboratorsGS.Application.Interfaces;
using CollaboratorsGS.Application.Constants;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using CollaboratorsGS.Infrastructure.Services;
using CollaboratorsGS.Application.DTOs;
using CollaboratorsGS.Infrastructure.Utilities;
using Google.Protobuf.WellKnownTypes;
using Minio;
using Minio.DataModel.Args;
using CollaboratorsGS.Infrastructure.Utils;
using CollaboratorsGS.Infrastructure.Configurations;
using Microsoft.Extensions.Options;

namespace CollaboratorsGS.API.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    [Authorize]
    public class CandidatesController : ControllerBase
    {
        private readonly ICandidateService _candidateService;
        private readonly ILogger<CandidatesController> _logger;
        private readonly ICollaboratorService _collaboratorService;
        private readonly CvScannerService _cvScannerService;
        private readonly IMinioClient _minioClient;
        private readonly FileStorageOptions _settings;
        public CandidatesController(ICandidateService candidateService,
        ILogger<CandidatesController> logger,
        ICollaboratorService collaboratorService,
        CvScannerService cvScannerService,
        IMinioClient minioClient,
        IOptions<FileStorageOptions> options)
        {
            _candidateService = candidateService;
            _logger = logger;
            _collaboratorService = collaboratorService;
            _cvScannerService = cvScannerService;
            _minioClient = minioClient;
            _settings = options.Value;
        }
        // Roles = "Admin,Recruiter,Manager,Collaborator"
        // GET: api/Candidates
        [HttpGet]
        [Authorize(Roles = RolesUser.AdminManagerRecruiterCollaborator)]
        public async Task<IActionResult> GetAll()
        {
            try
            {
                var candidates = await _candidateService.GetAllAsync();
                return Ok(ApiResponse<object>.SuccessResponse(
                    MessageCodes.SC2000,
                    "Get all candidates successfully",
                    candidates));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting all candidates");
                return StatusCode(500, ApiResponse<object>.ErrorResponse(
                    MessageCodes.ER5000,
                    "Internal server error",
                    500));
            }
        }

        // GET: api/Candidates/5
        [HttpGet("{id}")]
        [Authorize(Roles = RolesUser.AdminManagerRecruiterCollaborator)]
        public async Task<IActionResult> GetById(Guid id)
        {
            try
            {
                var candidate = await _candidateService.GetByIdAsync(id);
                if (candidate == null)
                {
                    return NotFound(ApiResponse<object>.ErrorResponse(
                        MessageCodes.ER4004,
                        "Candidate not found",
                        404));
                }
                return Ok(ApiResponse<object>.SuccessResponse(
                    MessageCodes.SC2000,
                    "Get candidate successfully",
                    candidate));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting candidate by ID: {CandidateId}", id);
                return StatusCode(500, ApiResponse<object>.ErrorResponse(
                    MessageCodes.ER5000,
                    "Internal server error",
                    500));
            }
        }

        // GET: api/Candidates/collaborator/5
        [HttpGet("collaborator/{collaboratorId}")]
        [Authorize(Roles = RolesUser.AdminManagerRecruiterCollaborator)]
        public async Task<IActionResult> GetByCollaboratorId(Guid collaboratorId)
        {
            try
            {
                var candidates = await _candidateService.GetByCollaboratorIdAsync(collaboratorId);
                return Ok(ApiResponse<object>.SuccessResponse(
                    MessageCodes.SC2000,
                    "Get candidates by collaborator successfully",
                    candidates));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting candidates by collaborator ID: {collaboratorId}", collaboratorId);
                return StatusCode(500, ApiResponse<object>.ErrorResponse(
                    MessageCodes.ER5000,
                    ex.Message,
                    500));
            }
        }

        // POST: api/Candidates
        [HttpPost]
        [Authorize(Roles = RolesUser.AdminManagerRecruiterCollaborator)]
        public async Task<IActionResult> Create([FromBody] CreateCandidateRequest request)
        {
            try
            {
                var userIdClaim = User.FindFirst(System.Security.Claims.ClaimTypes.NameIdentifier)
                ?? User.FindFirst("sub");

                if (userIdClaim == null || !Guid.TryParse(userIdClaim.Value, out var userId))
                {
                    return BadRequest(ApiResponse<object>.ErrorResponse(
                        MessageCodes.ER4001,
                        "User ID not found in token or invalid",
                        400));
                }

                var collaboratorId = await _collaboratorService.GetByUserId(userId);
                if (collaboratorId != null)
                    request.CollaboratorId = collaboratorId;

                var candidateId = await _candidateService.CreateCandidateAsync(request);

                var createdCandidate = await _candidateService.GetCreatedCandidateAsync(candidateId);

                if (createdCandidate == null)
                    return StatusCode(500, ApiResponse<object>.ErrorResponse(
                        MessageCodes.ER5000,
                        "Failed to retrieve created candidate",
                        500));

                return CreatedAtAction(nameof(GetById), new { id = candidateId },
                   ApiResponse<object>.SuccessResponse(
                       MessageCodes.SC2001,
                       "Candidate created successfully",
                       createdCandidate,
                       201));
            }
            catch (InvalidOperationException ex)
            {
                _logger.LogWarning(ex, "Validation error creating candidate");
                return BadRequest(ApiResponse<object>.ErrorResponse(
                    MessageCodes.ER4005,
                    ex.Message,
                    400,
                    new List<ErrorDetail>
                    {
                        new ErrorDetail
                        {
                            Field = "request",
                            ErrorCode = MessageCodes.ER4005,
                            Message = ex.Message
                        }
                    }));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating candidate");
                return StatusCode(500, ApiResponse<object>.ErrorResponse(
                    MessageCodes.ER5000,
                    ex.Message,
                    500));
            }
        }

        [HttpPost("/CandidateFromCV")]
        [Authorize(Roles = RolesUser.AdminManagerRecruiterCollaborator)]
        public async Task<IActionResult> CreateCandidateFromCV([FromForm] CreateCandidateFromCVRequest request, IFormFile file)  
        {
            try
            {
                var userIdClaim = User.FindFirst(System.Security.Claims.ClaimTypes.NameIdentifier)
                ?? User.FindFirst("sub");

                if (userIdClaim == null || !Guid.TryParse(userIdClaim.Value, out var userId))
                {
                    return BadRequest(ApiResponse<object>.ErrorResponse(
                        MessageCodes.ER4001,
                        "User ID not found in token or invalid",
                        400));
                }
                if (file == null || file.Length == 0)
                {
                    return BadRequest(ApiResponse<object>.ErrorResponse(
                        MessageCodes.ER4001,
                        "No file was uploaded",
                        400,
                        null
                    )
                   );
                }

                // string _bucketName = "gstalent";
                var objectName = $"{Guid.NewGuid()}{Path.GetExtension(file.FileName)}";
                using var memoryStream = new MemoryStream();
                await file.CopyToAsync(memoryStream);
                memoryStream.Seek(0, SeekOrigin.Begin);
                var fileBytes = memoryStream.ToArray();

                bool found = await _minioClient.BucketExistsAsync(
                 new BucketExistsArgs().WithBucket(_settings.MinioBucketName));
                if (!found)
                {
                    await _minioClient.MakeBucketAsync(
                        new MakeBucketArgs().WithBucket(_settings.MinioBucketName));
                }
                await _minioClient.PutObjectAsync(new PutObjectArgs()
                            .WithBucket(_settings.MinioBucketName)
                            .WithObject(objectName)
                            .WithStreamData(memoryStream)
                            .WithObjectSize(memoryStream.Length)
                            .WithContentType(file.ContentType)
                        );
                string fileName = FileUtils.GetFileName(objectName);
                string fileExtension = FileUtils.GetFileExtension(file);
                var scanResult = await _cvScannerService.ScanCvAsync(fileBytes, file.FileName);
                if (!scanResult.IsSuccess || scanResult.CandidateInfo == null)
                {
                    return BadRequest(ApiResponse<object>.ErrorResponse(
                        MessageCodes.ER4001,
                        "Scan CV failed",
                        400,
                        null
                    )
                   );
                }
                var collaboratorId = await _collaboratorService.GetByUserId(userId);
                if (collaboratorId == null)
                    return BadRequest(ApiResponse<object>.ErrorResponse(
                        MessageCodes.ER4001,
                        "Collaborator not found",
                        400,
                        null
                    )
                   );

                var candidateId = await _candidateService.CreateCandidateFromCVAsync(request, collaboratorId.Value, scanResult.CandidateInfo, fileName, fileExtension);

                var createdCandidate = await _candidateService.GetCreatedCandidateAsync(candidateId);



                if (createdCandidate == null)
                    return StatusCode(500, ApiResponse<object>.ErrorResponse(
                        MessageCodes.ER5000,
                        "Failed to retrieve created candidate",
                        500));

                return CreatedAtAction(nameof(GetById), new
                {
                    id = candidateId
                },
                   ApiResponse<object>.SuccessResponse(
                       MessageCodes.SC2001,
                       "Candidate created successfully",
                       createdCandidate,
                       201));
            }
            catch (InvalidOperationException ex)
            {
                _logger.LogWarning(ex, "Validation error creating candidate");
                return BadRequest(ApiResponse<object>.ErrorResponse(
                    MessageCodes.ER4005,
                    ex.Message,
                    400,
                    new List<ErrorDetail>
                    {
                        new ErrorDetail
                        {
                            Field = "request",
                            ErrorCode = MessageCodes.ER4005,
                            Message = ex.Message
                        }
                    }));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating candidate");
                return StatusCode(500, ApiResponse<object>.ErrorResponse(
                    MessageCodes.ER5000,
                    ex.Message,
                    500));
            }
        }

        // PUT: api/Candidates/5
        [HttpPut("{id}")]
        [Authorize(Roles = RolesUser.AdminManagerRecruiterCollaborator)]
        public async Task<IActionResult> Update(Guid id, [FromBody] UpdateCandidateRequest request)
        {
            try
            {
                var updatedCandidate = await _candidateService.UpdateCandidateAsync(id, request);
                if (updatedCandidate == null)
                {
                    return NotFound(ApiResponse<object>.ErrorResponse(
                        MessageCodes.ER4004,
                        "Candidate not found",
                        404));
                }
                return Ok(ApiResponse<object>.SuccessResponse(
                    MessageCodes.SC2002,
                    "Candidate updated successfully",
                    updatedCandidate));
            }
            catch (InvalidOperationException ex)
            {
                _logger.LogWarning(ex, "Validation error updating candidate: {CandidateId}", id);
                return BadRequest(ApiResponse<object>.ErrorResponse(
                    MessageCodes.ER4005,
                    ex.Message,
                    400,
                    new List<ErrorDetail>
                    {
                        new ErrorDetail
                        {
                            Field = "request",
                            ErrorCode = MessageCodes.ER4005,
                            Message = ex.Message
                        }
                    }));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating candidate: {CandidateId}", id);
                return StatusCode(500, ApiResponse<object>.ErrorResponse(
                    MessageCodes.ER5000,
                    ex.Message,
                    500));
            }
        }

        // DELETE: api/Candidates/5
        [HttpDelete("{id}")]
        [Authorize(Roles = RolesUser.AdminManagerRecruiterCollaborator)]
        public async Task<IActionResult> Delete(Guid id)
        {
            try
            {
                var result = await _candidateService.DeleteCandidateAsync(id);
                if (!result)
                {
                    return NotFound(ApiResponse<object>.ErrorResponse(
                        MessageCodes.ER4004,
                        "Candidate not found",
                        404));
                }
                return Ok(ApiResponse<object>.SuccessResponse(
                    MessageCodes.SC2003,
                    "Candidate deleted successfully",
                    true));
            }
            catch (InvalidOperationException ex)
            {
                _logger.LogWarning(ex, "Validation error deleting candidate: {CandidateId}", id);
                return BadRequest(ApiResponse<object>.ErrorResponse(
                MessageCodes.ER4005,
                ex.Message,
                400,
                new List<ErrorDetail>
                {
                        new ErrorDetail
                        {
                            Field = "id",
                            ErrorCode = MessageCodes.ER4005,
                            Message = ex.Message
                        }
                }));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting candidate: {CandidateId}", id);
                return StatusCode(500, ApiResponse<object>.ErrorResponse(
                    MessageCodes.ER5000,
                    ex.Message,
                    500));
            }
        }
    }
}
