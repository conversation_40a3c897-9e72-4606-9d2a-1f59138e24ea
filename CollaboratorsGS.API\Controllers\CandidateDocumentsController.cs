using CollaboratorsGS.Application.DTOs;
using CollaboratorsGS.Application.DTOs.CandidateDocument;
using CollaboratorsGS.Application.Interfaces;
using CollaboratorsGS.Infrastructure.Utilities;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Threading.Tasks;

namespace CollaboratorsGS.API.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    [Authorize]
    public class CandidateDocumentsController : ControllerBase
    {
        private readonly ICandidateDocumentService _documentService;

        public CandidateDocumentsController(
            ICandidateDocumentService documentService)
        {
            _documentService = documentService;
        }

        [HttpGet("{id}")]
        public async Task<IActionResult> GetById(Guid id)
        {
            try
            {
                var document = await _documentService.GetByIdAsync(id);
                if (document == null)
                    return NotFound(ApiResponse<object>.ErrorResponse(
                        MessageCodes.ER4004,
                        "Document not found",
                        404));

                return Ok(ApiResponse<object>.SuccessResponse(
                    MessageCodes.SC2000,
                    "Get document successfully",
                    document));
            }
            catch (Exception ex)
            {
                return StatusCode(500, ApiResponse<object>.ErrorResponse(
                    MessageCodes.ER5000,
                    ex.Message,
                    500));
            }
        }

        [HttpGet("candidate/{candidateId}")]
        public async Task<IActionResult> GetByCandidateId(Guid candidateId)
        {
            try
            {
                var documents = await _documentService.GetByCandidateIdAsync(candidateId);
                return Ok(ApiResponse<object>.SuccessResponse(
                    MessageCodes.SC2000,
                    "Get documents by candidate successfully",
                    documents));
            }
            catch (Exception ex)
            {
                return StatusCode(500, ApiResponse<object>.ErrorResponse(
                    MessageCodes.ER5000,
                    ex.Message,
                    500));
            }
        }

        [HttpPost]
        public async Task<IActionResult> Create([FromBody] CreateCandidateDocumentRequest request)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return ValidationHelper.CreateValidationErrorResponse(ModelState);
                }
                var documentId = await _documentService.CreateDocumentAsync(request);
                return CreatedAtAction(nameof(GetById), new { id = documentId },
                    ApiResponse<object>.SuccessResponse(
                        MessageCodes.SC2001,
                        "Document created successfully",
                        new { DocumentId = documentId },
                        201));
            }
            catch (InvalidOperationException ex)
            {
                return BadRequest(ApiResponse<object>.ErrorResponse(
                    MessageCodes.ER4005,
                    ex.Message,
                    400,
                    new List<ErrorDetail>
                    {
                        new ErrorDetail
                        {
                            Field = "request",
                            ErrorCode = MessageCodes.ER4005,
                            Message = ex.Message
                        }
                    }));
            }
            catch (Exception ex)
            {
                return StatusCode(500, ApiResponse<object>.ErrorResponse(
                    MessageCodes.ER5000,
                    ex.Message,
                    500));
            }
        }

        [HttpDelete("{id}")]
        public async Task<IActionResult> Delete(Guid id)
        {
            
            try
            {
                var result = await _documentService.DeleteDocumentAsync(id);
                if (!result)
                    return NotFound(ApiResponse<object>.ErrorResponse(
                        MessageCodes.ER4004,
                        "Document not found",
                        404));

                return Ok(ApiResponse<object>.SuccessResponse(
                    MessageCodes.SC2003,
                    "Document deleted successfully",
                    true));
            }
            catch (Exception ex)
            {
                return StatusCode(500, ApiResponse<object>.ErrorResponse(
                    MessageCodes.ER5000,
                    ex.Message,
                    500));
            }
        }
    }
}
