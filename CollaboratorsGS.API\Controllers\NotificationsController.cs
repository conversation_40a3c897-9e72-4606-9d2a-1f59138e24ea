using CollaboratorsGS.Application.DTOs;
using CollaboratorsGS.Application.Interfaces;
using CollaboratorsGS.Infrastructure.Configurations;
using CollaboratorsGS.Infrastructure.Utilities;
using Google.Protobuf.WellKnownTypes;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Options;

namespace CollaboratorsGS.API.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class NotificationsController : ControllerBase
    {
        private readonly INotificationService _notificationService;
        private readonly ILogger<NotificationsController> _logger;
        private readonly FirebaseConfig _firebaseConfig;

        public NotificationsController(
            INotificationService notificationService,
            ILogger<NotificationsController> logger,
            IOptions<FirebaseConfig> firebaseConfig)
        {
            _notificationService = notificationService;
            _logger = logger;
            _firebaseConfig = firebaseConfig.Value;
        }

        [HttpPost("send")]
        public async Task<IActionResult> SendNotification([FromBody] NotificationRequest request)
        {
            try
            {
                var result = await _notificationService.SendNotificationAsync(request);
                if (result)
                {
                    return Ok(ApiResponse<object>.SuccessResponse(
                        MessageCodes.SC2000,
                        "Collaborator created successfully",
                        200));
                }
                else
                {
                    return BadRequest(ApiResponse<object>.ErrorResponse(
                        MessageCodes.ER4005,
                        "Failed to send notification",
                        400));
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error sending notification");
                return StatusCode(500, ApiResponse<object>.ErrorResponse(
                        MessageCodes.ER5000,
                        $"Error sending notification: {ex.Message}" ,
                        500));
            }
        }

        [HttpPost("device-token")]
        public async Task<IActionResult> SaveDeviceToken([FromBody] DeviceTokenRequest request)
        {
            try
            {
                if (string.IsNullOrEmpty(request.Token) || string.IsNullOrEmpty(request.UserId))
                {
                    return BadRequest(new { Success = false, Message = "Token and UserId are required" });
                }

                var result = await _notificationService.SaveDeviceTokenAsync(request);
                if (result)
                {
                    return Ok(new { Success = true, Message = "Device token saved successfully" });
                }
                else
                {
                    return BadRequest(new { Success = false, Message = "Failed to save device token" });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error saving device token");
                return StatusCode(500, new { Success = false, Message = $"Error saving device token: {ex.Message}" });
            }
        }

        [HttpDelete("device-token/{token}")]
        public async Task<IActionResult> RemoveDeviceToken(string token)
        {
            try
            {
                if (string.IsNullOrEmpty(token))
                {
                    return BadRequest(ApiResponse<object>.ErrorResponse(
                        MessageCodes.ER4001,
                        "Token is required",
                        400));
                }

                var result = await _notificationService.RemoveDeviceTokenAsync(token);
                if (result)
                {
                    return Ok(ApiResponse<object>.SuccessResponse(
                        MessageCodes.SC2000,
                        "Device token removed successfully",
                        200));
                }
                else
                {
                    return BadRequest(ApiResponse<object>.ErrorResponse(
                        MessageCodes.ER4005,
                        "Failed to remove device token",
                        400));
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error removing device token");
                return StatusCode(500, ApiResponse<object>.ErrorResponse(
                    MessageCodes.ER5000,
                    $"Error removing device token: {ex.Message}",
                    500));
            }
        }

        [HttpGet("device-tokens/{userId}")]
        public async Task<IActionResult> GetUserDeviceTokens(string userId)
        {
            try
            {
                if (string.IsNullOrEmpty(userId))
                {
                    return BadRequest(ApiResponse<object>.ErrorResponse(
                        MessageCodes.ER4002,
                        "UserId is required",
                        400));
                }

                var tokens = await _notificationService.GetUserDeviceTokensAsync(userId);
                return Ok(ApiResponse<List<string>>.SuccessResponse(
                    MessageCodes.SC2000,
                    "Retrieved user device tokens successfully",
                    tokens));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting user device tokens");
                return StatusCode(500, ApiResponse<object>.ErrorResponse(
                    MessageCodes.ER5000,
                    $"Error getting user device tokens: {ex.Message}",
                    500));
            }
        }

        [HttpPost("subscribe")]
        public async Task<IActionResult> SubscribeToTopic([FromQuery] string token, [FromQuery] string topic)
        {
            try
            {
                if (string.IsNullOrEmpty(token) || string.IsNullOrEmpty(topic))
                {
                    return BadRequest(ApiResponse<object>.ErrorResponse(
                        MessageCodes.ER4002,
                        "Token and Topic are required",
                        400));
                }

                var result = await _notificationService.SubscribeToTopicAsync(token, topic);
                if (result)
                {
                    return Ok(ApiResponse<object>.SuccessResponse(
                        MessageCodes.SC2001,
                        "Subscribed to topic successfully",
                        201));
                }
                else
                {
                    return BadRequest(ApiResponse<object>.ErrorResponse(
                        MessageCodes.ER4005,
                        "Failed to subscribe to topic",
                        400));
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error subscribing to topic");
                return StatusCode(500, ApiResponse<object>.ErrorResponse(
                    MessageCodes.ER5000,
                    $"Error subscribing to topic: {ex.Message}",
                    500));
            }
        }

        [HttpPost("unsubscribe")]
        public async Task<IActionResult> UnsubscribeFromTopic([FromQuery] string token, [FromQuery] string topic)
        {
            try
            {
                if (string.IsNullOrEmpty(token) || string.IsNullOrEmpty(topic))
                {
                    return BadRequest(ApiResponse<object>.ErrorResponse(
                        MessageCodes.ER4002,
                        "Token and Topic are required",
                        400));
                }

                var result = await _notificationService.UnsubscribeFromTopicAsync(token, topic);
                if (result)
                {
                    return Ok(ApiResponse<object>.SuccessResponse(
                        MessageCodes.SC2001,
                        "Unsubscribed from topic successfully",
                        201));
                }
                else
                {
                    return BadRequest(ApiResponse<object>.ErrorResponse(
                        MessageCodes.ER4005,
                        "Failed to unsubscribe from topic",
                        400));
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error unsubscribing from topic");
                return StatusCode(500, ApiResponse<object>.ErrorResponse(
                    MessageCodes.ER5000,
                    $"Error unsubscribing from topic: {ex.Message}",
                    500));
            }
        }

        [HttpGet("config")]
        public IActionResult GetFirebaseConfig()
        {
            try
            {
                // Return Firebase configuration for client-side initialization from appsettings.json
                var firebaseConfig = new
                { 
                    ApiKey = _firebaseConfig.ApiKey,
                    AuthDomain = _firebaseConfig.AuthDomain,
                    ProjectId = _firebaseConfig.ProjectId,
                    StorageBucket = _firebaseConfig.StorageBucket,
                    MessagingSenderId = _firebaseConfig.MessagingSenderId,
                    AppId = _firebaseConfig.AppId,
                    MeasurementId = _firebaseConfig.MeasurementId,
                    VapidKey = _firebaseConfig.VapidKey
                };
                return Ok(ApiResponse<object>.SuccessResponse(
                    MessageCodes.SC2000,
                    "Get Firebase config successfully",
                    firebaseConfig));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting Firebase config");
                return StatusCode(500, ApiResponse<object>.ErrorResponse(
                    MessageCodes.ER5000,
                    $"Error getting Firebase config: {ex.Message}",
                    500));
            }
        }
    }
}
