using AutoMapper;
using CollaboratorsGS.Application.DTOs.CollaboratorReward;
using CollaboratorsGS.Application.Interfaces;
using CollaboratorsGS.Domain.Entities;
using CollaboratorsGS.Domain.Repositories;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using System.Security.Claims;

namespace CollaboratorsGS.Infrastructure.Services
{
    public class CollaboratorRewardService : ICollaboratorRewardService
    {
        private readonly ICollaboratorRewardRepository _collaboratorRewardRepository;
        private readonly ICollaboratorRepository _collaboratorRepository;
        private readonly IActivityLogService _activityLogService;
        private readonly IHttpContextAccessor _httpContextAccessor;
        private readonly IMapper _mapper;
        private readonly ILogger<CollaboratorRewardService> _logger;

        public CollaboratorRewardService(
            ICollaboratorRewardRepository collaboratorRewardRepository,
            ICollaboratorRepository collaboratorRepository,
            IActivityLogService activityLogService,
            IHttpContextAccessor httpContextAccessor,
            IMapper mapper,
            ILogger<CollaboratorRewardService> logger)
        {
            _collaboratorRewardRepository = collaboratorRewardRepository;
            _collaboratorRepository = collaboratorRepository;
            _activityLogService = activityLogService;
            _httpContextAccessor = httpContextAccessor;
            _mapper = mapper;
            _logger = logger;
        }

        public async Task<CollaboratorRewardDto?> GetByIdAsync(Guid rewardId)
        {
            var reward = await _collaboratorRewardRepository.GetByIdAsync(rewardId);
            return _mapper.Map<CollaboratorRewardDto>(reward);
        }

        public async Task<IEnumerable<CollaboratorRewardDto>> GetAllAsync()
        {
            var rewards = await _collaboratorRewardRepository.GetAllAsync();
            return _mapper.Map<IEnumerable<CollaboratorRewardDto>>(rewards);
        }

        public async Task<IEnumerable<CollaboratorRewardDto>> GetByCollaboratorIdAsync(Guid collaboratorId)
        {
            var rewards = await _collaboratorRewardRepository.GetByCollaboratorIdAsync(collaboratorId);
            return _mapper.Map<IEnumerable<CollaboratorRewardDto>>(rewards);
        }

        public async Task<IEnumerable<CollaboratorRewardDto>> GetByStatusAsync(string status)
        {
            var rewards = await _collaboratorRewardRepository.GetByStatusAsync(status);
            return _mapper.Map<IEnumerable<CollaboratorRewardDto>>(rewards);
        }

        public async Task<Guid> CreateCollaboratorRewardAsync(CreateCollaboratorRewardRequest request)
        {
            // Verify collaborator exists
            var collaborator = await _collaboratorRepository.GetByIdAsync(request.CollaboratorId);
            if (collaborator == null)
            {
                throw new InvalidOperationException($"Collaborator with ID {request.CollaboratorId} not found");
            }

            // Map request to entity
            var reward = new CollaboratorReward
            {
                CollaboratorId = request.CollaboratorId,
                ApplicationId = request.ApplicationId,
                RewardType = request.RewardType,
                Amount = request.Amount,
                LevelId = request.LevelId,
                RewardDate = DateTime.UtcNow,
                ScheduledPaymentDate = request.ScheduledPaymentDate,
                Status = "Pending"
            };

            // Create reward
            var rewardId = await _collaboratorRewardRepository.CreateAsync(reward);

            // Log activity
            var newValue = $"Amount: {reward.Amount:N0} VND, Type: {reward.RewardType}, Status: {reward.Status}";
            await LogActivityAsync("Create", "CollaboratorReward", rewardId, null, newValue);

            return rewardId;
        }

        public async Task<CollaboratorRewardDto?> GetCreatedCollaboratorRewardAsync(Guid rewardId)
        {
            return await GetByIdAsync(rewardId);
        }

        public async Task<CollaboratorRewardDto?> UpdateCollaboratorRewardAsync(UpdateCollaboratorRewardRequest request)
        {
            // Check if reward exists
            var existingReward = await _collaboratorRewardRepository.GetByIdAsync(request.RewardId);
            if (existingReward == null)
            {
                throw new InvalidOperationException($"Reward with ID {request.RewardId} not found");
            }

            // Store old values for audit log
            var oldValue = $"Amount: {existingReward.Amount:N0} VND, Status: {existingReward.Status}";

            // Map request to entity
            var reward = new CollaboratorReward
            {
                RewardId = request.RewardId,
                CollaboratorId = existingReward.CollaboratorId,
                ApplicationId = existingReward.ApplicationId,
                RewardType = existingReward.RewardType,
                Amount = request.Amount,
                LevelId = existingReward.LevelId,
                RewardDate = existingReward.RewardDate,
                ScheduledPaymentDate = request.ScheduledPaymentDate,
                Status = request.Status
            };

            // Update reward
            var result = await _collaboratorRewardRepository.UpdateAsync(reward);

            if (!result)
                return null;

            // Log activity with old and new values
            var newValue = $"Amount: {reward.Amount:N0} VND, Status: {reward.Status}";
            await LogActivityAsync("Update", "CollaboratorReward", request.RewardId, oldValue, newValue);

            // Get the updated reward
            return await GetByIdAsync(request.RewardId);
        }

        public async Task<bool> DeleteCollaboratorRewardAsync(Guid rewardId)
        {
            // Check if reward exists
            var existingReward = await _collaboratorRewardRepository.GetByIdAsync(rewardId);
            if (existingReward == null)
            {
                throw new InvalidOperationException($"Reward with ID {rewardId} not found");
            }

            // Delete reward
            return await _collaboratorRewardRepository.DeleteAsync(rewardId);
        }

        public async Task<bool> ProcessPaymentAsync(Guid rewardId, string paymentMethod)
        {
            // Get the reward
            var reward = await _collaboratorRewardRepository.GetByIdAsync(rewardId);
            if (reward == null)
            {
                throw new InvalidOperationException($"Reward with ID {rewardId} not found");
            }

            // Create payment history record
            var history = new CollaboratorRewardHistory
            {
                CollaboratorId = reward.CollaboratorId,
                RewardId = rewardId,
                Amount = reward.Amount,
                PaymentDate = DateTime.UtcNow,
                PaymentMethod = paymentMethod,
                Status = "Completed"
            };

            // Save payment history
            await _collaboratorRewardRepository.CreateHistoryAsync(history);

            // Update reward status
            var oldStatus = reward.Status;
            reward.Status = "Paid";
            await _collaboratorRewardRepository.UpdateAsync(reward);

            // Log payment activity
            var oldValue = $"Amount: {reward.Amount:N0} VND, Status: {oldStatus}";
            var newValue = $"Amount: {reward.Amount:N0} VND, Status: Paid, Method: {paymentMethod}";
            await LogActivityAsync("Pay", "CollaboratorReward", rewardId, oldValue, newValue);

            return true;
        }

        public async Task<IEnumerable<CollaboratorRewardHistoryDto>> GetHistoryByRewardIdAsync(Guid rewardId)
        {
            var history = await _collaboratorRewardRepository.GetHistoryByRewardIdAsync(rewardId);
            return _mapper.Map<IEnumerable<CollaboratorRewardHistoryDto>>(history);
        }

        public Task<Guid> CreateCollaboratorRewardHistoryAsync(CreateCollaboratorRewardHistoryRequest request)
        {
            throw new NotImplementedException();
        }

        public async Task<CollaboratorRewardOperationsDto> GetRewardOperationsAsync(Guid collaboratorId, int? month, int? year, string action)
        {
            var response = new CollaboratorRewardOperationsDto();

            // If action is "All", get all data types
            if (action.Equals("All", StringComparison.OrdinalIgnoreCase))
            {
                // Get Summary
                var summaryResults = await _collaboratorRewardRepository.GetRewardOperationsAsync(collaboratorId, month, year, "Summary");
                var summaryList = ((IEnumerable<dynamic>)summaryResults).ToList();
                if (summaryList.Any())
                {
                    var summaryData = summaryList.First();
                    response.Summary = new RewardSummaryDto
                    {
                        PaidAmount = summaryData.paid_amount ?? 0,
                        UnpaidAmount = summaryData.unpaid_amount ?? 0,
                        TotalReward = summaryData.total_reward ?? 0
                    };
                }

                // Get ByType
                var byTypeResults = await _collaboratorRewardRepository.GetRewardOperationsAsync(collaboratorId, month, year, "ByType");
                var byTypeList = ((IEnumerable<dynamic>)byTypeResults).ToList();
                response.RewardsByType = byTypeList.Select(r => new RewardByTypeDto
                {
                    RewardType = r.reward_type ?? string.Empty,
                    TotalAmount = r.total_amount ?? 0
                });

                // Get UpcomingPayment
                var upcomingResults = await _collaboratorRewardRepository.GetRewardOperationsAsync(collaboratorId, month, year, "UpcomingPayment");
                var upcomingList = ((IEnumerable<dynamic>)upcomingResults).ToList();
                if (upcomingList.Any())
                {
                    var paymentData = upcomingList.First();
                    response.UpcomingPayment = new UpcomingPaymentDto
                    {
                        ScheduledPaymentDate = paymentData.scheduled_payment_date ?? DateTime.MinValue,
                        AmountExpected = paymentData.amount_expected ?? 0,
                        DaysRemaining = paymentData.days_remaining ?? 0
                    };
                }

                // Get RewardDetails
                var detailsResults = await _collaboratorRewardRepository.GetRewardOperationsAsync(collaboratorId, month, year, "RewardDetails");
                var detailsList = ((IEnumerable<dynamic>)detailsResults).ToList();
                response.RewardDetails = detailsList.Select(r => new RewardDetailDto
                {
                    CandidateName = r.candidate_name ?? string.Empty,
                    Gender = r.gender ?? string.Empty,
                    RewardDate = r.reward_date ?? DateTime.MinValue,
                    Position = r.position ?? string.Empty,
                    RewardType = r.reward_type ?? string.Empty,
                    Amount = r.amount ?? 0,
                    Status = r.status ?? string.Empty
                });

                // Get RewardHistory
                var historyResults = await _collaboratorRewardRepository.GetRewardOperationsAsync(collaboratorId, month, year, "RewardHistory");
                var historyList = ((IEnumerable<dynamic>)historyResults).ToList();
                response.RewardHistory = historyList.Select(r => new RewardHistoryDto
                {
                    RewardId = r.reward_id ?? Guid.Empty,
                    Amount = r.amount ?? 0,
                    PaymentDate = r.payment_date ?? DateTime.MinValue,
                    PaymentMethod = r.payment_method ?? string.Empty,
                    RewardStatus = r.reward_status ?? string.Empty,
                    CandidateName = r.candidate_name ?? string.Empty,
                    PositionName = r.position_name ?? string.Empty,
                    RewardType = r.reward_type ?? string.Empty,
                    RewardDate = r.reward_date ?? DateTime.MinValue
                });

                return response;
            }

            // Handle individual actions
            var results = await _collaboratorRewardRepository.GetRewardOperationsAsync(collaboratorId, month, year, action);
            var resultList = ((IEnumerable<dynamic>)results).ToList();

            switch (action.ToLower())
            {
                case "summary":
                    if (resultList.Any())
                    {
                        var summaryData = resultList.First();
                        response.Summary = new RewardSummaryDto
                        {
                            PaidAmount = summaryData.paid_amount ?? 0,
                            UnpaidAmount = summaryData.unpaid_amount ?? 0,
                            TotalReward = summaryData.total_reward ?? 0
                        };
                    }
                    break;

                case "bytype":
                    response.RewardsByType = resultList.Select(r => new RewardByTypeDto
                    {
                        RewardType = r.reward_type ?? string.Empty,
                        TotalAmount = r.total_amount ?? 0
                    });
                    break;

                case "upcomingpayment":
                    if (resultList.Any())
                    {
                        var paymentData = resultList.First();
                        response.UpcomingPayment = new UpcomingPaymentDto
                        {
                            ScheduledPaymentDate = paymentData.scheduled_payment_date ?? DateTime.MinValue,
                            AmountExpected = paymentData.amount_expected ?? 0,
                            DaysRemaining = paymentData.days_remaining ?? 0
                        };
                    }
                    break;

                case "rewarddetails":
                    response.RewardDetails = resultList.Select(r => new RewardDetailDto
                    {
                        CandidateName = r.candidate_name ?? string.Empty,
                        Gender = r.gender ?? string.Empty,
                        RewardDate = r.reward_date ?? DateTime.MinValue,
                        Position = r.position ?? string.Empty,
                        RewardType = r.reward_type ?? string.Empty,
                        Amount = r.amount ?? 0,
                        Status = r.status ?? string.Empty
                    });
                    break;

                case "rewardhistory":
                    response.RewardHistory = resultList.Select(r => new RewardHistoryDto
                    {
                        RewardId = r.reward_id ?? Guid.Empty,
                        Amount = r.amount ?? 0,
                        PaymentDate = r.payment_date ?? DateTime.MinValue,
                        PaymentMethod = r.payment_method ?? string.Empty,
                        RewardStatus = r.reward_status ?? string.Empty,
                        CandidateName = r.candidate_name ?? string.Empty,
                        PositionName = r.position_name ?? string.Empty,
                        RewardType = r.reward_type ?? string.Empty,
                        RewardDate = r.reward_date ?? DateTime.MinValue
                    });
                    break;
            }

            return response;
        }

        private async Task LogActivityAsync(string action, string entityType, Guid entityId, string? oldValue = null, string? newValue = null)
        {
            try
            {
                var userIdClaim = _httpContextAccessor.HttpContext?.User?.FindFirst(ClaimTypes.NameIdentifier)
                    ?? _httpContextAccessor.HttpContext?.User?.FindFirst("sub");

                if (userIdClaim != null && Guid.TryParse(userIdClaim.Value, out var userId))
                {
                    // Get IP address from HttpContext
                    var ipAddress = GetClientIpAddress();

                    await _activityLogService.LogActivityAsync(userId, action, entityType, entityId, oldValue, newValue, ipAddress);
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Failed to log activity for {Action} on {EntityType} {EntityId}", action, entityType, entityId);
                // Don't throw - logging failure shouldn't break the main operation
            }
        }

        private string GetClientIpAddress()
        {
            var context = _httpContextAccessor.HttpContext;
            if (context == null) return "Unknown";

            // Try to get IP from various headers
            var headerNames = new[]
            {
                "X-Forwarded-For",
                "Forwarded",
                "X-Real-IP",
                "CF-Connecting-IP",
                "True-Client-IP",
                "X-Client-IP"
            };

            foreach (var headerName in headerNames)
            {
                if (context.Request.Headers.TryGetValue(headerName, out var headerValue) &&
                    !string.IsNullOrEmpty(headerValue))
                {
                    var ip = headerValue.ToString().Split(',')[0].Trim();
                    if (!string.IsNullOrEmpty(ip))
                    {
                        return ip;
                    }
                }
            }

            // Fallback to RemoteIpAddress
            return context.Connection.RemoteIpAddress?.ToString() ?? "Unknown";
        }
    }
}
