using System;

namespace CollaboratorsGS.Application.DTOs.CollaboratorLevel
{
    public class CollaboratorLevelDetailDto
    {
        public CollaboratorCurrentLevelDto CurrentLevel { get; set; } = new();
        public List<CollaboratorLevelBenefitDto> Benefits { get; set; } = new();
        public List<CollaboratorLevelRequirementDto> Requirements { get; set; } = new();
        public CollaboratorNextLevelDto? NextLevel { get; set; }
        public List<CollaboratorLevelHistoryDto> LevelHistory { get; set; } = new();
        public bool CanUpgrade { get; set; }
    }

    public class CollaboratorCurrentLevelDto
    {
        public Guid LevelId { get; set; }
        public string LevelName { get; set; } = string.Empty;
        public int LevelNumber { get; set; }
        public string Description { get; set; } = string.Empty;
        public DateTime AchievedDate { get; set; }
        public int StarRating { get; set; }
    }

    public class CollaboratorLevelBenefitDto
    {
        public string Title { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public bool IsActive { get; set; }
    }

    public class CollaboratorLevelRequirementDto
    {
        public string Title { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public int CurrentValue { get; set; }
        public int RequiredValue { get; set; }
        public decimal ProgressPercentage { get; set; }
        public bool IsMet { get; set; }
    }

    public class CollaboratorNextLevelDto
    {
        public Guid LevelId { get; set; }
        public string LevelName { get; set; } = string.Empty;
        public int LevelNumber { get; set; }
        public string Description { get; set; } = string.Empty;
        public List<string> Benefits { get; set; } = new();
        public bool CanUpgrade { get; set; }
    }

    public class CollaboratorLevelHistoryDto
    {
        public string LevelName { get; set; } = string.Empty;
        public DateTime AchievedDate { get; set; }
        public string Status { get; set; } = string.Empty; // "Current", "Previous"
    }

    public class LevelUpgradeCheckDto
    {
        public bool CanUpgrade { get; set; }
        public string Message { get; set; } = string.Empty;
    }
}
