using System;

namespace CollaboratorsGS.Application.DTOs.Collaborator
{
    public class CollaboratorProfileDto
    {
        // Basic collaborator information
        public string FullName { get; set; } = string.Empty;
        public string PhoneNumber { get; set; } = string.Empty;
        public string Email { get; set; } = string.Empty;
        public Guid LevelId { get; set; }
        public string? LevelName { get; set; }
        public string Status { get; set; } = string.Empty;
        public DateTime? LastLevelUpdatedAt { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime? UpdatedAt { get; set; }
        public string? PhotoUrl { get; set; }

        // Profile information
        public string? CitizenId { get; set; }
        public string? PermanentAddress { get; set; }
        public string? CurrentAddress { get; set; }
        public string? BankName { get; set; }
        public string? BankAccountNumber { get; set; }
        public string? BankAccountName { get; set; } // Bank account owner name

        // Avatar URL (stored in database)
        public string? Avatar { get; set; }

        // Extracted citizen ID information
        public string? CitizenIdExtracted { get; set; }
        public string? FullNameExtracted { get; set; }
        public DateTime? DateOfBirthExtracted { get; set; }
        public string? GenderExtracted { get; set; }
        public string? AddressExtracted { get; set; }
        public string? PersonalIdentificationExtracted { get; set; }
        public DateTime? IdIssueDateExtracted { get; set; }
        public string? IdIssueAuthorityExtracted { get; set; }
        
        // Extraction metadata
        public DateTime? ExtractionTimestamp { get; set; } // When extraction was performed
        public string? DataSource { get; set; } // 'manual' or 'extracted'
    }
}
