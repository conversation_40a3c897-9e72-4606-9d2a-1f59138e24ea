using AutoMapper;
using CollaboratorsGS.Application.DTOs.Candidate;
using CollaboratorsGS.Application.DTOs.Common;
using CollaboratorsGS.Application.Interfaces;
using CollaboratorsGS.Domain.Entities;
using CollaboratorsGS.Domain.Repositories;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using System.Data;
using System.Security.Claims;

namespace CollaboratorsGS.Infrastructure.Services
{
    public class CandidateService : ICandidateService
    {
        private readonly ICandidateRepository _candidateRepository;
        private readonly ICandidateDocumentRepository _documentRepository;
        private readonly IActivityLogService _activityLogService;
        private readonly IHttpContextAccessor _httpContextAccessor;
        private readonly IMapper _mapper;
        private readonly ILogger<CandidateService> _logger;

        public CandidateService(
            ICandidateRepository candidateRepository,
            IMapper mapper,
            ILogger<CandidateService> logger,
            ICandidateDocumentRepository documentRepository,
            IActivityLogService activityLogService,
            IHttpContextAccessor httpContextAccessor)
        {
            _candidateRepository = candidateRepository;
            _documentRepository = documentRepository;
            _mapper = mapper;
            _logger = logger;
            _activityLogService = activityLogService;
            _httpContextAccessor = httpContextAccessor;
        }

        public async Task<CandidateDto?> GetByIdAsync(Guid candidateId)
        {
            var candidate = await _candidateRepository.GetByIdAsync(candidateId);
            if (candidate == null)
                return null;
  
            var candidateDto = _mapper.Map<CandidateDto>(candidate);

            // Map collaborator name if available
            if (candidate.Collaborator != null)
            {
                candidateDto.CollaboratorName = candidate.Collaborator.FullName;
            }

            return candidateDto;
        }

        public async Task<IEnumerable<CandidateDto>> GetAllAsync()
        {
            var candidates = await _candidateRepository.GetAllAsync();
            var candidateDtos = _mapper.Map<IEnumerable<CandidateDto>>(candidates);

            // Map collaborator names
            foreach (var candidateDto in candidateDtos)
            {
                var candidate = candidates.FirstOrDefault(c => c.CandidateId == candidateDto.CandidateId);
                if (candidate?.Collaborator != null)
                {
                    candidateDto.CollaboratorName = candidate.Collaborator.FullName;
                }
            }

            return candidateDtos;
        }

        public async Task<IEnumerable<CandidateDto>> GetByCollaboratorIdAsync(Guid collaboratorId)
        {
            var candidates = await _candidateRepository.GetByCollaboratorIdAsync(collaboratorId);
            var candidateDtos = _mapper.Map<IEnumerable<CandidateDto>>(candidates);

            // Map collaborator names
            foreach (var candidateDto in candidateDtos)
            {
                var candidate = candidates.FirstOrDefault(c => c.CandidateId == candidateDto.CandidateId);
                if (candidate?.Collaborator != null)
                {
                    candidateDto.CollaboratorName = candidate.Collaborator.FullName;
                }
            }

            return candidateDtos;
        }

        public async Task<Guid> CreateCandidateAsync(CreateCandidateRequest request)
        {
            // Check if email is provided and if it's already in use
                
            
            if (!string.IsNullOrEmpty(request.Email))
            {
                var existingByEmail = await _candidateRepository.GetByEmailAsync(request.Email);
                if (existingByEmail != null)
                {
                    throw new InvalidOperationException($"Candidate with email {request.Email} already exists");
                }
            }

                // Check if phone number is provided and if it's already in use
                var existingByPhone = await _candidateRepository.GetByPhoneNumberAsync(request.PhoneNumber);
                if (existingByPhone != null)
                {
                    throw new InvalidOperationException($"Candidate with phone number {request.PhoneNumber} already exists");
                }

                // Check if citizen_id is provided and if it's already in use
                if (!string.IsNullOrEmpty(request.CitizenId))
                {
                    var existingByCitizenId = await _candidateRepository.GetByCitizenIdAsync(request.CitizenId);
                    if (existingByCitizenId != null)
                    {
                        throw new InvalidOperationException($"Candidate with citizen ID {request.CitizenId} already exists");
                    }
                }

                // Create candidate
                var candidate = new Candidate
                {
                    FullName = request.FullName,
                    PhoneNumber = request.PhoneNumber,
                    Email = request.Email,
                    EducationLevel = request.EducationLevel,
                    WorkExperience = request.WorkExperience,
                    Skills = request.Skills,
                    DateOfBirth = DateHelper.ParseDateString(request.DateOfBirth),
                    Address = request.Address,
                    ProfilePicture = request.ProfilePicture,
                    FullBodyPicture = request.FullBodyPicture,
                    HeightCm = request.HeightCm,
                    WeightKg = request.WeightKg,
                    Level = request.Level,
                    Source = request.Source,
                    CollaboratorId = request.CollaboratorId,
                    CitizenId = request.CitizenId,
                    CitizenIdAddress = request.CitizenIdAddress,
                    CitizenIdIssueDate = DateHelper.ParseDateString(request.CitizenIdIssueDate),
                    CitizenIdIssuePlace = request.CitizenIdIssuePlace
                };

                var candidateId = await _candidateRepository.CreateAsync(candidate);

                // Log activity
                var newValue = $"FullName: {candidate.FullName}, Email: {candidate.Email}, Phone: {candidate.PhoneNumber}";
                await LogActivityAsync("Create", "Candidate", candidateId, null, newValue);

                // Process documents if provided
                if (request.Documents != null && request.Documents.Any())
                {
                    foreach (var doc in request.Documents)
                    {
                        var document = new CandidateDocument
                        {
                            DocumentId=Guid.NewGuid(),
                            CandidateId = candidateId,
                            DocumentType = doc.DocumentType,
                            FilePath = doc.FilePath,
                            FileType = doc.FileType,
                            UploadedAt = DateTime.UtcNow
                        };

                        await _documentRepository.CreateAsync(document);
                    }
                }

                return candidateId;
            }

        public async Task<CandidateDto?> GetCreatedCandidateAsync(Guid candidateId)
        {
            var candidate = await _candidateRepository.GetByIdAsync(candidateId);
            if (candidate == null)
                return null;

            var candidateDto = _mapper.Map<CandidateDto>(candidate);

            // Map collaborator name if available
            if (candidate.Collaborator != null)
            {
                candidateDto.CollaboratorName = candidate.Collaborator.FullName;
            }

            return candidateDto;
        }

        public async Task<CandidateDto?> UpdateCandidateAsync(Guid candidateId, UpdateCandidateRequest request)
        {
            // Check if candidate exists
            var existingCandidate = await _candidateRepository.GetByIdAsync(candidateId);
            if (existingCandidate == null)
            {
                throw new InvalidOperationException($"Candidate with ID {candidateId} not found");
            }

            // Store old values for audit log
            var oldValue = $"FullName: {existingCandidate.FullName}, Email: {existingCandidate.Email}, Phone: {existingCandidate.PhoneNumber}";

            // Check if email is unique (if changed and not null)
            if (existingCandidate.Email != request.Email && !string.IsNullOrEmpty(request.Email))
            {
                var existingByEmail = await _candidateRepository.GetByEmailAsync(request.Email);
                if (existingByEmail != null && existingByEmail.CandidateId != candidateId)
                {
                    throw new InvalidOperationException($"Candidate with email {request.Email} already exists");
                }
            }

            // Check if phone number is unique (if changed)
            if (existingCandidate.PhoneNumber != request.PhoneNumber)
            {
                var existingByPhone = await _candidateRepository.GetByPhoneNumberAsync(request.PhoneNumber);
                if (existingByPhone != null && existingByPhone.CandidateId != candidateId)
                {
                    throw new InvalidOperationException($"Candidate with phone number {request.PhoneNumber} already exists");
                }
            }

            // Check if citizen_id is unique (if changed and not null)
            if (existingCandidate.CitizenId != request.CitizenId && !string.IsNullOrEmpty(request.CitizenId))
            {
                var existingByCitizenId = await _candidateRepository.GetByCitizenIdAsync(request.CitizenId);
                if (existingByCitizenId != null && existingByCitizenId.CandidateId != candidateId)
                {
                    throw new InvalidOperationException($"Candidate with citizen ID {request.CitizenId} already exists");
                }
            }

            // Update properties
            existingCandidate.FullName = request.FullName;
            existingCandidate.PhoneNumber = request.PhoneNumber;
            existingCandidate.Email = request.Email;
            existingCandidate.EducationLevel = request.EducationLevel;
            existingCandidate.WorkExperience = request.WorkExperience;
            existingCandidate.Skills = request.Skills;
            existingCandidate.DateOfBirth = DateHelper.ParseDateString(request.DateOfBirth);
            existingCandidate.Address = request.Address;
            existingCandidate.ProfilePicture = request.ProfilePicture;
            existingCandidate.FullBodyPicture = request.FullBodyPicture;
            existingCandidate.HeightCm = request.HeightCm;
            existingCandidate.WeightKg = request.WeightKg;
            existingCandidate.Level = request.Level;
            existingCandidate.Source = request.Source;
            existingCandidate.CollaboratorId = request.CtvId;
            existingCandidate.CitizenId = request.CitizenId;
            existingCandidate.CitizenIdAddress = request.CitizenIdAddress;
            existingCandidate.CitizenIdIssueDate = DateHelper.ParseDateString(request.CitizenIdIssueDate);
            existingCandidate.CitizenIdIssuePlace = request.CitizenIdIssuePlace;

            var result = await _candidateRepository.UpdateAsync(existingCandidate);
            if (!result)
                return null;

            // Log activity with old and new values
            var newValue = $"FullName: {request.FullName}, Email: {request.Email}, Phone: {request.PhoneNumber}";
            await LogActivityAsync("Update", "Candidate", candidateId, oldValue, newValue);

            // Get the updated candidate
            return await GetByIdAsync(candidateId);
        }

        public async Task<bool> DeleteCandidateAsync(Guid candidateId)
        {
            // Check if candidate exists
            var existingCandidate = await _candidateRepository.GetByIdAsync(candidateId);
            if (existingCandidate == null)
            {
                throw new InvalidOperationException($"Candidate with ID {candidateId} not found");
            }

            return await _candidateRepository.DeleteAsync(candidateId);
        }

        public async Task<CandidateDetailDto?> GetDetailByIdAsync(Guid candidateId)
        {
            try
            {
                var candidateDetail = await _candidateRepository.GetDetailByIdAsync(candidateId);
                if (candidateDetail == null)
                    return null;

                // Manual mapping from dynamic to CandidateDetailDto
                var dto = new CandidateDetailDto
                {
                    CandidateId = candidateDetail.CandidateId,
                    FullName = candidateDetail.FullName,
                    PhoneNumber = candidateDetail.PhoneNumber,
                    Email = candidateDetail.Email,
                    DateOfBirth = candidateDetail.DateOfBirth,
                    Gender = candidateDetail.Gender,
                    Address = candidateDetail.Address,
                    ProfilePicture = candidateDetail.ProfilePicture,
                    Level = candidateDetail.Level,
                    Source = candidateDetail.Source,
                    CtvId = candidateDetail.CtvId,
                    CollaboratorName = candidateDetail.CollaboratorName,
                    CreatedAt = candidateDetail.CreatedAt,
                    UpdatedAt = candidateDetail.UpdatedAt,
                    ApplicationDate = candidateDetail.ApplicationDate,
                    ApplicationStatus = candidateDetail.ApplicationStatus,
                    InterviewRound1Date = candidateDetail.InterviewRound1Date,
                    InterviewRound1Result = candidateDetail.InterviewRound1Result,
                    InterviewRound2Date = candidateDetail.InterviewRound2Date,
                    InterviewRound2Result = candidateDetail.InterviewRound2Result,
                    OnboardDate = candidateDetail.OnboardDate,
                    PostingId = candidateDetail.PostingId,
                    Title = candidateDetail.Title,
                    PositionName = candidateDetail.PositionName,
                    DepartmentName = candidateDetail.DepartmentName
                };

                return dto;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting candidate detail with ID {CandidateId}", candidateId);
                throw;
            }
        }

        public async Task<IEnumerable<CandidateDetailDto>> GetAllDetailsAsync()
        {
            try
            {
                var candidateDetails = await _candidateRepository.GetAllDetailsAsync();
                var dtoList = new List<CandidateDetailDto>();

                foreach (var detail in candidateDetails)
                {
                    // Manual mapping from dynamic to CandidateDetailDto
                    var dto = new CandidateDetailDto
                    {
                        CandidateId = detail.CandidateId,
                        FullName = detail.FullName,
                        PhoneNumber = detail.PhoneNumber,
                        Email = detail.Email,
                        DateOfBirth = detail.DateOfBirth,
                        Gender = detail.Gender,
                        Address = detail.Address,
                        ProfilePicture = detail.ProfilePicture,
                        Level = detail.Level,
                        Source = detail.Source,
                        CtvId = detail.CtvId,
                        CollaboratorName = detail.CollaboratorName,
                        CreatedAt = detail.CreatedAt,
                        UpdatedAt = detail.UpdatedAt,
                        ApplicationDate = detail.ApplicationDate,
                        ApplicationStatus = detail.ApplicationStatus,
                        InterviewRound1Date = detail.InterviewRound1Date,
                        InterviewRound1Result = detail.InterviewRound1Result,
                        InterviewRound2Date = detail.InterviewRound2Date,
                        InterviewRound2Result = detail.InterviewRound2Result,
                        OnboardDate = detail.OnboardDate,
                        PostingId = detail.PostingId,
                        Title = detail.Title,
                        PositionName = detail.PositionName,
                        DepartmentName = detail.DepartmentName
                    };

                    dtoList.Add(dto);
                }

                return dtoList;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting all candidate details");
                throw;
            }
        }

        public async Task<IEnumerable<CandidateDetailDto>> GetDetailsByCollaboratorIdAsync(Guid collaboratorId)
        {
            try
            {
                var candidateDetails = await _candidateRepository.GetDetailsByCollaboratorIdAsync(collaboratorId);
                var dtoList = new List<CandidateDetailDto>();

                foreach (var detail in candidateDetails)
                {
                    // Manual mapping from dynamic to CandidateDetailDto
                    var dto = new CandidateDetailDto
                    {
                        CandidateId = detail.CandidateId,
                        FullName = detail.FullName,
                        PhoneNumber = detail.PhoneNumber,
                        Email = detail.Email,
                        DateOfBirth = detail.DateOfBirth,
                        Gender = detail.Gender,
                        Address = detail.Address,
                        ProfilePicture = detail.ProfilePicture,
                        Level = detail.Level,
                        Source = detail.Source,
                        CtvId = detail.CtvId,
                        CollaboratorName = detail.CollaboratorName,
                        CreatedAt = detail.CreatedAt,
                        UpdatedAt = detail.UpdatedAt,
                        ApplicationDate = detail.ApplicationDate,
                        ApplicationStatus = detail.ApplicationStatus,
                        InterviewRound1Date = detail.InterviewRound1Date,
                        InterviewRound1Result = detail.InterviewRound1Result,
                        InterviewRound2Date = detail.InterviewRound2Date,
                        InterviewRound2Result = detail.InterviewRound2Result,
                        OnboardDate = detail.OnboardDate,
                        PostingId = detail.PostingId,
                        Title = detail.Title,
                        PositionName = detail.PositionName,
                        DepartmentName = detail.DepartmentName
                    };

                    dtoList.Add(dto);
                }

                return dtoList;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting candidate details for CTV with ID {CtvId}", collaboratorId);
                throw;
            }
        }

        private async Task LogActivityAsync(string action, string entityType, Guid entityId, string? oldValue = null, string? newValue = null)
        {
            try
            {
                var userIdClaim = _httpContextAccessor.HttpContext?.User?.FindFirst(ClaimTypes.NameIdentifier)
                    ?? _httpContextAccessor.HttpContext?.User?.FindFirst("sub");

                if (userIdClaim != null && Guid.TryParse(userIdClaim.Value, out var userId))
                {
                    // Get IP address from HttpContext
                    var ipAddress = GetClientIpAddress();

                    await _activityLogService.LogActivityAsync(userId, action, entityType, entityId, oldValue, newValue, ipAddress);
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Failed to log activity for {Action} on {EntityType} {EntityId}", action, entityType, entityId);
                // Don't throw - logging failure shouldn't break the main operation
            }
        }

        private string GetClientIpAddress()
        {
            var context = _httpContextAccessor.HttpContext;
            if (context == null) return "Unknown";

            // Try to get IP from various headers (same logic as RequestLoggingMiddleware)
            var headerNames = new[]
            {
                "X-Forwarded-For",
                "Forwarded",
                "X-Real-IP",
                "CF-Connecting-IP",
                "True-Client-IP",
                "X-Client-IP"
            };

            foreach (var headerName in headerNames)
            {
                if (context.Request.Headers.TryGetValue(headerName, out var headerValue) &&
                    !string.IsNullOrEmpty(headerValue))
                {
                    var ip = headerValue.ToString().Split(',')[0].Trim();
                    if (!string.IsNullOrEmpty(ip))
                    {
                        return ip;
                    }
                }
            }

            // Fallback to RemoteIpAddress
            return context.Connection.RemoteIpAddress?.ToString() ?? "Unknown";
        }

        public async  Task<Guid> CreateCandidateFromCVAsync(CreateCandidateFromCVRequest request, Guid collaboratorId, dynamic candidateInfo, string fileName, string fileExtension)
        {
            if (!string.IsNullOrEmpty(candidateInfo.Email))
            {
                var existingByEmail = await _candidateRepository.GetByEmailAsync(candidateInfo.Email);
                if (existingByEmail != null)
                {
                    throw new InvalidOperationException($"Candidate with email {candidateInfo.Email} already exists");
                }
            }

                // Check if phone number is provided and if it's already in use
                var existingByPhone = await _candidateRepository.GetByPhoneNumberAsync(candidateInfo.PhoneNumber);
                if (existingByPhone != null)
                {
                    throw new InvalidOperationException($"Candidate with phone number {candidateInfo.PhoneNumber} already exists");
                }

                // Create candidate
                var candidate = new Candidate
                {
                    FullName = candidateInfo.FullName,
                    PhoneNumber = candidateInfo.PhoneNumber,
                    Email = candidateInfo.Email,
                    EducationLevel = candidateInfo.Education,
                    WorkExperience = request.WorkExperience,
                    Skills = candidateInfo.Skills,
                    DateOfBirth = DateHelper.ParseDateString(request.DateOfBirth),
                    Address = request.Address,
                    Ward = request.Ward,
                    District = request.District,
                    Province = request.Province,
                    Level = request.Level,
                    Source = request.Source,
                    CollaboratorId = collaboratorId,
                    Documents = new List<CandidateDocument>
                    {
                        new CandidateDocument
                        {
                            DocumentId = Guid.NewGuid(),
                            CandidateId = Guid.Empty,
                            DocumentType = "CV",
                            FilePath = fileName,
                            FileType = fileExtension,
                            UploadedAt = DateTime.UtcNow
                        }
                    }

                };

                var candidateId = await _candidateRepository.CreateAsync(candidate);

                // Log activity
                var newValue = $"FullName: {candidate.FullName}, Email: {candidate.Email}, Phone: {candidate.PhoneNumber}";
                await LogActivityAsync("Create", "Candidate", candidateId, null, newValue);
                return candidateId;
        }
    }
}
