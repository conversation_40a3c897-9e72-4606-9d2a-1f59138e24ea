using System;

namespace CollaboratorsGS.Application.DTOs.CollaboratorReport
{
    public class CollaboratorReportDto
    {
        public Guid ReportId { get; set; }
        public Guid CollaboratorId { get; set; }
        public string CollaboratorName { get; set; } = string.Empty;
        public string ReportPeriod { get; set; } = string.Empty;
        public int TotalCandidates { get; set; }
        public decimal TotalPayment { get; set; }
        public DateTime ReportDate { get; set; }
        public string? Data { get; set; }
    }
}
