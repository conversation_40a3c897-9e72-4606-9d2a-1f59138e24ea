using AutoMapper;
using CollaboratorsGS.Application.DTOs.Company;
using CollaboratorsGS.Application.Interfaces;
using CollaboratorsGS.Domain.Entities;
using CollaboratorsGS.Domain.Repositories;
using Microsoft.Extensions.Logging;

namespace CollaboratorsGS.Infrastructure.Services
{
    public class CompanyService : ICompanyService
    {
        private readonly ICompanyRepository _companyRepository;
        private readonly IMapper _mapper;
        private readonly ILogger<CompanyService> _logger;

        public CompanyService(
            ICompanyRepository companyRepository,
            IMapper mapper,
            ILogger<CompanyService> logger)
        {
            _companyRepository = companyRepository;
            _mapper = mapper;
            _logger = logger;
        }

        public async Task<CompanyDto?> GetByIdAsync(Guid companyId)
        {
            try
            {
                var company = await _companyRepository.GetByIdAsync(companyId);
                if (company == null)
                    return null;

                return _mapper.Map<CompanyDto>(company);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting company with ID {CompanyId}", companyId);
                throw;
            }
        }

        public async Task<IEnumerable<CompanyDto>> GetAllAsync()
        {
            try
            {
                var companies = await _companyRepository.GetAllAsync();
                return _mapper.Map<IEnumerable<CompanyDto>>(companies);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting all companies");
                throw;
            }
        }

        public async Task<CompanyDto> CreateCompanyAsync(CreateCompanyRequest request)
        {
            try
            {
                var company = _mapper.Map<Company>(request);
                var companyId = await _companyRepository.CreateAsync(company);

                // Get the created company
                var createdCompany = await _companyRepository.GetByIdAsync(companyId);
                if (createdCompany == null)
                {
                    throw new InvalidOperationException($"Failed to retrieve created company with ID {companyId}");
                }

                return _mapper.Map<CompanyDto>(createdCompany);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating company");
                throw;
            }
        }

        public async Task<CompanyDto?> UpdateCompanyAsync(Guid companyId, UpdateCompanyRequest request)
        {
            try
            {
                // Check if company exists
                var existingCompany = await _companyRepository.GetByIdAsync(companyId);
                if (existingCompany == null)
                {
                    _logger.LogWarning("Company with ID {CompanyId} not found", companyId);
                    return null;
                }

                // Update properties
                existingCompany.CompanyName = request.CompanyName;
                existingCompany.PhoneNumber = request.PhoneNumber;
                existingCompany.Email = request.Email;

                var success = await _companyRepository.UpdateAsync(existingCompany);

                if (!success)
                {
                    return null;
                }

                // Get the updated company
                var updatedCompany = await _companyRepository.GetByIdAsync(companyId);
                return _mapper.Map<CompanyDto>(updatedCompany);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating company with ID {CompanyId}", companyId);
                throw;
            }
        }

        public async Task<bool> DeleteCompanyAsync(Guid companyId)
        {
            try
            {
                // Check if company exists
                var existingCompany = await _companyRepository.GetByIdAsync(companyId);
                if (existingCompany == null)
                {
                    _logger.LogWarning("Company with ID {CompanyId} not found", companyId);
                    return false;
                }

                return await _companyRepository.DeleteAsync(companyId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting company with ID {CompanyId}", companyId);
                throw;
            }
        }
    }
}
