using CollaboratorsGS.Application.Interfaces;
using CollaboratorsGS.Domain.Repositories;
using CollaboratorsGS.Infrastructure.Configurations;
using CollaboratorsGS.Infrastructure.Data;
using CollaboratorsGS.Infrastructure.Extensions;
using CollaboratorsGS.Infrastructure.Repositories;
using CollaboratorsGS.Infrastructure.Services;
using Microsoft.AspNetCore.Builder;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using FirebaseAdmin;

namespace CollaboratorsGS.Infrastructure
{
    public static class DependencyInjection
    {
        public static IServiceCollection AddInfrastructure(this IServiceCollection services, IConfiguration configuration)
        {
            // Register data access
            services.AddSingleton<IConnectionFactory, ConnectionFactory>();

            // Register repositories
            services.AddScoped<IUserRepository, UserRepository>();
            services.AddScoped<IRoleRepository, RoleRepository>();
            services.AddScoped<IAuthRepository, AuthRepository>();
            services.AddScoped<IPermissionRepository, PermissionRepository>();
            services.AddScoped<IRolePermissionRepository, RolePermissionRepository>();
            services.AddScoped<ICandidateRepository, CandidateRepository>();
            services.AddScoped<ICollaboratorRepository, CollaboratorRepository>();
            services.AddScoped<ICollaboratorLevelRepository, CollaboratorLevelRepository>();
            services.AddScoped<ICollaboratorRewardRepository, CollaboratorRewardRepository>();
            services.AddScoped<IRecruitmentPostingRepository, RecruitmentPostingRepository>();
            services.AddScoped<IDepartmentRepository, DepartmentRepository>();
            services.AddScoped<IPositionRepository, PositionRepository>();
            services.AddScoped<ICandidateApplicationRepository, CandidateApplicationRepository>();
            services.AddScoped<IContractRepository, ContractRepository>();
            services.AddScoped<ICollaboratorViolationRepository, CollaboratorViolationRepository>();
            services.AddScoped<ICollaboratorKpiRepository, CollaboratorKpiRepository>();
            services.AddScoped<ICollaboratorKpiTargetRepository, CollaboratorKpiTargetRepository>();
            services.AddScoped<ICollaboratorReportRepository, CollaboratorReportRepository>();
            services.AddScoped<ICompanyRepository, CompanyRepository>();
            services.AddScoped<IBranchRepository, BranchRepository>();
            services.AddScoped<IUserLogs, UserLogsRepository>();
            services.AddScoped<IAuditLogRepository, AuditLogRepository>();
            services.AddScoped<ICandidateDocumentRepository, CandidateDocumentRepository>();
            services.AddScoped<IObjectDataRepository, ObjectDataRepository>();
            services.AddScoped<IPayrollRepository, PayrollRepository>();
            services.AddScoped<IPayslipRepository, PayslipRepository>();
            services.AddScoped<IAddressRepository, AddressRepository>();


            // Register services
            services.AddScoped<IAuthService, AuthService>();
            services.AddScoped<IUserService, UserService>();
            services.AddScoped<IRoleService, RoleService>();
            services.AddScoped<IPermissionService, PermissionService>();
            services.AddScoped<IJwtService, JwtService>();
            services.AddScoped<IExternalAuthService, ExternalAuthService>();
            services.AddScoped<IEmailService, EmailService>();
            services.AddScoped<IEmailTemplateService, EmailTemplateService>();
            services.AddScoped<ICandidateService, CandidateService>();
            services.AddScoped<ICollaboratorService, CollaboratorService>();
            services.AddScoped<ICollaboratorLevelService, CollaboratorLevelService>();
            services.AddScoped<ICollaboratorRewardService, CollaboratorRewardService>();
            services.AddScoped<IRecruitmentPostingService, RecruitmentPostingService>();
            services.AddScoped<IDepartmentService, DepartmentService>();
            services.AddScoped<IPositionService, PositionService>();
            services.AddScoped<ICandidateApplicationService, CandidateApplicationService>();
            services.AddScoped<IContractService, ContractService>();
            services.AddScoped<ICollaboratorViolationService, CollaboratorViolationService>();
            services.AddScoped<ICollaboratorKpiService, CollaboratorKpiService>();
            services.AddScoped<ICollaboratorKpiTargetService, CollaboratorKpiTargetService>();
            services.AddScoped<ICollaboratorReportService, CollaboratorReportService>();
            services.AddScoped<ICollaboratorDashboardService, CollaboratorDashboardService>();
            services.AddScoped<ICompanyService, CompanyService>();
            services.AddScoped<IBranchService, BranchService>();
            services.AddScoped<ICandidateDocumentService, CandidateDocumentService>();
            services.AddScoped<IObjectDataService, ObjectDataService>();
            services.AddScoped<IActivityLogService, ActivityLogService>();
            services.AddScoped<IPayrollService,PayrollService>();
            services.AddScoped<IPayslipService, PayslipService>();
            services.AddScoped<IAddressService, AddressService>();

            // Register file storage services
            services.Configure<FileStorageOptions>(configuration.GetSection("FileStorage"));
            services.AddScoped<LocalFileStorageService>();
            services.AddScoped<MinioFileStorageService>();
            services.AddScoped<IFileStorageService, FileStorageService>();
            services.AddSingleton<FileExtensionService>();

            // Add HttpClient for CV scanning
            services.AddHttpClient<CvScannerService>();
            services.AddScoped<CvScannerService>();
            // Register Firebase notification services
            services.Configure<FirebaseConfig>(configuration.GetSection("Firebase"));
            services.AddScoped<IDeviceTokenRepository, DeviceTokenRepository>();
            services.AddScoped<INotificationService, FirebaseNotificationService>();

            // Add HttpContextAccessor for accessing HTTP context in services
            services.AddHttpContextAccessor();

            return services;
        }

        public static IServiceCollection AddInfrastructureExtensions(this IServiceCollection services, IConfiguration configuration)
        {
            // Add AutoMapper
            services.AddAutoMapperProfiles();

            // Add JWT Authentication
            services.AddJwtAuthentication(configuration);

            // Add CORS
            services.AddCorsPolicy();

            // Add Swagger
            services.AddSwaggerDocumentation();

            return services;
        }

        public static IApplicationBuilder UseInfrastructureMiddlewares(this IApplicationBuilder app)
        {
            // Use Swagger
            app.UseSwagger();
            app.UseSwaggerUI();

            // Use CORS
            app.UseCors("AllowAll");

            // Use Exception Handling
            app.UseExceptionHandling();
            
            // Use Request Logging
            app.UseRequestLogging();

            return app;
        }
    }
}
