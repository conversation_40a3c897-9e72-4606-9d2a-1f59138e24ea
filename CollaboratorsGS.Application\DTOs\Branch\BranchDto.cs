namespace CollaboratorsGS.Application.DTOs.Branch
{
    public class BranchDto
    {
        public Guid BranchId { get; set; }
        public Guid CompanyId { get; set; }
        public string BranchName { get; set; } = string.Empty;
        public string? PhoneNumber { get; set; }
        public string? Email { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime? UpdatedAt { get; set; }
        public string? CompanyName { get; set; }
    }
}
