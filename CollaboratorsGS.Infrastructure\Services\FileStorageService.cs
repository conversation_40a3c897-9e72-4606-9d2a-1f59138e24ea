using CollaboratorsGS.Application.Interfaces;
using CollaboratorsGS.Infrastructure.Configurations;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Options;

namespace CollaboratorsGS.Infrastructure.Services
{
    public class FileStorageService : IFileStorageService
    {
        private readonly IFileStorageService _storageService;

        public FileStorageService(
            IOptions<FileStorageOptions> options,
            LocalFileStorageService localFileStorageService,
            MinioFileStorageService minioFileStorageService)
        {
            _storageService = options.Value.StorageType.ToLower() == "minio"
                ? minioFileStorageService
                : localFileStorageService;
        }

        public Task<string> UploadFileAsync(IFormFile file, string? fileName = null)
        {
            return _storageService.UploadFileAsync(file, fileName);
        }

        public Task<bool> DeleteFileAsync(string fileUrl)
        {
            return _storageService.DeleteFileAsync(fileUrl);
        }

        public Task<string> GetTemporaryUrlAsync(string fileUrl, int expiryMinutes = 60)
        {
            return _storageService.GetTemporaryUrlAsync(fileUrl, expiryMinutes);
        }
    }
}
