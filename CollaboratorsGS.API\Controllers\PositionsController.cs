using CollaboratorsGS.Application.Constants;
using CollaboratorsGS.Application.DTOs;
using CollaboratorsGS.Application.DTOs.Position;
using CollaboratorsGS.Application.Interfaces;
using CollaboratorsGS.Infrastructure.Utilities;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace CollaboratorsGS.API.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    [Authorize]
    public class PositionsController : ControllerBase
    {
        private readonly IPositionService _positionService;
        private readonly ILogger<PositionsController> _logger;

        public PositionsController(
            IPositionService positionService,
            ILogger<PositionsController> logger)
        {
            _positionService = positionService;
            _logger = logger;
        }

        // GET: api/Positions
        [HttpGet]
        public async Task<IActionResult> GetAll()
        {
            try
            {
                var positions = await _positionService.GetAllAsync();
                return Ok(ApiResponse<object>.SuccessResponse(
                    MessageCodes.SC2000,
                    "Get all positions successfully",
                    positions));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting all positions");
                return StatusCode(500, ApiResponse<object>.ErrorResponse(
                    MessageCodes.ER5000,
                    "Internal server error",
                    500));
            }
        }

        // GET: api/Positions/{id}
        [HttpGet("{id}")]
        public async Task<IActionResult> GetById(Guid id)
        {
            try
            {
                var position = await _positionService.GetByIdAsync(id);

                if (position == null)
                    return NotFound(ApiResponse<object>.ErrorResponse(
                        MessageCodes.ER4004,
                        "Position not found",
                        404));

                return Ok(ApiResponse<object>.SuccessResponse(
                    MessageCodes.SC2000,
                    "Get position by id successfully",
                    position));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting position with ID {PositionId}", id);
                return StatusCode(500, ApiResponse<object>.ErrorResponse(
                    MessageCodes.ER5000,
                    "Internal server error",
                    500));
            }
        }

        // GET: api/Positions/department/{departmentId}
        [HttpGet("department/{departmentId}")]
        public async Task<IActionResult> GetByDepartment(Guid departmentId)
        {
            try
            {
                var positions = await _positionService.GetByDepartmentAsync(departmentId);
                return Ok(ApiResponse<object>.SuccessResponse(
                    MessageCodes.SC2000,
                    "Get positions by department successfully",
                    positions));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting positions for department {DepartmentId}", departmentId);
                return StatusCode(500, ApiResponse<object>.ErrorResponse(
                    MessageCodes.ER5000,
                    "Internal server error",
                    500));
            }
        }

        // POST: api/Positions
        [HttpPost]
        [Authorize(Roles = RolesUser.AdminManager)]
        public async Task<IActionResult> Create([FromBody] CreatePositionRequest request)
        {
            try
            {
                var positionId = await _positionService.CreatePositionAsync(request);
                var createdPosition = await _positionService.GetCreatedPositionAsync(positionId);

                if (createdPosition == null)
                    return StatusCode(500, ApiResponse<object>.ErrorResponse(
                        MessageCodes.ER5000,
                        "Failed to retrieve created position",
                        500));

                return CreatedAtAction(nameof(GetById), new { id = positionId },
                ApiResponse<object>.SuccessResponse(
                        MessageCodes.SC2001,
                        "Position created successfully",
                        createdPosition,
                        201));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating position");
                return StatusCode(500, ApiResponse<object>.ErrorResponse(
                    MessageCodes.ER5000,
                    "Internal server error",
                    500));
            }
        }

        // PUT: api/Positions/{id}
        [HttpPut("{id}")]
        [Authorize(Roles = RolesUser.AdminManager)]
        public async Task<IActionResult> Update(Guid id, [FromBody] UpdatePositionRequest request)
        {
            try
            {
                if (id != request.PositionId)
                    return NotFound(ApiResponse<object>.ErrorResponse(
                        MessageCodes.ER4004,
                        "Position not found",
                        404));

                var updatedPosition = await _positionService.UpdatePositionAsync(request);

                if (updatedPosition == null)
                    return NotFound(ApiResponse<object>.ErrorResponse(
                        MessageCodes.ER4004,
                        "Position not found",
                        404));

                return Ok(ApiResponse<object>.SuccessResponse(
                    MessageCodes.SC2002,
                    "Position updated successfully",
                    updatedPosition));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating position with ID {PositionId}", id);
                return StatusCode(500, ApiResponse<object>.ErrorResponse(
                    MessageCodes.ER5000,
                    "Internal server error",
                    500));
            }
        }

        // DELETE: api/Positions/{id}
        [HttpDelete("{id}")]
        [Authorize(Roles = RolesUser.Admin)]
        public async Task<IActionResult> Delete(Guid id)
        {
            try
            {
                var result = await _positionService.DeletePositionAsync(id);

                if (!result)
                    return NotFound(ApiResponse<object>.ErrorResponse(
                        MessageCodes.ER4004,
                        "Position not found",
                        404));

                return Ok(ApiResponse<object>.SuccessResponse(
                    MessageCodes.SC2003,
                    "Position deleted successfully",
                    true));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting position with ID {PositionId}", id);
                return StatusCode(500, ApiResponse<object>.ErrorResponse(
                    MessageCodes.ER5000,
                    "Internal server error",
                    500));
            }
        }
    }
}
