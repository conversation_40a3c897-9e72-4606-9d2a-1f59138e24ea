using System;
using System.ComponentModel.DataAnnotations;

namespace CollaboratorsGS.Application.DTOs.CollaboratorProfile
{
    public class UpdateCollaboratorProfileRequest
    {
        [Required]
        public Guid CtvId { get; set; }
        
        public string? CitizenId { get; set; }
        
        public string? CitizenIdFront { get; set; }
        
        public string? CitizenIdBack { get; set; }
        
        public string? PermanentAddress { get; set; }
        
        public string? CurrentAddress { get; set; }
        
        public string? BankName { get; set; }
        
        public string? BankAccountNumber { get; set; }
    }
}
