using AutoMapper;
using CollaboratorsGS.Application.DTOs.CollaboratorKpiTarget;
using CollaboratorsGS.Application.Interfaces;
using CollaboratorsGS.Domain.Entities;
using CollaboratorsGS.Domain.Repositories;

namespace CollaboratorsGS.Infrastructure.Services
{
    public class CollaboratorKpiTargetService : ICollaboratorKpiTargetService
    {
        private readonly ICollaboratorKpiTargetRepository _collaboratorKpiTargetRepository;
        private readonly ICollaboratorRepository _collaboratorRepository;
        private readonly IMapper _mapper;

        public CollaboratorKpiTargetService(
            ICollaboratorKpiTargetRepository collaboratorKpiTargetRepository,
            ICollaboratorRepository collaboratorRepository,
            IMapper mapper)
        {
            _collaboratorKpiTargetRepository = collaboratorKpiTargetRepository;
            _collaboratorRepository = collaboratorRepository;
            _mapper = mapper;
        }

        public async Task<CollaboratorKpiTargetDto?> GetByIdAsync(Guid targetId)
        {
            var target = await _collaboratorKpiTargetRepository.GetByIdAsync(targetId);
            return _mapper.Map<CollaboratorKpiTargetDto>(target);
        }

        public async Task<IEnumerable<CollaboratorKpiTargetDto>> GetAllAsync()
        {
            var targets = await _collaboratorKpiTargetRepository.GetAllAsync();
            return _mapper.Map<IEnumerable<CollaboratorKpiTargetDto>>(targets);
        }

        public async Task<IEnumerable<CollaboratorKpiTargetDto>> GetByCollaboratorIdAsync(Guid collaboratorId)
        {
            var targets = await _collaboratorKpiTargetRepository.GetByCollaboratorIdAsync(collaboratorId);
            return _mapper.Map<IEnumerable<CollaboratorKpiTargetDto>>(targets);
        }

        public async Task<IEnumerable<CollaboratorKpiTargetDto>> GetByPeriodAsync(string period)
        {
            var targets = await _collaboratorKpiTargetRepository.GetByPeriodAsync(period);
            return _mapper.Map<IEnumerable<CollaboratorKpiTargetDto>>(targets);
        }

        public async Task<Guid> CreateCollaboratorKpiTargetAsync(CreateCollaboratorKpiTargetRequest request)
        {
            // Verify collaborator exists
            var collaborator = await _collaboratorRepository.GetByIdAsync(request.CollaboratorId);
            if (collaborator == null)
            {
                throw new InvalidOperationException($"Collaborator with ID {request.CollaboratorId} not found");
            }

            // Map request to entity
            var target = new CollaboratorKpiTarget
            {
                CollaboratorId = request.CollaboratorId,
                Period = request.Period,
                TargetCandidatesImported = request.TargetCandidatesImported,
                TargetCandidatesPassedRound1 = request.TargetCandidatesPassedRound1,
                TargetCandidatesOnboarded = request.TargetCandidatesOnboarded,
                CollaboratorName = collaborator.FullName
            };

            // Create target
            return await _collaboratorKpiTargetRepository.CreateAsync(target);
        }

        public async Task<CollaboratorKpiTargetDto?> GetCreatedCollaboratorKpiTargetAsync(Guid targetId)
        {
            return await GetByIdAsync(targetId);
        }

        public async Task<CollaboratorKpiTargetDto?> UpdateCollaboratorKpiTargetAsync(UpdateCollaboratorKpiTargetRequest request)
        {
            // Check if target exists
            var existingTarget = await _collaboratorKpiTargetRepository.GetByIdAsync(request.TargetId);
            if (existingTarget == null)
            {
                throw new InvalidOperationException($"KPI Target with ID {request.TargetId} not found");
            }

            // Map request to entity
            var target = new CollaboratorKpiTarget
            {
                TargetId = request.TargetId,
                CollaboratorId = existingTarget.CollaboratorId,
                Period = existingTarget.Period,
                TargetCandidatesImported = request.TargetCandidatesImported,
                TargetCandidatesPassedRound1 = request.TargetCandidatesPassedRound1,
                TargetCandidatesOnboarded = request.TargetCandidatesOnboarded,
                CreatedAt = existingTarget.CreatedAt,
                CollaboratorName = existingTarget.CollaboratorName
            };

            // Update target
            var result = await _collaboratorKpiTargetRepository.UpdateAsync(target);

            if (!result)
                return null;

            // Get the updated target
            return await GetByIdAsync(request.TargetId);
        }

        public async Task<bool> DeleteCollaboratorKpiTargetAsync(Guid targetId)
        {
            // Check if target exists
            var existingTarget = await _collaboratorKpiTargetRepository.GetByIdAsync(targetId);
            if (existingTarget == null)
            {
                throw new InvalidOperationException($"KPI Target with ID {targetId} not found");
            }

            // Delete target
            return await _collaboratorKpiTargetRepository.DeleteAsync(targetId);
        }
    }
}
