using System;
using System.ComponentModel.DataAnnotations;

namespace CollaboratorsGS.Application.DTOs.CollaboratorReward
{
    public class UpdateCollaboratorRewardRequest
    {
        [Required]
        public Guid RewardId { get; set; }
        
        [Range(0.01, double.MaxValue, ErrorMessage = "Amount must be greater than 0")]
        public decimal Amount { get; set; }
        
        public DateTime ScheduledPaymentDate { get; set; }
        
        [StringLength(20, ErrorMessage = "Status cannot exceed 20 characters")]
        public string Status { get; set; } = string.Empty;
    }
}
