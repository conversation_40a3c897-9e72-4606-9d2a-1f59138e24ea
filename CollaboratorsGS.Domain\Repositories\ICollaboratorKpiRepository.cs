using CollaboratorsGS.Domain.Entities;

namespace CollaboratorsGS.Domain.Repositories
{
    public interface ICollaboratorKpiRepository
    {
        Task<CollaboratorKpi?> GetByIdAsync(Guid kpiId);
        Task<IEnumerable<CollaboratorKpi>> GetAllAsync();
        Task<IEnumerable<CollaboratorKpi>> GetByCollaboratorIdAsync(Guid collaboratorId);
        Task<IEnumerable<CollaboratorKpi>> GetByPeriodAsync(string period);
        Task<Guid> CreateAsync(CollaboratorKpi kpi);
        Task<bool> UpdateAsync(CollaboratorKpi kpi);
        Task<bool> DeleteAsync(Guid kpiId);

        Task<IEnumerable<CollaboratorKpi>> GetKpiSummaryByCollaboratorIdAsync(Guid collaboratorId);
        Task<object> GetKpiDetailByUserIdAsync(Guid userId);
    }
}
