-- Stored procedure to create/update collaborator profile from extraction data
-- This procedure handles the exact JSON structure from extraction API

IF EXISTS (SELECT * FROM sys.objects WHERE type = 'P' AND name = 'sp_CreateCollaboratorProfileFromExtraction')
    DROP PROCEDURE sp_CreateCollaboratorProfileFromExtraction
GO

CREATE PROCEDURE sp_CreateCollaboratorProfileFromExtraction
    @CollaboratorId UNIQUEIDENTIFIER,
    @CitizenIdExtracted VARCHAR(50),
    @FullNameExtracted NVARCHAR(255),
    @DateOfBirthString VARCHAR(20), -- Input as "13/01/2001"
    @GenderExtracted NVARCHAR(10),
    @AddressExtracted NVARCHAR(MAX),
    @PersonalIdentificationExtracted NVARCHAR(500),
    @IdIssueDateString VARCHAR(20), -- Input as "12/02/2023"
    @IdIssueAuthorityExtracted NVARCHAR(500),
    @ExtractionRawData NVARCHAR(MAX)
AS
BEGIN
    SET NOCOUNT ON;
    
    DECLARE @DateOfBirthExtracted DATE = NULL;
    DECLARE @IdIssueDateExtracted DATE = NULL;
    DECLARE @ExtractionTimestamp DATETIME = GETDATE();
    DECLARE @ProfileId UNIQUEIDENTIFIER = NEWID();
    DECLARE @ErrorMessage NVARCHAR(500);
    
    -- Validate and parse date of birth
    IF @DateOfBirthString IS NOT NULL AND @DateOfBirthString != ''
    BEGIN
        BEGIN TRY
            -- Try to parse date in format "dd/MM/yyyy"
            SET @DateOfBirthExtracted = CONVERT(DATE, @DateOfBirthString, 103);
            
            -- Validate date range (must be between 1900 and current year - 10)
            IF @DateOfBirthExtracted < '1900-01-01' OR @DateOfBirthExtracted > DATEADD(YEAR, -10, GETDATE())
            BEGIN
                SET @ErrorMessage = 'Invalid date of birth: ' + @DateOfBirthString + '. Must be between 1900 and ' + CAST(YEAR(DATEADD(YEAR, -10, GETDATE())) AS VARCHAR(4));
                RAISERROR(@ErrorMessage, 16, 1);
                RETURN;
            END
        END TRY
        BEGIN CATCH
            SET @ErrorMessage = 'Invalid date of birth format: ' + @DateOfBirthString + '. Expected format: dd/MM/yyyy';
            RAISERROR(@ErrorMessage, 16, 1);
            RETURN;
        END CATCH
    END
    
    -- Validate and parse ID issue date
    IF @IdIssueDateString IS NOT NULL AND @IdIssueDateString != ''
    BEGIN
        BEGIN TRY
            -- Try to parse date in format "dd/MM/yyyy"
            SET @IdIssueDateExtracted = CONVERT(DATE, @IdIssueDateString, 103);
            
            -- Validate date range (must be between 1990 and current date + 1 year)
            IF @IdIssueDateExtracted < '1990-01-01' OR @IdIssueDateExtracted > DATEADD(YEAR, 1, GETDATE())
            BEGIN
                SET @ErrorMessage = 'Invalid ID issue date: ' + @IdIssueDateString + '. Must be between 1990 and ' + CAST(YEAR(DATEADD(YEAR, 1, GETDATE())) AS VARCHAR(4));
                RAISERROR(@ErrorMessage, 16, 1);
                RETURN;
            END
        END TRY
        BEGIN CATCH
            SET @ErrorMessage = 'Invalid ID issue date format: ' + @IdIssueDateString + '. Expected format: dd/MM/yyyy';
            RAISERROR(@ErrorMessage, 16, 1);
            RETURN;
        END CATCH
    END
    
    -- Validate collaborator exists
    IF NOT EXISTS (SELECT 1 FROM Collaborators WHERE collaborator_id = @CollaboratorId)
    BEGIN
        RAISERROR('Collaborator with ID %s does not exist', 16, 1, @CollaboratorId);
        RETURN;
    END
    
    -- Check if profile already exists
    IF EXISTS (SELECT 1 FROM collaborator_profiles WHERE collaborator_id = @CollaboratorId)
    BEGIN
        -- UPDATE existing profile with extraction data
        UPDATE collaborator_profiles
        SET citizen_id_extracted = @CitizenIdExtracted,
            full_name_extracted = @FullNameExtracted,
            date_of_birth_extracted = @DateOfBirthExtracted,
            gender_extracted = @GenderExtracted,
            address_extracted = @AddressExtracted,
            personal_identification_extracted = @PersonalIdentificationExtracted,
            id_issue_date_extracted = @IdIssueDateExtracted,
            id_issue_authority_extracted = @IdIssueAuthorityExtracted,
            extraction_raw_data = @ExtractionRawData,
            extraction_timestamp = @ExtractionTimestamp,
            data_source = 'extracted',
            updated_at = @ExtractionTimestamp
        WHERE collaborator_id = @CollaboratorId;
        
        SELECT 'UPDATED' as Action, @@ROWCOUNT as RowsAffected, 'Profile updated with extraction data' as Message;
    END
    ELSE
    BEGIN
        -- CREATE new profile with extraction data
        INSERT INTO collaborator_profiles (
            profile_id, collaborator_id,
            citizen_id_extracted, full_name_extracted, date_of_birth_extracted, gender_extracted,
            address_extracted, personal_identification_extracted, id_issue_date_extracted, id_issue_authority_extracted,
            extraction_raw_data, extraction_timestamp, data_source,
            created_at, updated_at
        )
        VALUES (
            @ProfileId, @CollaboratorId,
            @CitizenIdExtracted, @FullNameExtracted, @DateOfBirthExtracted, @GenderExtracted,
            @AddressExtracted, @PersonalIdentificationExtracted, @IdIssueDateExtracted, @IdIssueAuthorityExtracted,
            @ExtractionRawData, @ExtractionTimestamp, 'extracted',
            @ExtractionTimestamp, NULL
        );
        
        SELECT 'CREATED' as Action, @@ROWCOUNT as RowsAffected, 'Profile created with extraction data' as Message;
    END
END
GO

PRINT 'Created sp_CreateCollaboratorProfileFromExtraction stored procedure with date validation.';
