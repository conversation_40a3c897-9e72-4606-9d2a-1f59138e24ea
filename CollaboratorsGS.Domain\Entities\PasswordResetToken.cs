
namespace CollaboratorsGS.Domain.Entities
{
    public class PasswordResetToken
    {
        public Guid TokenId { get; set; }
        public Guid UserId { get; set; }
        public string Token { get; set; } = string.Empty;
        public DateTime ExpiresAt { get; set; }
        public DateTime CreatedAt { get; set; }
        // Navigation properties
        public User? User { get; set; }
    }
}