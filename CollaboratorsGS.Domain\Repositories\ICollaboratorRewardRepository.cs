using CollaboratorsGS.Domain.Entities;

namespace CollaboratorsGS.Domain.Repositories
{
    public interface ICollaboratorRewardRepository
    {
        Task<CollaboratorReward?> GetByIdAsync(Guid rewardId);
        Task<IEnumerable<CollaboratorReward>> GetAllAsync();
        Task<IEnumerable<CollaboratorReward>> GetByCollaboratorIdAsync(Guid collaboratorId);
        Task<IEnumerable<CollaboratorReward>> GetByStatusAsync(string status);
        Task<Guid> CreateAsync(CollaboratorReward reward);
        Task<bool> UpdateAsync(CollaboratorReward reward);
        Task<bool> DeleteAsync(Guid rewardId);
        Task<IEnumerable<CollaboratorRewardHistory>> GetHistoryByRewardIdAsync(Guid rewardId);
        Task<Guid> CreateHistoryAsync(CollaboratorRewardHistory history);

        // Unified Reward Operations Method
        Task<object> GetRewardOperationsAsync(Guid collaboratorId, int? month, int? year, string action);
    }
}
