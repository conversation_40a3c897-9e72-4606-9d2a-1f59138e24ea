using AutoMapper;
using CollaboratorsGS.Application.DTOs.CollaboratorReport;
using CollaboratorsGS.Application.Interfaces;
using CollaboratorsGS.Domain.Entities;
using CollaboratorsGS.Domain.Repositories;

namespace CollaboratorsGS.Infrastructure.Services
{
    public class CollaboratorReportService : ICollaboratorReportService
    {
        private readonly ICollaboratorReportRepository _collaboratorReportRepository;
        private readonly ICollaboratorRepository _collaboratorRepository;
        private readonly IMapper _mapper;

        public CollaboratorReportService(
            ICollaboratorReportRepository collaboratorReportRepository,
            ICollaboratorRepository collaboratorRepository,
            IMapper mapper)
        {
            _collaboratorReportRepository = collaboratorReportRepository;
            _collaboratorRepository = collaboratorRepository;
            _mapper = mapper;
        }

        public async Task<CollaboratorReportDto?> GetByIdAsync(Guid reportId)
        {
            var report = await _collaboratorReportRepository.GetByIdAsync(reportId);
            return _mapper.Map<CollaboratorReportDto>(report);
        }

        public async Task<IEnumerable<CollaboratorReportDto>> GetAllAsync()
        {
            var reports = await _collaboratorReportRepository.GetAllAsync();
            return _mapper.Map<IEnumerable<CollaboratorReportDto>>(reports);
        }

        public async Task<IEnumerable<CollaboratorReportDto>> GetByCollaboratorIdAsync(Guid collaboratorId)
        {
            var reports = await _collaboratorReportRepository.GetByCollaboratorIdAsync(collaboratorId);
            return _mapper.Map<IEnumerable<CollaboratorReportDto>>(reports);
        }

        public async Task<IEnumerable<CollaboratorReportDto>> GetByPeriodAsync(string period)
        {
            var reports = await _collaboratorReportRepository.GetByPeriodAsync(period);
            return _mapper.Map<IEnumerable<CollaboratorReportDto>>(reports);
        }

        public async Task<Guid> CreateCollaboratorReportAsync(CreateCollaboratorReportRequest request)
        {
            // Verify collaborator exists
            var collaborator = await _collaboratorRepository.GetByIdAsync(request.CollaboratorId);
            if (collaborator == null)
            {
                throw new InvalidOperationException($"Collaborator with ID {request.CollaboratorId} not found");
            }

            // Map request to entity
            var report = new CollaboratorReport
            {
                CollaboratorId = request.CollaboratorId,
                ReportPeriod = request.ReportPeriod,
                TotalCandidates = request.TotalCandidates,
                TotalPayment = request.TotalPayment,
                ReportDate = DateTime.UtcNow,
                Data = request.Data,
                CollaboratorName = collaborator.FullName
            };

            // Create report
            return await _collaboratorReportRepository.CreateAsync(report);
        }

        public async Task<CollaboratorReportDto?> GetCreatedCollaboratorReportAsync(Guid reportId)
        {
            return await GetByIdAsync(reportId);
        }

        public async Task<CollaboratorReportDto?> UpdateCollaboratorReportAsync(UpdateCollaboratorReportRequest request)
        {
            // Check if report exists
            var existingReport = await _collaboratorReportRepository.GetByIdAsync(request.ReportId);
            if (existingReport == null)
            {
                throw new InvalidOperationException($"Report with ID {request.ReportId} not found");
            }

            // Map request to entity
            var report = new CollaboratorReport
            {
                ReportId = request.ReportId,
                CollaboratorId = existingReport.CollaboratorId,
                ReportPeriod = existingReport.ReportPeriod,
                TotalCandidates = request.TotalCandidates,
                TotalPayment = request.TotalPayment,
                ReportDate = existingReport.ReportDate,
                Data = request.Data,
                CollaboratorName = existingReport.CollaboratorName
            };

            // Update report
            var result = await _collaboratorReportRepository.UpdateAsync(report);

            if (!result)
                return null;

            // Get the updated report
            return await GetByIdAsync(request.ReportId);
        }

        public async Task<bool> DeleteCollaboratorReportAsync(Guid reportId)
        {
            // Check if report exists
            var existingReport = await _collaboratorReportRepository.GetByIdAsync(reportId);
            if (existingReport == null)
            {
                throw new InvalidOperationException($"Report with ID {reportId} not found");
            }

            // Delete report
            return await _collaboratorReportRepository.DeleteAsync(reportId);
        }

        public async Task<bool> GenerateReportAsync(Guid collaboratorId, string period)
        {
            // Verify collaborator exists
            var collaborator = await _collaboratorRepository.GetByIdAsync(collaboratorId);
            if (collaborator == null)
            {
                throw new InvalidOperationException($"Collaborator with ID {collaboratorId} not found");
            }

            // TODO: Implement report generation logic
            // This would typically involve:
            // 1. Gathering data from various sources (KPIs, rewards, etc.)
            // 2. Calculating totals and statistics
            // 3. Creating the report entity
            // 4. Saving to database

            return true; // Placeholder
        }
    }
}
