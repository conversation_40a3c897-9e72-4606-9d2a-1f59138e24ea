using CollaboratorsGS.Application.DTOs.Company;

namespace CollaboratorsGS.Application.Interfaces
{
    public interface ICompanyService
    {
        Task<CompanyDto?> GetByIdAsync(Guid companyId);
        Task<IEnumerable<CompanyDto>> GetAllAsync();
        Task<CompanyDto> CreateCompanyAsync(CreateCompanyRequest request);
        Task<CompanyDto?> UpdateCompanyAsync(Guid companyId, UpdateCompanyRequest request);
        Task<bool> DeleteCompanyAsync(Guid companyId);
    }
}
