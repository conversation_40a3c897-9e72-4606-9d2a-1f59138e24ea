using System;
using CollaboratorsGS.Domain.Enums;

namespace CollaboratorsGS.Application.DTOs.RecruitmentPosting
{
    public class RecruitmentPostingDto
    {
        public Guid posting_id { get; set; }
        public string refer_code { get; set; } = string.Empty;
        public string title { get; set; } = string.Empty;

        public string? project { get; set; }
        public string? level { get; set; }
        public string? position { get; set; } // ObjectCode from ObjectData

        public int? salary_from { get; set; }
        public int? salary_to { get; set; }
        public int? commission { get; set; }
        public int? commission_warranty_months { get; set; }

        public string? working_location { get; set; }
        public string? working_time { get; set; }

        public int view_count { get; set; }
        public int referral_count { get; set; }

        public bool is_urgent { get; set; }
        public bool is_hot { get; set; }
        public bool is_saved { get; set; }

        public RecruitmentPostingStatus status { get; set; }

        public DateTime created_at { get; set; }
        public DateTime? updated_at { get; set; }
        public DateTime? expired_at { get; set; }
    }
}
