namespace CollaboratorsGS.Application.DTOs.CollaboratorLevel
{
    public class CreateCollaboratorLevelRequest
    {
        public string LevelName { get; set; } = string.Empty;
        public float? MinKpiThreshold { get; set; }
        public float? CommissionRate { get; set; }
        public decimal? Round1Bonus { get; set; }
        public decimal? Round2Bonus { get; set; }
        public decimal? OnboardBonus { get; set; }
        public string? Description { get; set; }
    }
}
