using CollaboratorsGS.Domain.Entities;

namespace CollaboratorsGS.Domain.Repositories
{
    public interface IRolePermissionRepository
    {
        Task<IEnumerable<RolePermission>> GetByRoleIdAsync(Guid roleId);
        Task<IEnumerable<RolePermission>> GetByPermissionIdAsync(Guid permissionId);
        Task<bool> AssignPermissionToRoleAsync(Guid roleId, Guid permissionId);
        Task<bool> RemovePermissionFromRoleAsync(Guid roleId, Guid permissionId);
        Task<bool> RemoveAllPermissionsFromRoleAsync(Guid roleId);
    }
}
