-- Seed Permissions with <PERSON>UI<PERSON>
IF NOT EXISTS (SELECT TOP 1 1 FROM Permissions)
BEGIN
    PRINT 'Seeding Permissions with UUID...';

    -- User Management Permissions
    INSERT INTO Permissions (permission_id, permission_name, description) VALUES (NEWID(), 'users.view', 'View users');
    INSERT INTO Permissions (permission_id, permission_name, description) VALUES (NEWID(), 'users.create', 'Create users');
    INSERT INTO Permissions (permission_id, permission_name, description) VALUES (NEWID(), 'users.edit', 'Edit users');
    INSERT INTO Permissions (permission_id, permission_name, description) VALUES (NEWID(), 'users.delete', 'Delete users');
    
    -- Role Management Permissions
    INSERT INTO Permissions (permission_id, permission_name, description) VALUES (NEWID(), 'roles.view', 'View roles');
    INSERT INTO Permissions (permission_id, permission_name, description) VALUES (NEWID(), 'roles.create', 'Create roles');
    INSERT INTO Permissions (permission_id, permission_name, description) VALUES (NEWID(), 'roles.edit', 'Edit roles');
    INSERT INTO Permissions (permission_id, permission_name, description) VALUES (NEWID(), 'roles.delete', 'Delete roles');
    
    -- Permission Management Permissions
    INSERT INTO Permissions (permission_id, permission_name, description) VALUES (NEWID(), 'permissions.view', 'View permissions');
    INSERT INTO Permissions (permission_id, permission_name, description) VALUES (NEWID(), 'permissions.create', 'Create permissions');
    INSERT INTO Permissions (permission_id, permission_name, description) VALUES (NEWID(), 'permissions.edit', 'Edit permissions');
    INSERT INTO Permissions (permission_id, permission_name, description) VALUES (NEWID(), 'permissions.delete', 'Delete permissions');
    
    -- Collaborator Management Permissions
    INSERT INTO Permissions (permission_id, permission_name, description) VALUES (NEWID(), 'collaborators.view', 'View collaborators');
    INSERT INTO Permissions (permission_id, permission_name, description) VALUES (NEWID(), 'collaborators.create', 'Create collaborators');
    INSERT INTO Permissions (permission_id, permission_name, description) VALUES (NEWID(), 'collaborators.edit', 'Edit collaborators');
    INSERT INTO Permissions (permission_id, permission_name, description) VALUES (NEWID(), 'collaborators.delete', 'Delete collaborators');
    INSERT INTO Permissions (permission_id, permission_name, description) VALUES (NEWID(), 'collaborators.approve', 'Approve collaborators');
    
    -- Candidate Management Permissions
    INSERT INTO Permissions (permission_id, permission_name, description) VALUES (NEWID(), 'candidates.view', 'View candidates');
    INSERT INTO Permissions (permission_id, permission_name, description) VALUES (NEWID(), 'candidates.create', 'Create candidates');
    INSERT INTO Permissions (permission_id, permission_name, description) VALUES (NEWID(), 'candidates.edit', 'Edit candidates');
    INSERT INTO Permissions (permission_id, permission_name, description) VALUES (NEWID(), 'candidates.delete', 'Delete candidates');
    
    -- Recruitment Management Permissions
    INSERT INTO Permissions (permission_id, permission_name, description) VALUES (NEWID(), 'recruitments.view', 'View recruitment postings');
    INSERT INTO Permissions (permission_id, permission_name, description) VALUES (NEWID(), 'recruitments.create', 'Create recruitment postings');
    INSERT INTO Permissions (permission_id, permission_name, description) VALUES (NEWID(), 'recruitments.edit', 'Edit recruitment postings');
    INSERT INTO Permissions (permission_id, permission_name, description) VALUES (NEWID(), 'recruitments.delete', 'Delete recruitment postings');
    
    -- Application Management Permissions
    INSERT INTO Permissions (permission_id, permission_name, description) VALUES (NEWID(), 'applications.view', 'View applications');
    INSERT INTO Permissions (permission_id, permission_name, description) VALUES (NEWID(), 'applications.create', 'Create applications');
    INSERT INTO Permissions (permission_id, permission_name, description) VALUES (NEWID(), 'applications.edit', 'Edit applications');
    INSERT INTO Permissions (permission_id, permission_name, description) VALUES (NEWID(), 'applications.delete', 'Delete applications');
    INSERT INTO Permissions (permission_id, permission_name, description) VALUES (NEWID(), 'applications.process', 'Process applications (change status)');
    
    -- Reward Management Permissions
    INSERT INTO Permissions (permission_id, permission_name, description) VALUES (NEWID(), 'rewards.view', 'View rewards');
    INSERT INTO Permissions (permission_id, permission_name, description) VALUES (NEWID(), 'rewards.create', 'Create rewards');
    INSERT INTO Permissions (permission_id, permission_name, description) VALUES (NEWID(), 'rewards.edit', 'Edit rewards');
    INSERT INTO Permissions (permission_id, permission_name, description) VALUES (NEWID(), 'rewards.delete', 'Delete rewards');
    INSERT INTO Permissions (permission_id, permission_name, description) VALUES (NEWID(), 'rewards.approve', 'Approve rewards');
    
    -- KPI Management Permissions
    INSERT INTO Permissions (permission_id, permission_name, description) VALUES (NEWID(), 'kpis.view', 'View KPIs');
    INSERT INTO Permissions (permission_id, permission_name, description) VALUES (NEWID(), 'kpis.create', 'Create KPIs');
    INSERT INTO Permissions (permission_id, permission_name, description) VALUES (NEWID(), 'kpis.edit', 'Edit KPIs');
    
    -- Report Management Permissions
    INSERT INTO Permissions (permission_id, permission_name, description) VALUES (NEWID(), 'reports.view', 'View reports');
    INSERT INTO Permissions (permission_id, permission_name, description) VALUES (NEWID(), 'reports.create', 'Create reports');
    
    -- System Settings Permissions
    INSERT INTO Permissions (permission_id, permission_name, description) VALUES (NEWID(), 'settings.view', 'View system settings');
    INSERT INTO Permissions (permission_id, permission_name, description) VALUES (NEWID(), 'settings.edit', 'Edit system settings');
    
    PRINT 'Permissions seeded successfully.';
END
ELSE
BEGIN
    PRINT 'Permissions already exist. Skipping...';
END
GO
