using CollaboratorsGS.Application.DTOs.ObjectData;

namespace CollaboratorsGS.Application.Interfaces
{
    public interface IObjectDataService
    {
        Task<ObjectDataDto?> GetByIdAsync(Guid objectId); // Changed from int to Guid
        Task<IEnumerable<ObjectDataDto>> GetAllAsync();
        Task<IEnumerable<ObjectDataDto>> GetByTypeAsync(string objectType);
        Task<ObjectDataDto?> GetByCodeAsync(string objectCode);
        Task<IEnumerable<ObjectDataDto>> GetByValueAsync(string objectValue);
        Task<Guid> CreateAsync(CreateObjectDataRequest request); // Changed return type from int to Guid
        Task<bool> UpdateAsync(Guid objectId, UpdateObjectDataRequest request); // Changed from int to Guid
        Task<bool> DeleteAsync(Guid objectId); // Changed from int to Guid
    }
}
