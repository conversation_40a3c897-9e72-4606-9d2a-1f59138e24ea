using CollaboratorsGS.Domain.Entities;
using CollaboratorsGS.Domain.Repositories;
using CollaboratorsGS.Infrastructure.Data;
using Dapper;
using System.Data;

namespace CollaboratorsGS.Infrastructure.Repositories
{
    public class ObjectDataRepository : IObjectDataRepository
    {
        private readonly IConnectionFactory _connectionFactory;

        public ObjectDataRepository(IConnectionFactory connectionFactory)
        {
            _connectionFactory = connectionFactory;
        }

        public async Task<ObjectData?> GetByIdAsync(Guid objectId)
        {
            using var connection = _connectionFactory.CreateConnection();

            var parameters = new DynamicParameters();
            parameters.Add("@object_id", objectId, DbType.Guid);

            return await connection.QuerySingleOrDefaultAsync<ObjectData>(
                "sp_GetObjectDataById",
                parameters,
                commandType: CommandType.StoredProcedure);
        }

        public async Task<IEnumerable<ObjectData>> GetAllAsync()
        {
            using var connection = _connectionFactory.CreateConnection();

            return await connection.QueryAsync<ObjectData>(
                "sp_GetAllObjectData",
                commandType: CommandType.StoredProcedure);
        }

        public async Task<IEnumerable<ObjectData>> GetByTypeAsync(string objectType)
        {
            using var connection = _connectionFactory.CreateConnection();

            var parameters = new DynamicParameters();
            parameters.Add("@object_type", objectType, DbType.String);

            return await connection.QueryAsync<ObjectData>(
                "sp_GetObjectDataByType",
                parameters,
                commandType: CommandType.StoredProcedure);
        }

        public async Task<ObjectData?> GetByCodeAsync(string objectCode)
        {
            using var connection = _connectionFactory.CreateConnection();

            var parameters = new DynamicParameters();
            parameters.Add("@object_code", objectCode, DbType.String);

            return await connection.QuerySingleOrDefaultAsync<ObjectData>(
                "sp_GetObjectDataByCode",
                parameters,
                commandType: CommandType.StoredProcedure);
        }

        public async Task<IEnumerable<ObjectData>> GetByValueAsync(string objectValue)
        {
            using var connection = _connectionFactory.CreateConnection();

            var parameters = new DynamicParameters();
            parameters.Add("@object_value", objectValue, DbType.String);

            return await connection.QueryAsync<ObjectData>(
                "sp_GetObjectDataByValue",
                parameters,
                commandType: CommandType.StoredProcedure);
        }

        public async Task<Guid> CreateAsync(ObjectData objectData)
        {
            if (objectData.ObjectId == Guid.Empty)
            {
                objectData.ObjectId = Guid.NewGuid();
            }

            using var connection = _connectionFactory.CreateConnection();

            var parameters = new DynamicParameters();
            parameters.Add("@object_id", objectData.ObjectId, DbType.Guid);
            parameters.Add("@object_type", objectData.ObjectType, DbType.String);
            parameters.Add("@object_code", objectData.ObjectCode, DbType.String);
            parameters.Add("@object_value", objectData.ObjectValue, DbType.String);
            parameters.Add("@description", objectData.Description, DbType.String);
            parameters.Add("@created_at", objectData.CreatedAt, DbType.DateTime);

            var result = await connection.QuerySingleAsync<Guid>(
                "sp_CreateObjectData",
                parameters,
                commandType: CommandType.StoredProcedure);

            return result;
        }

        public async Task<bool> UpdateAsync(ObjectData objectData)
        {
            using var connection = _connectionFactory.CreateConnection();

            var parameters = new DynamicParameters();
            parameters.Add("@object_id", objectData.ObjectId, DbType.Guid);
            parameters.Add("@object_type", objectData.ObjectType, DbType.String);
            parameters.Add("@object_code", objectData.ObjectCode, DbType.String);
            parameters.Add("@object_value", objectData.ObjectValue, DbType.String);
            parameters.Add("@description", objectData.Description, DbType.String);
            parameters.Add("@updated_at", objectData.UpdatedAt, DbType.DateTime);

            var rowsAffected = await connection.ExecuteAsync(
                "sp_UpdateObjectData",
                parameters,
                commandType: CommandType.StoredProcedure);

            return rowsAffected > 0;
        }

        public async Task<bool> DeleteAsync(Guid objectId)
        {
            using var connection = _connectionFactory.CreateConnection();

            var parameters = new DynamicParameters();
            parameters.Add("@object_id", objectId, DbType.Guid);

            var rowsAffected = await connection.ExecuteAsync(
                "sp_DeleteObjectData",
                parameters,
                commandType: CommandType.StoredProcedure);

            return rowsAffected > 0;
        }
    }
}
