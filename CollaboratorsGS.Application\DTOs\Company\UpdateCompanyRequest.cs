using System.ComponentModel.DataAnnotations;

namespace CollaboratorsGS.Application.DTOs.Company
{
    public class UpdateCompanyRequest
    {
        [Required]
        [StringLength(255)]
        public string CompanyName { get; set; } = string.Empty;

        [StringLength(20)]
        public string? PhoneNumber { get; set; }

        [EmailAddress]
        [StringLength(255)]
        public string? Email { get; set; }
    }
}
