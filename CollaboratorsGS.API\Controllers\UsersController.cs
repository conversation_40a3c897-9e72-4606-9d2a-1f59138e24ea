using CollaboratorsGS.Application.Constants;
using CollaboratorsGS.Application.DTOs;
using CollaboratorsGS.Application.Interfaces;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace CollaboratorsGS.API.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    [Authorize]
    public class UsersController : ControllerBase
    {
        private readonly IUserService _userService;

        public UsersController(IUserService userService)
        {
            _userService = userService;
        }

        [HttpGet]
        [Authorize(Roles = RolesUser.Admin)]
        public async Task<IActionResult> GetAll()
        {
            var users = await _userService.GetAllAsync();
            return Ok(users);
        }

        [HttpGet("{id}")]
        public async Task<IActionResult> GetById(Guid id)
        {
            var user = await _userService.GetByIdAsync(id);

            if (user == null)
                return NotFound();

            return Ok(user);
        }

        [HttpPost]
        [Authorize(Roles = RolesUser.Admin)]
        public async Task<IActionResult> Create([FromBody] CreateUserRequest request)
        {
            try
            {
                var userId = await _userService.CreateUserAsync(request);
                return CreatedAtAction(nameof(GetById), new { id = userId }, new { UserId = userId });
            }
            catch (InvalidOperationException ex)
            {
                return BadRequest(new { Message = ex.Message });
            }
        }

        [HttpPut("{id}")]
        [Authorize(Roles = RolesUser.Admin)]
        public async Task<IActionResult> Update(Guid id, [FromBody] UpdateUserRequest request)
        {
            try
            {
                var result = await _userService.UpdateUserAsync(id, request);

                if (!result)
                    return NotFound();

                return NoContent();
            }
            catch (InvalidOperationException ex)
            {
                return BadRequest(new { Message = ex.Message });
            }
        }

        [HttpDelete("{id}")]
        [Authorize(Roles = RolesUser.Admin)]
        public async Task<IActionResult> Delete(Guid id)
        {
            var result = await _userService.DeleteUserAsync(id);

            if (!result)
                return NotFound();

            return NoContent();
        }

        [HttpPost("change-password")]
        [Authorize]
        public async Task<IActionResult> ChangePassword([FromBody] ChangePasswordRequest request)
        {
            try
            {
                // Get userId from token claims
                var userIdClaim = User.FindFirst(System.Security.Claims.ClaimTypes.NameIdentifier)
                    ?? User.FindFirst("sub");

                if (userIdClaim == null || !Guid.TryParse(userIdClaim.Value, out var userId))
                {
                    return BadRequest(new { Message = "User ID not found in token or invalid" });
                }

                // Ensure the user can only change their own password (unless they are admin)
                if (userId != request.UserId && !User.IsInRole(RolesUser.Admin))
                {
                    return Forbid("You can only change your own password");
                }

                var result = await _userService.ChangePasswordAsync(request);

                if (!result)
                    return BadRequest(new { Message = "Invalid current password or user not found" });

                return NoContent();
            }
            catch (Exception ex)
            {
                return BadRequest(new { Message = "An error occurred while changing password", Error = ex.Message });
            }
        }

        [HttpPost("admin/change-password")]
        [Authorize(Roles = RolesUser.Admin)]
        public async Task<IActionResult> AdminChangePassword([FromBody] AdminChangePasswordRequest request)
        {
            var result = await _userService.AdminChangePasswordAsync(request);

            if (!result)
                return NotFound();

            return NoContent();
        }
    }
}
