IF EXISTS (SELECT * FROM sys.objects WHERE type = 'P' AND name = 'sp_CollaboratorRewardOperations')
    DROP PROCEDURE sp_CollaboratorRewardOperations;
GO

CREATE PROCEDURE sp_CollaboratorRewardOperations
    @CollaboratorId UNIQUEIDENTIFIER,
    @Month INT = NULL,
    @Year INT = NULL,
    @Action NVARCHAR(50)
AS
BEGIN
    IF @Action = 'Summary'
    BEGIN
        SELECT
            ISNULL(SUM(CASE WHEN cr.status = 'Paid' THEN cr.amount ELSE 0 END), 0) AS paid_amount,
            ISNULL(SUM(CASE WHEN cr.status = 'Pending' THEN cr.amount ELSE 0 END), 0) AS unpaid_amount,
            ISNULL(SUM(cr.amount), 0) AS total_reward
        FROM collaborator_rewards cr
        WHERE cr.collaborator_id = @CollaboratorId
            AND (@Year IS NULL OR YEAR(reward_date) = @Year)
            AND (@Month IS NULL OR MONTH(reward_date) = @Month)
    END

    ELSE IF @Action = 'ByType'
    BEGIN
        SELECT
            reward_type,
            SUM(amount) AS total_amount
        FROM collaborator_rewards
        WHERE collaborator_id = @CollaboratorId
            AND (@Year IS NULL OR YEAR(reward_date) = @Year)
            AND (@Month IS NULL OR MONTH(reward_date) = @Month)
        GROUP BY reward_type;
    END

    ELSE IF @Action = 'UpcomingPayment'
    BEGIN
        SELECT TOP 1
            scheduled_payment_date,
            SUM(amount) AS amount_expected,
            DATEDIFF(DAY, GETDATE(), scheduled_payment_date) AS days_remaining
        FROM collaborator_rewards
        WHERE collaborator_id = @CollaboratorId
            AND status = 'Pending'
            AND scheduled_payment_date >= GETDATE()
            AND (@Month IS NULL OR MONTH(scheduled_payment_date) = @Month)
            AND (@Year IS NULL OR YEAR(scheduled_payment_date) = @Year)
        GROUP BY scheduled_payment_date
        ORDER BY scheduled_payment_date ASC;
    END

    ELSE IF @Action = 'RewardDetails'
    BEGIN
        SELECT
            c.full_name AS candidate_name,
            c.gender,
            cr.reward_date,
            rp.position,
            cr.reward_type,
            cr.amount,
            cr.status
        FROM collaborator_rewards cr
        INNER JOIN candidate_applications ca ON ca.application_id = cr.application_id
        INNER JOIN candidates c ON c.candidate_id = ca.candidate_id
        INNER JOIN recruitment_postings rp ON rp.posting_id = ca.posting_id
        WHERE cr.collaborator_id = @CollaboratorId
            AND (@Year IS NULL OR YEAR(reward_date) = @Year)
            AND (@Month IS NULL OR MONTH(reward_date) = @Month)
        ORDER BY cr.reward_date DESC;
    END

    ELSE IF @Action = 'RewardHistory'
    BEGIN
        SELECT
            h.reward_id,
            h.amount,
            h.payment_date,
            h.payment_method,
            h.Status AS reward_status,
            c.full_name AS candidate_name,
            r.position AS position_name,
            cr.reward_type,
            cr.reward_date
        FROM collaborator_reward_history h
        INNER JOIN collaborator_rewards cr ON cr.reward_id = h.reward_id
        LEFT JOIN candidate_applications ca ON ca.application_id = cr.application_id
        LEFT JOIN candidates c ON ca.candidate_id = c.candidate_id
        LEFT JOIN recruitment_postings r ON ca.posting_id = r.posting_id
        WHERE h.collaborator_id = @CollaboratorId
            AND h.Status = 'Paid'
            AND cr.status='Paid'
            AND (@Month IS NULL OR MONTH(scheduled_payment_date) = @Month)
            AND (@Year IS NULL OR YEAR(scheduled_payment_date) = @Year)
        ORDER BY h.payment_date DESC;
    END
END
GO