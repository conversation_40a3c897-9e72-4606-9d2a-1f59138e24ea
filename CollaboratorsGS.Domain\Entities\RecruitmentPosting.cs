
using System;
using CollaboratorsGS.Domain.Enums;

namespace CollaboratorsGS.Domain.Entities
{
    public class RecruitmentPosting
    {
        public Guid PostingId { get; set; }
        public string ReferCode { get; set; } = string.Empty;
        public string Title { get; set; } = string.Empty;

        public string? Project { get; set; }
        public string? Level { get; set; }
        public string? Position { get; set; } // ObjectCode from ObjectData table

        public int? SalaryFrom { get; set; }
        public int? SalaryTo { get; set; }
        public int? Commission { get; set; }
        public int? CommissionWarrantyMonths { get; set; }

        public string? WorkingLocation { get; set; }
        public string? WorkingTime { get; set; }

        public int ViewCount { get; set; } = 0;
        public int ReferralCount { get; set; } = 0;

        public bool IsUrgent { get; set; } = false;
        public bool IsHot { get; set; } = false;
        public bool IsSaved { get; set; } = false;

        public RecruitmentPostingStatus Status { get; set; } = RecruitmentPostingStatus.Active;

        public string? JobDetailJson { get; set; }

        // Metadata
        public DateTime CreatedAt { get; set; }
        public DateTime? UpdatedAt { get; set; }
        public DateTime? ExpiredAt { get; set; }
    }
}
