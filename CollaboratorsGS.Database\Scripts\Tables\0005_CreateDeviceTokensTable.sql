    IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[device_tokens]') AND type in (N'U'))
    BEGIN
        CREATE TABLE [dbo].[device_tokens](
            [id] INT IDENTITY(1,1) NOT NULL,
            [token] NVARCHAR(500) NOT NULL,
            [user_id] NVARCHAR(50) NOT NULL,
            [device_type] NVARCHAR(20) NOT NULL,
            [created_at] DATETIME2(7) NOT NULL,
            [updated_at] DATETIME2(7) NULL,
            CONSTRAINT [pk_device_tokens] PRIMARY KEY CLUSTERED 
            (
                [id] ASC
            )
        );

        CREATE UNIQUE INDEX [ix_device_tokens_token] ON [dbo].[device_tokens]
        (
            [token] ASC
        );
    END
