using CollaboratorsGS.Domain.Entities;
using CollaboratorsGS.Domain.Repositories;
using CollaboratorsGS.Infrastructure.Data;
using Dapper;

namespace CollaboratorsGS.Infrastructure.Repositories
{
    public class RolePermissionRepository : IRolePermissionRepository
    {
        private readonly IConnectionFactory _connectionFactory;

        public RolePermissionRepository(IConnectionFactory connectionFactory)
        {
            _connectionFactory = connectionFactory;
        }

        public async Task<IEnumerable<RolePermission>> GetByRoleIdAsync(Guid roleId)
        {
            using var connection = _connectionFactory.CreateConnection();
            
            var query = @"
                SELECT rp.*, p.permission_name, p.Description
                FROM role_permissions rp
                INNER JOIN Permissions p ON rp.permission_id = p.permission_id
                WHERE rp.role_id = @RoleId";
            
            var rolePermissions = await connection.QueryAsync<RolePermission, Permission, RolePermission>(
                query,
                (rolePermission, permission) =>
                {
                    rolePermission.Permission = permission;
                    return rolePermission;
                },
                new { RoleId = roleId  },
                splitOn: "permission_name"
            );
            
            return rolePermissions;
        }

        public async Task<IEnumerable<RolePermission>> GetByPermissionIdAsync(Guid permissionId)
        {
            using var connection = _connectionFactory.CreateConnection();
            
            var query = @"
                SELECT rp.*, r.role_name, r.description
                FROM role_permissions rp
                INNER JOIN roles r ON rp.role_id = r.role_id
                WHERE rp.permission_id = @PermissionId";
            
            var rolePermissions = await connection.QueryAsync<RolePermission, Role, RolePermission>(
                query,
                (rolePermission, role) =>
                {
                    rolePermission.Role = role;
                    return rolePermission;
                },
                new { PermissionId = permissionId },
                splitOn: "role_name"
            );
            
            return rolePermissions;
        }

        public async Task<bool> AssignPermissionToRoleAsync(Guid roleId, Guid permissionId)
        {
            using var connection = _connectionFactory.CreateConnection();
            
            // Check if the assignment already exists
            var checkQuery = "SELECT COUNT(1) FROM role_rermissions WHERE role_id = @RoleId AND permission_id = @PermissionId";
            var exists = await connection.ExecuteScalarAsync<int>(checkQuery, new { RoleId = roleId, PermissionId = permissionId }) > 0;
            
            if (exists)
                return true; // Already assigned
            
            var query = "INSERT INTO role_rermissions (role_id, permission_id) VALUES (@RoleId, @PermissionId)";
            var rowsAffected = await connection.ExecuteAsync(query, new { RoleId = roleId, PermissionId = permissionId });
            
            return rowsAffected > 0;
        }

        public async Task<bool> RemovePermissionFromRoleAsync(Guid roleId, Guid permissionId)
        {
            using var connection = _connectionFactory.CreateConnection();
            
            var query = "DELETE FROM role_permissions WHERE role_id = @RoleId AND PermissionId = @PermissionId";
            var rowsAffected = await connection.ExecuteAsync(query, new { RoleId = roleId, PermissionId = permissionId });
            
            return rowsAffected > 0;
        }

        public async Task<bool> RemoveAllPermissionsFromRoleAsync(Guid roleId)
        {
            using var connection = _connectionFactory.CreateConnection();
            
            var query = "DELETE FROM role_permissions WHERE role_id = @RoleId";
            await connection.ExecuteAsync(query, new { RoleId = roleId });
            
            return true;
        }
    }
}
