using Microsoft.AspNetCore.Http;
using System.ComponentModel.DataAnnotations;

namespace CollaboratorsGS.Application.DTOs.Collaborator
{
    public class CitizenIdExtractionRequest
    {
        [Required(ErrorMessage = "Front image is required")]
        public IFormFile FrontImage { get; set; } = null!;

        [Required(ErrorMessage = "Back image is required")]
        public IFormFile BackImage { get; set; } = null!;
    }
}
