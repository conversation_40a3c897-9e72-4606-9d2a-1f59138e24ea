using CollaboratorsGS.Domain.Entities;

namespace CollaboratorsGS.Domain.Repositories
{
    public interface ICollaboratorReportRepository
    {
        Task<CollaboratorReport?> GetByIdAsync(Guid reportId);
        Task<IEnumerable<CollaboratorReport>> GetAllAsync();
        Task<IEnumerable<CollaboratorReport>> GetByCollaboratorIdAsync(Guid collaboratorId);
        Task<IEnumerable<CollaboratorReport>> GetByPeriodAsync(string period);
        Task<Guid> CreateAsync(CollaboratorReport report);
        Task<bool> UpdateAsync(CollaboratorReport report);
        Task<bool> DeleteAsync(Guid reportId);
    }
}
