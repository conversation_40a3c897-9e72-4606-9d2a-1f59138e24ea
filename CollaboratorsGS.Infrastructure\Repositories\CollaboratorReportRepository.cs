using CollaboratorsGS.Domain.Entities;
using CollaboratorsGS.Domain.Repositories;
using CollaboratorsGS.Infrastructure.Data;
using Dapper;
using System.Data;

namespace CollaboratorsGS.Infrastructure.Repositories
{
    public class CollaboratorReportRepository : ICollaboratorReportRepository
    {
        private readonly IConnectionFactory _connectionFactory;

        public CollaboratorReportRepository(IConnectionFactory connectionFactory)
        {
            _connectionFactory = connectionFactory;
        }

        public async Task<CollaboratorReport?> GetByIdAsync(Guid reportId)
        {
            using var connection = _connectionFactory.CreateConnection();

            var parameters = new DynamicParameters();
            parameters.Add("@ReportId", reportId, DbType.Guid);

            return await connection.QuerySingleOrDefaultAsync<CollaboratorReport>(
                "sp_GetCollaboratorReportById",
                parameters,
                commandType: CommandType.StoredProcedure);
        }

        public async Task<IEnumerable<CollaboratorReport>> GetAllAsync()
        {
            using var connection = _connectionFactory.CreateConnection();

            return await connection.QueryAsync<CollaboratorReport>(
                "sp_GetAllCollaboratorReports",
                commandType: CommandType.StoredProcedure);
        }

        public async Task<IEnumerable<CollaboratorReport>> GetByCollaboratorIdAsync(Guid collaboratorId)
        {
            using var connection = _connectionFactory.CreateConnection();

            var parameters = new DynamicParameters();
            parameters.Add("@CollaboratorId", collaboratorId, DbType.Guid);

            return await connection.QueryAsync<CollaboratorReport>(
                "sp_GetCollaboratorReportsByCollaboratorId",
                parameters,
                commandType: CommandType.StoredProcedure);
        }

        public async Task<IEnumerable<CollaboratorReport>> GetByPeriodAsync(string period)
        {
            using var connection = _connectionFactory.CreateConnection();

            var parameters = new DynamicParameters();
            parameters.Add("@ReportPeriod", period, DbType.String);

            return await connection.QueryAsync<CollaboratorReport>(
                "sp_GetCollaboratorReportsByPeriod",
                parameters,
                commandType: CommandType.StoredProcedure);
        }

        public async Task<Guid> CreateAsync(CollaboratorReport report)
        {
            if (report.ReportId == Guid.Empty)
            {
                report.ReportId = Guid.NewGuid();
            }

            using var connection = _connectionFactory.CreateConnection();

            var parameters = new DynamicParameters();
            parameters.Add("@ReportId", report.ReportId, DbType.Guid);
            parameters.Add("@CollaboratorId", report.CollaboratorId, DbType.Guid);
            parameters.Add("@ReportPeriod", report.ReportPeriod, DbType.String);
            parameters.Add("@TotalCandidates", report.TotalCandidates, DbType.Int32);
            parameters.Add("@TotalPayment", report.TotalPayment, DbType.Decimal);
            parameters.Add("@ReportDate", report.ReportDate, DbType.DateTime);
            parameters.Add("@Data", report.Data, DbType.String);

            await connection.ExecuteAsync(
                "sp_CreateCollaboratorReport",
                parameters,
                commandType: CommandType.StoredProcedure);

            return report.ReportId;
        }

        public async Task<bool> UpdateAsync(CollaboratorReport report)
        {
            using var connection = _connectionFactory.CreateConnection();

            var parameters = new DynamicParameters();
            parameters.Add("@ReportId", report.ReportId, DbType.Guid);
            parameters.Add("@TotalCandidates", report.TotalCandidates, DbType.Int32);
            parameters.Add("@TotalPayment", report.TotalPayment, DbType.Decimal);
            parameters.Add("@Data", report.Data, DbType.String);

            var rowsAffected = await connection.ExecuteAsync(
                "sp_UpdateCollaboratorReport",
                parameters,
                commandType: CommandType.StoredProcedure);

            return rowsAffected > 0;
        }

        public async Task<bool> DeleteAsync(Guid reportId)
        {
            using var connection = _connectionFactory.CreateConnection();

            var parameters = new DynamicParameters();
            parameters.Add("@ReportId", reportId, DbType.Guid);

            var rowsAffected = await connection.ExecuteAsync(
                "sp_DeleteCollaboratorReport",
                parameters,
                commandType: CommandType.StoredProcedure);

            return rowsAffected > 0;
        }
    }
}
