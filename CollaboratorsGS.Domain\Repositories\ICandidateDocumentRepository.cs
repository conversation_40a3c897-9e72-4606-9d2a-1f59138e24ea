using CollaboratorsGS.Domain.Entities;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace CollaboratorsGS.Domain.Repositories
{
    public interface ICandidateDocumentRepository
    {
        Task<Guid> CreateAsync(CandidateDocument document);
        Task<CandidateDocument?> GetByIdAsync(Guid documentId);
        Task<IEnumerable<CandidateDocument>> GetByCandidateIdAsync(Guid candidateId);
        Task<bool> DeleteAsync(Guid documentId);
    }
}