using CollaboratorsGS.Application.DTOs;

namespace CollaboratorsGS.Application.Interfaces
{
    public interface IRoleService
    {
        Task<IEnumerable<RoleDto>> GetAllRolesAsync();
        Task<RoleDto?> GetRoleByIdAsync(Guid roleId);
        Task<RoleWithPermissionsDto?> GetRoleWithPermissionsAsync(Guid roleId);
        Task<Guid> CreateRoleAsync(CreateRoleRequest request);
        Task<bool> UpdateRoleAsync(RoleDto roleDto);
        Task<bool> DeleteRoleAsync(Guid roleId);
        Task<bool> AssignPermissionsToRoleAsync(AssignPermissionsRequest request);
    }
}
