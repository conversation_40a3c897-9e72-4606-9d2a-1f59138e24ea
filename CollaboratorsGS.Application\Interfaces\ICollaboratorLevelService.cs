using CollaboratorsGS.Application.DTOs.CollaboratorLevel;

namespace CollaboratorsGS.Application.Interfaces
{
    public interface ICollaboratorLevelService
    {
        Task<CollaboratorLevelDto?> GetByIdAsync(Guid levelId);
        Task<IEnumerable<CollaboratorLevelDto>> GetAllAsync();
        Task<Guid> CreateCollaboratorLevelAsync(CreateCollaboratorLevelRequest request);
        Task<CollaboratorLevelDto?> GetCreatedCollaboratorLevelAsync(Guid levelId);
        Task<CollaboratorLevelDto?> UpdateCollaboratorLevelAsync(UpdateCollaboratorLevelRequest request);
        Task<bool> DeleteCollaboratorLevelAsync(Guid levelId);
    }
}
