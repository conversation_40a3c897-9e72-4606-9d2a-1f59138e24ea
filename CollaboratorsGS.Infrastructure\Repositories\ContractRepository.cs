using CollaboratorsGS.Domain.Entities;
using CollaboratorsGS.Domain.Repositories;
using CollaboratorsGS.Infrastructure.Data;
using Dapper;
using System.Data;

namespace CollaboratorsGS.Infrastructure.Repositories
{
    public class ContractRepository : IContractRepository
    {
        private readonly IConnectionFactory _connectionFactory;

        public ContractRepository(IConnectionFactory connectionFactory)
        {
            _connectionFactory = connectionFactory;
        }

        public async Task<Contract?> GetByIdAsync(Guid contractId)
        {
            using var connection = _connectionFactory.CreateConnection();

            var parameters = new DynamicParameters();
            parameters.Add("@ContractId", contractId, DbType.Guid);

            return await connection.QuerySingleOrDefaultAsync<Contract>(
                "sp_GetContractById",
                parameters,
                commandType: CommandType.StoredProcedure);
        }

        public async Task<IEnumerable<Contract>> GetAllAsync()
        {
            using var connection = _connectionFactory.CreateConnection();

            return await connection.QueryAsync<Contract>(
                "SELECT c.*, ctv.full_name AS CollaboratorName " +
                "FROM Contracts c " +
                "INNER JOIN Collaborators ctv ON c.collaborator_id = ctv.collaborator_id " +
                "ORDER BY c.created_at DESC");

        }

        public async Task<IEnumerable<Contract>> GetByCollaboratorIdAsync(Guid collaboratorId)
        {
            using var connection = _connectionFactory.CreateConnection();

            var parameters = new DynamicParameters();
            parameters.Add("@CollaboratorId", collaboratorId, DbType.Guid);

            return await connection.QueryAsync<Contract>(
                "sp_GetContractsByCollaboratorId",
                parameters,
                commandType: CommandType.StoredProcedure);
        }

        public async Task<IEnumerable<Contract>> GetByStatusAsync(string status)
        {
            using var connection = _connectionFactory.CreateConnection();

            var parameters = new DynamicParameters();
            parameters.Add("@Status", status, DbType.String);

            return await connection.QueryAsync<Contract>(
                "sp_GetContractsByStatus",
                parameters,
                commandType: CommandType.StoredProcedure);
        }

        public async Task<Guid> CreateAsync(Contract contract)
        {
            // Generate a new UUID if not provided
            if (contract.ContractId == Guid.Empty)
            {
                contract.ContractId = Guid.NewGuid();
            }

            contract.CreatedAt = DateTime.UtcNow;

            using var connection = _connectionFactory.CreateConnection();

            var parameters = new DynamicParameters();
            parameters.Add("@ContractId", contract.ContractId, DbType.Guid);
            parameters.Add("@CollaboratorId", contract.CollaboratorId, DbType.Guid);
            parameters.Add("@ContractContent", contract.ContractContent, DbType.String);
            parameters.Add("@Status", contract.Status, DbType.String);
            parameters.Add("@SignedAt", contract.SignedAt, DbType.DateTime);
            parameters.Add("@CreatedAt", contract.CreatedAt, DbType.DateTime);
            parameters.Add("@UpdatedAt", contract.UpdatedAt, DbType.DateTime);

            await connection.ExecuteAsync(
                "sp_CreateContract",
                parameters,
                commandType: CommandType.StoredProcedure);

            return contract.ContractId;
        }

        public async Task<bool> UpdateAsync(Contract contract)
        {
            contract.UpdatedAt = DateTime.UtcNow;

            using var connection = _connectionFactory.CreateConnection();

            var parameters = new DynamicParameters();
            parameters.Add("@ContractId", contract.ContractId, DbType.Guid);
            parameters.Add("@ContractContent", contract.ContractContent, DbType.String);
            parameters.Add("@Status", contract.Status, DbType.String);
            parameters.Add("@SignedAt", contract.SignedAt, DbType.DateTime);
            parameters.Add("@UpdatedAt", contract.UpdatedAt, DbType.DateTime);

            var rowsAffected = await connection.ExecuteAsync(
                "sp_UpdateContract",
                parameters,
                commandType: CommandType.StoredProcedure);

            return rowsAffected > 0;
        }

        public async Task<bool> DeleteAsync(Guid contractId)
        {
            using var connection = _connectionFactory.CreateConnection();

            var parameters = new DynamicParameters();
            parameters.Add("@ContractId", contractId, DbType.Guid);

            var rowsAffected = await connection.ExecuteAsync(
                "sp_DeleteContract",
                parameters,
                commandType: CommandType.StoredProcedure);

            return rowsAffected > 0;
        }
    }
}
