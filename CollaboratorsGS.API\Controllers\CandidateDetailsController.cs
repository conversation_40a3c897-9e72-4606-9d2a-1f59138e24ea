using CollaboratorsGS.Application.Interfaces;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using CollaboratorsGS.Application.DTOs;
using CollaboratorsGS.Infrastructure.Utilities;

namespace CollaboratorsGS.API.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    [Authorize]
    public class CandidateDetailsController : ControllerBase
    {
        private readonly ICandidateService _candidateService;
        private readonly ILogger<CandidateDetailsController> _logger;

        public CandidateDetailsController(
            ICandidateService candidateService,
            ILogger<CandidateDetailsController> logger)
        {
            _candidateService = candidateService;
            _logger = logger;
        }

        // GET: api/CandidateDetails
        [HttpGet]
        public async Task<IActionResult> GetAll()
        {
            try
            {
                var candidates = await _candidateService.GetAllDetailsAsync();
                return Ok(ApiResponse<object>.SuccessResponse(
                    MessageCodes.SC2000,
                    "Get all candidate details successfully",
                    candidates));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting all candidate details");
                return StatusCode(500, ApiResponse<object>.ErrorResponse(
                   MessageCodes.ER5000,
                   ex.Message,
                   500));
            }
        }

        // GET: api/CandidateDetails/{id}
        [HttpGet("{id}")]
        public async Task<IActionResult> GetById(Guid id)
        {
            try
            {
                var candidate = await _candidateService.GetDetailByIdAsync(id);

                if (candidate == null)
                    return NotFound(ApiResponse<object>.ErrorResponse(
                        MessageCodes.ER4004,
                        "Candidate detail not found",
                        404));

                return Ok(ApiResponse<object>.SuccessResponse(
                    MessageCodes.SC2000,
                    "Get candidate detail successfully",
                    candidate));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting candidate detail with ID {CandidateId}", id);
                return StatusCode(500, ApiResponse<object>.ErrorResponse(
                    MessageCodes.ER5000,
                    ex.Message,
                    500));
            }
        }

        // GET: api/CandidateDetails/ByCtv/{ctvId}
        [HttpGet("ByCollaboratorId/{CollaboratorId}")]
        public async Task<IActionResult> GetByCollaboratorId(Guid CollaboratorId)
        {
            try
            {
                var candidates = await _candidateService.GetDetailsByCollaboratorIdAsync(CollaboratorId);
                return Ok(ApiResponse<object>.SuccessResponse(
                    MessageCodes.SC2000,
                    "Get candidate details by collaborator successfully",
                    candidates));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting candidate details for Collaborator with ID {CollaboratorId}", CollaboratorId);
                return StatusCode(500, ApiResponse<object>.ErrorResponse(
                    MessageCodes.ER5000,
                    ex.Message,
                    500));
            }
        }
    }
}
