using CollaboratorsGS.Domain.Entities;
using CollaboratorsGS.Domain.Repositories;
using CollaboratorsGS.Infrastructure.Data;
using Dapper;
using System.Data;

namespace CollaboratorsGS.Infrastructure.Repositories
{
    public class UserRepository : IUserRepository
    {
        private readonly IConnectionFactory _connectionFactory;

        public UserRepository(IConnectionFactory connectionFactory)
        {
            _connectionFactory = connectionFactory;
        }

        public async Task<User?> GetByIdAsync(Guid userId)
        {
            using var connection = _connectionFactory.CreateConnection();

            var query = @"
                SELECT u.*, r.role_id, r.role_name, r.description as role_description
                FROM Users u
                LEFT JOIN Roles r ON u.role_id = r.role_id
                WHERE u.user_id = @UserId";

            var userDictionary = new Dictionary<Guid, User>();

            var result = await connection.QueryAsync<User, Role, User>(
                query,
                (user, role) =>
                {
                    if (!userDictionary.TryGetValue(user.UserId, out var existingUser))
                    {
                        existingUser = user;
                        existingUser.Role = role;
                        userDictionary.Add(user.UserId, existingUser);
                    }

                    return existingUser;
                },
                new { UserId = userId },
                splitOn: "role_id"
            );

            return result.FirstOrDefault();
        }

        public async Task<User?> GetByUsernameAsync(string username)
        {


            var query = @"
               SELECT
               u.*,
                r.role_id as r_role_id,
                r.role_name,
                r.description as role_description
            FROM Users u
            LEFT JOIN Roles r ON u.role_id = r.role_id
            WHERE u.username = @Username";


            using (var connection = _connectionFactory.CreateConnection())
            {
                var userDictionary = new Dictionary<Guid, User>();
                var result = await connection.QueryAsync<User, Role, User>(
                    query,
                    (user, role) =>
                    {
                        if (!userDictionary.TryGetValue(user.UserId, out var existingUser))
                        {
                            existingUser = user;
                            existingUser.Role = role;
                            userDictionary.Add(user.UserId, existingUser);
                        }

                        return existingUser;
                    },
                    new { Username = username },
                    splitOn: "r_role_id"

                );
                if (result.Count() > 0)
                    return result.FirstOrDefault();
            }



            return null;
        }

        public async Task<User?> GetByUsernameInternalAsync(string username)
        {
            var queryEcm = @"SELECT u.UserName,u.PasswordHash,e.FullName,u.Email,e.Phone as PhoneNumber FROM Users u left join employees e on e.EmployeeId=u.EmployeeId WHERE u.username = @Username";
            using (var connectionEcm = _connectionFactory.CreateConnectionEcm())
            {
                var resultEcm = await connectionEcm.QueryAsync<User>(queryEcm, new { Username = username });
                if (resultEcm.Count() > 0)
                    return resultEcm.FirstOrDefault();
            }
            return null;
        }

        public async Task<User?> GetByEmailAsync(string email)
        {
            using var connection = _connectionFactory.CreateConnection();

            var query = @"
                SELECT u.*, r.role_id, r.role_name, r.description as role_description
                FROM Users u
                LEFT JOIN Roles r ON u.role_id = r.role_id
                WHERE u.email = @Email";

            var userDictionary = new Dictionary<Guid, User>();

            var result = await connection.QueryAsync<User, Role, User>(
                query,
                (user, role) =>
                {
                    if (!userDictionary.TryGetValue(user.UserId, out var existingUser))
                    {
                        existingUser = user;
                        existingUser.Role = role;
                        userDictionary.Add(user.UserId, existingUser);
                    }

                    return existingUser;
                },
                new { Email = email },
                splitOn: "role_id"
            );

            return result.FirstOrDefault();
        }

        public async Task<User?> GetByPhoneNumberAsync(string phoneNumber)
        {
            using var connection = _connectionFactory.CreateConnection();

            var query = @"
                SELECT u.*, r.role_id, r.role_name, r.description as role_description
                FROM Users u
                LEFT JOIN Roles r ON u.role_id = r.role_id
                WHERE u.phone_number = @PhoneNumber";

            var userDictionary = new Dictionary<Guid, User>();

            var result = await connection.QueryAsync<User, Role, User>(
                query,
                (user, role) =>
                {
                    if (!userDictionary.TryGetValue(user.UserId, out var existingUser))
                    {
                        existingUser = user;
                        existingUser.Role = role;
                        userDictionary.Add(user.UserId, existingUser);
                    }

                    return existingUser;
                },
                new { PhoneNumber = phoneNumber },
                splitOn: "role_id"
            );

            return result.FirstOrDefault();
        }

        public async Task<Guid> CreateAsync(User user)
        {
            // Generate a new UUID if not provided
            if (user.UserId == Guid.Empty)
            {
                user.UserId = Guid.NewGuid();
            }

            using var connection = _connectionFactory.CreateConnection();

            var query = @"
                INSERT INTO Users (user_id, username, password, role_id, full_name, email, phone_number, created_at, last_login, is_active,[group])
                VALUES (@UserId, @Username, @Password, @RoleId, @FullName, @Email, @PhoneNumber, @CreatedAt, @LastLogin, @IsActive, @Group)";

            await connection.ExecuteAsync(query, user);
            return user.UserId;
        }



        public async Task<bool> UpdateAsync(User user)
        {
            using var connection = _connectionFactory.CreateConnection();

            var query = @"
                UPDATE Users
                SET username = @Username,
                    password = @Password,
                    role_id = @RoleId,
                    full_name = @FullName,
                    email = @Email,
                    phone_number = @PhoneNumber,
                    last_login = @LastLogin,
                    is_active = @IsActive,
                    [group] = @Group
                WHERE user_id = @UserId";

            var rowsAffected = await connection.ExecuteAsync(query, user);
            return rowsAffected > 0;
        }

        public async Task<bool> DeleteAsync(Guid User_Id)
        {
            using var connection = _connectionFactory.CreateConnection();

            var query = "DELETE FROM Users WHERE user_id = @UserId";

            var rowsAffected = await connection.ExecuteAsync(query, new { User_Id = User_Id });
            return rowsAffected > 0;
        }

        public async Task<IEnumerable<User>> GetAllAsync()
        {
            using var connection = _connectionFactory.CreateConnection();

            var query = @"
                SELECT u.*, r.role_id, r.role_name, r.Description as RoleDescription
                FROM Users u
                LEFT JOIN Roles r ON u.role_id = r.role_id";

            var userDictionary = new Dictionary<Guid, User>();

            var result = await connection.QueryAsync<User, Role, User>(
                query,
                (user, role) =>
                {
                    if (!userDictionary.TryGetValue(user.UserId, out var existingUser))
                    {
                        existingUser = user;
                        existingUser.Role = role;
                        userDictionary.Add(user.UserId, existingUser);
                    }

                    return existingUser;
                },
                splitOn: "role_id"
            );

            return userDictionary.Values;
        }


    }
}
