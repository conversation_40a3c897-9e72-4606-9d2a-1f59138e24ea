using CollaboratorsGS.Application.Constants;
using CollaboratorsGS.Application.DTOs.Branch;
using CollaboratorsGS.Application.Interfaces;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using CollaboratorsGS.Infrastructure.Utilities;
using CollaboratorsGS.Application.DTOs;

namespace CollaboratorsGS.API.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    [Authorize]
    public class BranchesController : ControllerBase
    {
        private readonly IBranchService _branchService;
        private readonly ILogger<BranchesController> _logger;

        public BranchesController(
            IBranchService branchService,
            ILogger<BranchesController> logger)
        {
            _branchService = branchService;
            _logger = logger;
        }

        // GET: api/Branches
        [HttpGet]
        public async Task<IActionResult> GetAll()
        {
            try
            {
                var branches = await _branchService.GetAllAsync();
                return Ok(ApiResponse<object>.SuccessResponse(
                    MessageCodes.SC2000,
                    "Get all branches successfully",
                    branches));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting all branches");
                return StatusCode(500, ApiResponse<object>.ErrorResponse(
                    MessageCodes.ER5000,
                    "Internal server error",
                    500));
            }
        }

        // GET: api/Branches/{id}
        [HttpGet("{id}")]
        public async Task<IActionResult> GetById(Guid id)
        {
            try
            {
                var branch = await _branchService.GetByIdAsync(id);

                if (branch == null)
                     return NotFound(ApiResponse<object>.ErrorResponse(
                        MessageCodes.ER4004,
                        "Branch not found",
                        404));

                return Ok(ApiResponse<object>.SuccessResponse(
                    MessageCodes.SC2000,
                    "Get branch successfully",
                    branch));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting branch with ID {BranchId}", id);
                 return StatusCode(500, ApiResponse<object>.ErrorResponse(
                    MessageCodes.ER5000,
                    "Internal server error",
                    500));
            }
        }

        // GET: api/Branches/ByCompany/{companyId}
        [HttpGet("ByCompany/{companyId}")]
        public async Task<IActionResult> GetByCompany(Guid companyId)
        {
            try
            {
                var branches = await _branchService.GetByCompanyAsync(companyId);
                return Ok(ApiResponse<object>.SuccessResponse(
                    MessageCodes.SC2000,
                    "Get branches by company successfully",
                    branches));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting branches for company {CompanyId}", companyId);
                return StatusCode(500, ApiResponse<object>.ErrorResponse(
                    MessageCodes.ER5000,
                    "Internal server error",
                    500));
            }
        }

        // POST: api/Branches
        [HttpPost]
        [Authorize(Roles = RolesUser.AdminManager)]
        public async Task<IActionResult> Create([FromBody] CreateBranchRequest request)
        {
            try
            {
                var branch = await _branchService.CreateBranchAsync(request);
                
                return CreatedAtAction(nameof(GetById), new { id = branch.BranchId },
                    ApiResponse<object>.SuccessResponse(
                        MessageCodes.SC2001,
                        "Branch created successfully",
                        branch,
                        201));
            }
            catch (InvalidOperationException ex)
            {
                _logger.LogWarning(ex, "Validation error creating branch");
                return BadRequest(ApiResponse<object>.ErrorResponse(
                    MessageCodes.ER4005,
                    ex.Message,
                    400,
                    new List<ErrorDetail>
                    {
                        new ErrorDetail
                        {
                            Field = "request",
                            ErrorCode = MessageCodes.ER4005,
                            Message = ex.Message
                        }
                    }));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating branch");
                return StatusCode(500, ApiResponse<object>.ErrorResponse(
                    MessageCodes.ER5000,
                    "Internal server error",
                    500));
            }
        }

        // PUT: api/Branches/{id}
        [HttpPut("{id}")]
        [Authorize(Roles = RolesUser.AdminManager)]
        public async Task<IActionResult> Update(Guid id, [FromBody] UpdateBranchRequest request)
        {
            try
            {
                if (id != request.BranchId)
                    return BadRequest(ApiResponse<object>.ErrorResponse(
                        MessageCodes.ER4001,
                        "ID mismatch",
                        400,
                        new List<ErrorDetail>
                        {
                            new ErrorDetail
                            {
                                Field = "id",
                                ErrorCode = MessageCodes.ER4001,
                                Message = "Branch ID in URL does not match request body"
                            }
                        }));

                var branch = await _branchService.UpdateBranchAsync(request);

                if (branch == null)
                    return NotFound(ApiResponse<object>.ErrorResponse(
                        MessageCodes.ER4004,
                        "Branch not found",
                        404));

                return Ok(ApiResponse<object>.SuccessResponse(
                    MessageCodes.SC2002,
                    "Branch updated successfully",
                    branch));
            }
            catch (InvalidOperationException ex)
            {
                _logger.LogWarning(ex, "Validation error updating branch");
                return BadRequest(ApiResponse<object>.ErrorResponse(
                    MessageCodes.ER4005,
                    ex.Message,
                    400,
                    new List<ErrorDetail>
                    {
                        new ErrorDetail
                        {
                            Field = "request",
                            ErrorCode = MessageCodes.ER4005,
                            Message = ex.Message
                        }
                    }));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating branch with ID {BranchId}", id);
                return StatusCode(500, ApiResponse<object>.ErrorResponse(
                    MessageCodes.ER5000,
                    "Internal server error",
                    500));
            }
        }

        // DELETE: api/Branches/{id}
        [HttpDelete("{id}")]
        [Authorize(Roles = RolesUser.Admin)]
        public async Task<IActionResult> Delete(Guid id)
        {
            try
            {
                var result = await _branchService.DeleteBranchAsync(id);

                if (!result)
                    return NotFound(ApiResponse<object>.ErrorResponse(
                        MessageCodes.ER4004,
                        "Branch not found",
                        404));

                return Ok(ApiResponse<object>.SuccessResponse(
                    MessageCodes.SC2003,
                    "Branch deleted successfully",
                    true));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting branch with ID {BranchId}", id);
               return StatusCode(500, ApiResponse<object>.ErrorResponse(
                    MessageCodes.ER5000,
                    "Internal server error",
                    500));
            }
        }
    }
}
