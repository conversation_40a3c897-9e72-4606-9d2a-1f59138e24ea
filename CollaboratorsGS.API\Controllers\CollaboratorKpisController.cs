using CollaboratorsGS.Application.Constants;
using CollaboratorsGS.Application.DTOs;
using CollaboratorsGS.Application.DTOs.CollaboratorKpi;
using CollaboratorsGS.Application.Interfaces;
using CollaboratorsGS.Infrastructure.Utilities;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace CollaboratorsGS.API.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    [Authorize]
    public class CollaboratorKpisController : ControllerBase
    {
        private readonly ICollaboratorKpiService _kpiService;
        private readonly ILogger<CollaboratorKpisController> _logger;

        public CollaboratorKpisController(
            ICollaboratorKpiService kpiService,
            ILogger<CollaboratorKpisController> logger)
        {
            _kpiService = kpiService;
            _logger = logger;
        }

        // GET: api/CtvKpis
        [HttpGet]
        [Authorize(Roles = RolesUser.AdminManager)]
        public async Task<IActionResult> GetAll()
        {
            try
            {
                var kpis = await _kpiService.GetAllAsync();
                return Ok(ApiResponse<object>.SuccessResponse(
                    MessageCodes.SC2000,
                    "Get all KPIs successfully",
                    kpis));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting all KPIs");
                return StatusCode(500, ApiResponse<object>.ErrorResponse(
                    MessageCodes.ER5000,
                    "Internal server error",
                    500));
            }
        }

        // GET: api/CtvKpis/{id}
        [HttpGet("{id}")]
        public async Task<IActionResult> GetById(Guid id)
        {
            try
            {
                var kpi = await _kpiService.GetByIdAsync(id);

                if (kpi == null)
                    return NotFound(ApiResponse<object>.ErrorResponse(
                        MessageCodes.ER4004,
                        "KPI not found",
                        404));

                return Ok(ApiResponse<object>.SuccessResponse(
                    MessageCodes.SC2000,
                    "Get KPI successfully",
                    kpi));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting KPI with ID {KpiId}", id);
                return StatusCode(500, ApiResponse<object>.ErrorResponse(
                   MessageCodes.ER5000,
                   ex.Message,
                   500));
            }
        }

        // GET: api/CollaboratorKpis/collaborator/{collaboratorId}
        [HttpGet("collaborator/{collaboratorId}")]
        public async Task<IActionResult> GetByCollaborator(Guid collaboratorId)
        {
            try
            {
                var kpis = await _kpiService.GetByCollaboratorIdAsync(collaboratorId);
                return Ok(ApiResponse<object>.SuccessResponse(
                    MessageCodes.SC2000,
                    "Get KPIs by period successfully",
                    kpis));
            }
            catch (InvalidOperationException ex)
            {
                _logger.LogWarning(ex, "Invalid operation: {Message}", ex.Message);
                return BadRequest(ApiResponse<object>.ErrorResponse(
                    MessageCodes.ER4005,
                    "Collaborator ID not found or invalid",
                    400,
                    new List<ErrorDetail>
                    {
                        new ErrorDetail
                        {
                            Field = "collaboratorId",
                            ErrorCode = MessageCodes.ER4005,
                            Message = ex.Message
                        }
                    }));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting KPIs for collaborator {CtvId}", collaboratorId);
                return StatusCode(500, ApiResponse<object>.ErrorResponse(
                    MessageCodes.ER5000,
                    ex.Message,
                    500));
            }
        }

        // GET: api/CtvKpis/period/{period}
        [HttpGet("period/{period}")]
        [Authorize(Roles = RolesUser.AdminManager)]
        public async Task<IActionResult> GetByPeriod(string period)
        {
            try
            {
                var kpis = await _kpiService.GetByPeriodAsync(period);
                return Ok(ApiResponse<object>.SuccessResponse(
                    MessageCodes.SC2000,
                    "Get KPIs by period successfully",
                    kpis));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting KPIs for period {Period}", period);
                return StatusCode(500, ApiResponse<object>.ErrorResponse(
                    MessageCodes.ER5000,
                    ex.Message,
                    500));
            }
        }

        // POST: api/CtvKpis
        [HttpPost]
        [Authorize(Roles = RolesUser.AdminManager)]
        public async Task<IActionResult> Create([FromBody] CreateCollaboratorKpiRequest request)
        {
            try
            {
                var kpiId = await _kpiService.CreateCollaboratorKpiAsync(request);
                return CreatedAtAction(nameof(GetById), new { id = kpiId }, 
                ApiResponse<object>.SuccessResponse(
                        MessageCodes.SC2001,
                        "Candidate created successfully",
                        kpiId,
                        201));
            }
            catch (InvalidOperationException ex)
            {
                _logger.LogWarning(ex, "Invalid operation: {Message}", ex.Message);
                return BadRequest(ApiResponse<object>.ErrorResponse(
                    MessageCodes.ER4005,
                    "Request body invalid",
                    400,
                    new List<ErrorDetail>
                    {
                        new ErrorDetail
                        {
                            Field = "Request",
                            ErrorCode = MessageCodes.ER4005,
                            Message = ex.Message
                        }
                    }));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating KPI");
                return StatusCode(500, ApiResponse<object>.ErrorResponse(
                    MessageCodes.ER5000,
                    ex.Message,
                    500));
            }
        }

        // PUT: api/CtvKpis/{id}
        [HttpPut("{id}")]
        [Authorize(Roles = RolesUser.AdminManager)]
        public async Task<IActionResult> Update(Guid id, [FromBody] UpdateCollaboratorKpiRequest request)
        {
            try
            {
                if (id != request.KpiId)
                    return BadRequest(ApiResponse<object>.ErrorResponse(
                    MessageCodes.ER4005,
                    "KPI ID mismatch",
                    400,
                    new List<ErrorDetail>
                    {
                        new ErrorDetail
                        {
                            Field = "KpiId",
                            ErrorCode = MessageCodes.ER4005,
                            Message ="KPI ID mismatch"
                        }
                    }));

                var result = await _kpiService.UpdateCollaboratorKpiAsync(request);

                if (result == null)
                    return NotFound(ApiResponse<object>.ErrorResponse(
                        MessageCodes.ER4004,
                        "Candidate not found",
                        404));

                return Ok(ApiResponse<object>.SuccessResponse(
                    MessageCodes.SC2003,
                    "KPI updated successfully",
                    true));
            }
            catch (InvalidOperationException ex)
            {
                _logger.LogWarning(ex, "Invalid operation: {Message}", ex.Message);
                return BadRequest(ApiResponse<object>.ErrorResponse(
                    MessageCodes.ER4005,
                    ex.Message,
                    400,
                    new List<ErrorDetail>
                    {
                        new ErrorDetail
                        {
                            Field = "KpiId",
                            ErrorCode = MessageCodes.ER4005,
                            Message = ex.Message
                        }
                    }));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating KPI with ID {KpiId}", id);
                return StatusCode(500, ApiResponse<object>.ErrorResponse(
                    MessageCodes.ER5000,
                    ex.Message,
                    500));
            }
        }

        // DELETE: api/CtvKpis/{id}
        [HttpDelete("{id}")]
        [Authorize(Roles = RolesUser.Admin)]
        public async Task<IActionResult> Delete(Guid id)
        {
            try
            {
                var result = await _kpiService.DeleteCollaboratorKpiAsync(id);

                if (!result)
                    return NotFound(ApiResponse<object>.ErrorResponse(
                        MessageCodes.ER4004,
                        "CollaboratorKPI ID not found",
                        404));

                return Ok(ApiResponse<object>.SuccessResponse(
                    MessageCodes.SC2003,
                    "CollaboratorKPI ID deleted successfully",
                    true));
            }
            catch (InvalidOperationException ex)
            {
                _logger.LogWarning(ex, "Invalid operation: {Message}", ex.Message);
                return BadRequest(ApiResponse<object>.ErrorResponse(
                    MessageCodes.ER4005,
                    ex.Message,
                    400,
                    new List<ErrorDetail>
                    {
                        new ErrorDetail
                        {
                            Field = "CollaboratorKPI_id",
                            ErrorCode = MessageCodes.ER4005,
                            Message = ex.Message
                        }
                    }));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting KPI with ID {KpiId}", id);
                return StatusCode(500, ApiResponse<object>.ErrorResponse(
                    MessageCodes.ER5000,
                    ex.Message,
                    500));
            }
        }

        // GET: api/CollaboratorKpis/detail
        [HttpGet("detail")]
        [Authorize(Roles = RolesUser.AdminManagerRecruiterCollaborator)]
        public async Task<IActionResult> GetKpiDetail()
        {
            try
            {
                // Get userId from token claims
                var userIdClaim = User.FindFirst(System.Security.Claims.ClaimTypes.NameIdentifier)
                 ?? User.FindFirst("sub");

                if (userIdClaim == null || !Guid.TryParse(userIdClaim.Value, out var userId))
                {
                    return BadRequest(ApiResponse<object>.ErrorResponse(
                        MessageCodes.ER4005,
                        "User ID not found in token or invalid",
                        400));
                }

                var kpiDetail = await _kpiService.GetKpiDetailByUserIdAsync(userId);
                return Ok(ApiResponse<object>.SuccessResponse(
                    MessageCodes.SC2000,
                    "Get KPI detail successfully",
                    kpiDetail));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting KPI detail for user {UserId}", User.FindFirst("sub")?.Value);
                return StatusCode(500, ApiResponse<object>.ErrorResponse(
                    MessageCodes.ER5000,
                    ex.Message,
                    500));
            }
        }
    }
}
