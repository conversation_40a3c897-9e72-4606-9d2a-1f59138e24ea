using CollaboratorsGS.Domain.Entities;
using CollaboratorsGS.Domain.Repositories;
using CollaboratorsGS.Infrastructure.Data;
using Dapper;
using System.Data;

namespace CollaboratorsGS.Infrastructure.Repositories
{
    public class CompanyRepository : ICompanyRepository
    {
        private readonly IConnectionFactory _connectionFactory;

        public CompanyRepository(IConnectionFactory connectionFactory)
        {
            _connectionFactory = connectionFactory;
        }

        public async Task<Company?> GetByIdAsync(Guid companyId)
        {
            using var connection = _connectionFactory.CreateConnection();

            var parameters = new DynamicParameters();
            parameters.Add("@CompanyId", companyId, DbType.Guid);

            return await connection.QuerySingleOrDefaultAsync<Company>(
                "sp_GetCompanyById",
                parameters,
                commandType: CommandType.StoredProcedure);
        }

        public async Task<IEnumerable<Company>> GetAllAsync()
        {
            using var connection = _connectionFactory.CreateConnection();

            return await connection.QueryAsync<Company>(
                "sp_GetAllCompanies",
                commandType: CommandType.StoredProcedure);
        }

        public async Task<Guid> CreateAsync(Company company)
        {
            using var connection = _connectionFactory.CreateConnection();

            // Generate a new UUID if not provided
            if (company.CompanyId == Guid.Empty)
            {
                company.CompanyId = Guid.NewGuid();
            }

            var parameters = new DynamicParameters();
            parameters.Add("@CompanyId", company.CompanyId, DbType.Guid);
            parameters.Add("@CompanyName", company.CompanyName, DbType.String);
            parameters.Add("@PhoneNumber", company.PhoneNumber, DbType.String);
            parameters.Add("@Email", company.Email, DbType.String);

            return await connection.ExecuteScalarAsync<Guid>(
                "sp_CreateCompany",
                parameters,
                commandType: CommandType.StoredProcedure);
        }

        public async Task<bool> UpdateAsync(Company company)
        {
            using var connection = _connectionFactory.CreateConnection();

            var parameters = new DynamicParameters();
            parameters.Add("@CompanyId", company.CompanyId, DbType.Guid);
            parameters.Add("@CompanyName", company.CompanyName, DbType.String);
            parameters.Add("@PhoneNumber", company.PhoneNumber, DbType.String);
            parameters.Add("@Email", company.Email, DbType.String);

            var affectedRows = await connection.ExecuteScalarAsync<int>(
                "sp_UpdateCompany",
                parameters,
                commandType: CommandType.StoredProcedure);

            return affectedRows > 0;
        }

        public async Task<bool> DeleteAsync(Guid companyId)
        {
            using var connection = _connectionFactory.CreateConnection();

            var parameters = new DynamicParameters();
            parameters.Add("@CompanyId", companyId, DbType.Guid);

            try
            {
                var affectedRows = await connection.ExecuteScalarAsync<int>(
                    "sp_DeleteCompany",
                    parameters,
                    commandType: CommandType.StoredProcedure);

                return affectedRows > 0;
            }
            catch
            {
                return false;
            }
        }
    }
}
