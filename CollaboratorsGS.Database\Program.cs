using Microsoft.Extensions.Configuration;

namespace CollaboratorsGS.Database
{
    public class Program
    {
        public static int Main(string[] args)
        {
            var configuration = new ConfigurationBuilder()
                .SetBasePath(Directory.GetCurrentDirectory())
                .AddJsonFile("appsettings.json")
                .Build();

            var upgrader = DatabaseUpgrader.CreateFromConfiguration(configuration);

            // Check command line arguments
            if (args.Length > 0)
            {
                switch (args[0].ToLower())
                {
                    case "migrate":
                        return upgrader.PerformMigrationOnly() ? 0 : -1;
                    case "seed":
                        return upgrader.PerformSeedingOnly() ? 0 : -1;
                    default:
                        Console.WriteLine("Unknown command. Use 'migrate' or 'seed'.");
                        return -1;
                }
            }

            // Default: perform both migration and seeding
            return upgrader.PerformUpgrade() ? 0 : -1;
        }
    }
}