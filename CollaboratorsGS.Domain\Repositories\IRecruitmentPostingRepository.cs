using CollaboratorsGS.Domain.Entities;
using CollaboratorsGS.Domain.Enums;

namespace CollaboratorsGS.Domain.Repositories
{
    public interface IRecruitmentPostingRepository
    {
        Task<RecruitmentPosting?> GetByIdAsync(Guid postingId);
        Task<RecruitmentPosting?> GetByIdWithDetailAsync(Guid postingId);
        Task<IEnumerable<RecruitmentPosting>> GetAllAsync();
        Task<IEnumerable<RecruitmentPosting>> GetByReferCodeAsync(string referCode);
        Task<(IEnumerable<RecruitmentPosting> data, int totalCount)> SearchAsync(
            string? keyword = null,
            string? level = null,
            string? workingLocation = null,
            int? salaryFrom = null,
            int? salaryTo = null,
            bool? isUrgent = null,
            bool? isHot = null,
            RecruitmentPostingStatus? status = null,
            int page = 1,
            int pageSize = 10,
            string sortBy = "created_at",
            string sortOrder = "desc");
        Task<Guid> CreateAsync(RecruitmentPosting posting);
        Task<bool> UpdateAsync(RecruitmentPosting posting);
        Task<bool> DeleteAsync(Guid postingId);
        Task<bool> IncrementViewCountAsync(Guid postingId);
        Task<bool> IncrementReferralCountAsync(Guid postingId);
    }
}
