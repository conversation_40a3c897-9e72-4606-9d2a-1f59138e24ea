using CollaboratorsGS.Domain.Entities;
using CollaboratorsGS.Domain.Repositories;
using CollaboratorsGS.Infrastructure.Data;
using Dapper;
using System.Data;

namespace CollaboratorsGS.Infrastructure.Repositories
{
    public class CollaboratorRepository : ICollaboratorRepository
    {
        private readonly IConnectionFactory _connectionFactory;

        public CollaboratorRepository(IConnectionFactory connectionFactory)
        {
            _connectionFactory = connectionFactory;
        }

        public async Task<Collaborator?> GetByIdAsync(Guid CollaboratorId)
        {
            using var connection = _connectionFactory.CreateConnection();

            var collaboratorDictionary = new Dictionary<Guid, Collaborator>();
            var profileDictionary = new Dictionary<Guid, CollaboratorProfile>();

            var result = await connection.QueryAsync<Collaborator, User, CollaboratorLevel, CollaboratorProfile, Collaborator>(
                "sp_GetCollaboratorById",
                (collaborator, user, level, profile) =>
                {
                    if (!collaboratorDictionary.TryGetValue(collaborator.CollaboratorId, out var existingCollaborator))
                    {
                        existingCollaborator = collaborator;
                        existingCollaborator.User = user;
                        existingCollaborator.Level = level;
                        collaboratorDictionary.Add(collaborator.CollaboratorId, existingCollaborator);
                    }

                    if (profile != null && !profileDictionary.ContainsKey(profile.ProfileId))
                    {
                        existingCollaborator.Profile = profile;
                        profileDictionary.Add(profile.ProfileId, profile);
                    }

                    return existingCollaborator;
                },
                new { Collaborator_Id = CollaboratorId },
                splitOn: "user_id,level_id,profile_id",
                commandType: CommandType.StoredProcedure
            );

            return result.FirstOrDefault();
        }

        public async Task<IEnumerable<Collaborator>> GetAllAsync()
        {
            using var connection = _connectionFactory.CreateConnection();

            var collaboratorDictionary = new Dictionary<Guid, Collaborator>();
            var profileDictionary = new Dictionary<Guid, CollaboratorProfile>();

            var result = await connection.QueryAsync<Collaborator, User, CollaboratorLevel, CollaboratorProfile, Collaborator>(
                "sp_GetAllCollaborators",
                (collaborator, user, level, profile) =>
                {
                    if (!collaboratorDictionary.TryGetValue(collaborator.CollaboratorId, out var existingCollaborator))
                    {
                        existingCollaborator = collaborator;
                        existingCollaborator.User = user;
                        existingCollaborator.Level = level;
                        collaboratorDictionary.Add(collaborator.CollaboratorId, existingCollaborator);
                    }

                    if (profile != null && !profileDictionary.ContainsKey(profile.ProfileId))
                    {
                        existingCollaborator.Profile = profile;
                        profileDictionary.Add(profile.ProfileId, profile);
                    }

                    return existingCollaborator;
                },
                splitOn: "user_id,level_id,profile_id",
                commandType: CommandType.StoredProcedure
            );

            return collaboratorDictionary.Values;
        }

        public async Task<IEnumerable<Collaborator>> GetByStatusAsync(string status)
        {
            using var connection = _connectionFactory.CreateConnection();

            var collaboratorDictionary = new Dictionary<Guid, Collaborator>();
            var profileDictionary = new Dictionary<Guid, CollaboratorProfile>();

            var result = await connection.QueryAsync<Collaborator, User, CollaboratorLevel, CollaboratorProfile, Collaborator>(
                "sp_GetCollaboratorsByStatus",
                (collaborator, user, level, profile) =>
                {
                    if (!collaboratorDictionary.TryGetValue(collaborator.CollaboratorId, out var existingCollaborator))
                    {
                        existingCollaborator = collaborator;
                        existingCollaborator.User = user;
                        existingCollaborator.Level = level;
                        collaboratorDictionary.Add(collaborator.CollaboratorId, existingCollaborator);
                    }

                    if (profile != null && !profileDictionary.ContainsKey(profile.ProfileId))
                    {
                        existingCollaborator.Profile = profile;
                        profileDictionary.Add(profile.ProfileId, profile);
                    }

                    return existingCollaborator;
                },
                new { Status = status },
                splitOn: "user_id,level_id,profile_id",
                commandType: CommandType.StoredProcedure
            );

            return collaboratorDictionary.Values;
        }

        public async Task<Collaborator?> GetByUserIdAsync(Guid userId)
        {
            using var connection = _connectionFactory.CreateConnection();

            var collaboratorDictionary = new Dictionary<Guid, Collaborator>();
            var profileDictionary = new Dictionary<Guid, CollaboratorProfile>();

            var result = await connection.QueryAsync<Collaborator, User, CollaboratorLevel, CollaboratorProfile, Collaborator>(
                "sp_GetCollaboratorByUserId",
                (collaborator, user, level, profile) =>
                {
                    if (!collaboratorDictionary.TryGetValue(collaborator.CollaboratorId, out var existingCollaborator))
                    {
                        existingCollaborator = collaborator;
                        existingCollaborator.User = user;
                        existingCollaborator.Level = level;
                        collaboratorDictionary.Add(collaborator.CollaboratorId, existingCollaborator);
                    }

                    if (profile != null && !profileDictionary.ContainsKey(profile.ProfileId))
                    {
                        existingCollaborator.Profile = profile;
                        profileDictionary.Add(profile.ProfileId, profile);
                    }

                    return existingCollaborator;
                },
                new { UserId = userId },
                splitOn: "user_id,level_id,profile_id",
                commandType: CommandType.StoredProcedure
            );

            return result.FirstOrDefault();
        }

        public async Task<Collaborator?> GetByEmailAsync(string email)
        {
            using var connection = _connectionFactory.CreateConnection();

            var collaboratorDictionary = new Dictionary<Guid, Collaborator>();
            var profileDictionary = new Dictionary<Guid, CollaboratorProfile>();

            var result = await connection.QueryAsync<Collaborator, User, CollaboratorLevel, CollaboratorProfile, Collaborator>(
                "sp_GetCollaboratorByEmail",
                (collaborator, user, level, profile) =>
                {
                    if (!collaboratorDictionary.TryGetValue(collaborator.CollaboratorId, out var existingCollaborator))
                    {
                        existingCollaborator = collaborator;
                        existingCollaborator.User = user;
                        existingCollaborator.Level = level;
                        collaboratorDictionary.Add(collaborator.CollaboratorId, existingCollaborator);
                    }

                    if (profile != null && !profileDictionary.ContainsKey(profile.ProfileId))
                    {
                        existingCollaborator.Profile = profile;
                        profileDictionary.Add(profile.ProfileId, profile);
                    }

                    return existingCollaborator;
                },
                new { Email = email },
                splitOn: "user_id,level_id,profile_id",
                commandType: CommandType.StoredProcedure
            );

            return result.FirstOrDefault();
        }

        public async Task<Collaborator?> GetByPhoneNumberAsync(string phoneNumber)
        {
            using var connection = _connectionFactory.CreateConnection();

            var collaboratorDictionary = new Dictionary<Guid, Collaborator>();
            var profileDictionary = new Dictionary<Guid, CollaboratorProfile>();

            var result = await connection.QueryAsync<Collaborator, User, CollaboratorLevel, CollaboratorProfile, Collaborator>(
                "sp_GetCollaboratorByPhoneNumber",
                (collaborator, user, level, profile) =>
                {
                    if (!collaboratorDictionary.TryGetValue(collaborator.CollaboratorId, out var existingCollaborator))
                    {
                        existingCollaborator = collaborator;
                        existingCollaborator.User = user;
                        existingCollaborator.Level = level;
                        collaboratorDictionary.Add(collaborator.CollaboratorId, existingCollaborator);
                    }

                    if (profile != null && !profileDictionary.ContainsKey(profile.ProfileId))
                    {
                        existingCollaborator.Profile = profile;
                        profileDictionary.Add(profile.ProfileId, profile);
                    }

                    return existingCollaborator;
                },
                new { PhoneNumber = phoneNumber },
                splitOn: "user_id,level_id,profile_id",
                commandType: CommandType.StoredProcedure
            );

            return result.FirstOrDefault();
        }

        public async Task<Guid> CreateAsync(Collaborator collaborator)
        {
            // Generate a new UUID if not provided
            if (collaborator.CollaboratorId == Guid.Empty)
            {
                collaborator.CollaboratorId = Guid.NewGuid();
            }

            using var connection = _connectionFactory.CreateConnection();

            var parameters = new
            {
                collaborator.CollaboratorId,
                collaborator.UserId,
                collaborator.FullName,
                collaborator.PhoneNumber,
                collaborator.Email,
                collaborator.LevelId,
                collaborator.Status,
                collaborator.LastLevelUpdatedAt,
                collaborator.CreatedAt,
                collaborator.ApprovedBy,
                collaborator.ApprovedDate,
                collaborator.UpdatedAt
            };

            await connection.ExecuteAsync(
                "sp_CreateCollaborator",
                parameters,
                commandType: CommandType.StoredProcedure
            );

            return collaborator.CollaboratorId;
        }

        public async Task<bool> UpdateAsync(Collaborator collaborator)
        {
            collaborator.UpdatedAt = DateTime.UtcNow;

            using var connection = _connectionFactory.CreateConnection();

            var parameters = new
            {
                collaborator_id = collaborator.CollaboratorId,
                full_name = collaborator.FullName,
                phone_number = collaborator.PhoneNumber,
                email = collaborator.Email,
                level_id = collaborator.LevelId,
                status = collaborator.Status,
                last_level_updated_at = collaborator.LastLevelUpdatedAt,
                updated_at = collaborator.UpdatedAt
            };

            try
            {
                var result = await connection.ExecuteAsync(
                    "sp_UpdateCollaborator",
                    parameters,
                    commandType: CommandType.StoredProcedure
                );

                return result > 0;
            }
            catch (Exception ex)
            {
                // Log the exception details for debugging
                throw new InvalidOperationException($"Failed to update collaborator {collaborator.CollaboratorId}. " +
                    $"Error: {ex.Message}. Parameters: FullName='{collaborator.FullName}', " +
                    $"PhoneNumber='{collaborator.PhoneNumber}', Email='{collaborator.Email}'", ex);
            }
        }

        public async Task<bool> DeleteAsync(Guid CollaboratorId)
        {
            using var connection = _connectionFactory.CreateConnection();

            var result = await connection.ExecuteScalarAsync<int>(
                "sp_DeleteCollaborator",
                new { CollaboratorId = CollaboratorId },
                commandType: CommandType.StoredProcedure
            );

            return result > 0;
        }

        public async Task<bool> ApproveAsync(Guid CollaboratorId, Guid approvedBy)
        {
            using var connection = _connectionFactory.CreateConnection();

            var parameters = new
            {
                CollaboratorId = CollaboratorId,
                ApprovedBy = approvedBy,
                ApprovedDate = DateTime.UtcNow,
                Status = "Active",
                UpdatedAt = DateTime.UtcNow
            };

            var result = await connection.ExecuteScalarAsync<int>(
                "sp_ApproveCollaborator",
                parameters,
                commandType: CommandType.StoredProcedure
            );

            return result > 0;
        }

        public async Task<CollaboratorProfile?> GetProfileByCollaboratorIdAsync(Guid CollaboratorId)
        {
            using var connection = _connectionFactory.CreateConnection();

            return await connection.QueryFirstOrDefaultAsync<CollaboratorProfile>(
                "sp_GetCollaboratorProfileByCollaboratorId",
                new { CollaboratorId = CollaboratorId },
                commandType: CommandType.StoredProcedure
            );
        }

        public async Task<bool> CreateProfileAsync(CollaboratorProfile profile)
        {
            // Generate a new UUID if not provided
            if (profile.ProfileId == Guid.Empty)
            {
                profile.ProfileId = Guid.NewGuid();
            }

            profile.CreatedAt = DateTime.UtcNow;

            using var connection = _connectionFactory.CreateConnection();

            var parameters = new
            {
                profile.ProfileId,
                profile.CollaboratorId,
                profile.CitizenId,
                profile.PermanentAddress,
                profile.CurrentAddress,
                profile.BankName,
                profile.BankAccountNumber,
                profile.CitizenIdExtracted,
                profile.FullNameExtracted,
                profile.DateOfBirthExtracted,
                profile.GenderExtracted,
                profile.AddressExtracted,
                profile.PersonalIdentificationExtracted,
                profile.IdIssueDateExtracted,
                profile.IdIssueAuthorityExtracted,
                profile.ExtractionRawData,
                profile.ExtractionTimestamp,
                profile.DataSource,
                profile.CreatedAt,
                profile.UpdatedAt
            };

            try
            {
                // Use QueryFirstOrDefault to get the result from stored procedure
                var result = await connection.QueryFirstOrDefaultAsync<dynamic>(
                    "sp_CreateCollaboratorProfile",
                    parameters,
                    commandType: CommandType.StoredProcedure
                );

                var rowsAffected = result?.RowsAffected ?? 0;

                if (rowsAffected <= 0)
                {
                    throw new InvalidOperationException($"No rows affected when creating collaborator profile. " +
                        $"CollaboratorId: {profile.CollaboratorId}, ProfileId: {profile.ProfileId}. " +
                        $"This might indicate a constraint violation or the profile already exists.");
                }

                return rowsAffected > 0;
            }
            catch (Exception ex)
            {
                // Log the exception details for debugging
                throw new InvalidOperationException($"Failed to create collaborator profile for CollaboratorId: {profile.CollaboratorId}. " +
                    $"Error: {ex.Message}. Parameters: CitizenId='{profile.CitizenId}', " +
                    $"BankName='{profile.BankName}', BankAccountNumber='{profile.BankAccountNumber}', " +
                    $"ProfileId: {profile.ProfileId}", ex);
            }
        }

        public async Task<bool> UpdateProfileAsync(CollaboratorProfile profile)
        {
            profile.UpdatedAt = DateTime.UtcNow;

            using var connection = _connectionFactory.CreateConnection();

            var parameters = new
            {
                ProfileId = profile.ProfileId == Guid.Empty ? (Guid?)null : profile.ProfileId,
                CollaboratorId = profile.CollaboratorId,
                CitizenId = profile.CitizenId,

                PermanentAddress = profile.PermanentAddress,
                CurrentAddress = profile.CurrentAddress,
                BankName = profile.BankName,
                BankAccountNumber = profile.BankAccountNumber,
                BankAccountName = profile.BankAccountName,
                UpdatedAt = profile.UpdatedAt
            };

            try
            {
                // Use QueryFirstOrDefault to get the result from stored procedure
                var result = await connection.QueryFirstOrDefaultAsync<dynamic>(
                    "sp_UpdateCollaboratorProfile",
                    parameters,
                    commandType: CommandType.StoredProcedure
                );

                var rowsAffected = result?.RowsAffected ?? 0;
                var profileExisted = result?.ProfileExisted ?? false;

                // Log the result for debugging
                System.Diagnostics.Debug.WriteLine($"UpdateProfile Result: RowsAffected={rowsAffected}, ProfileExisted={profileExisted}, CollaboratorId={profile.CollaboratorId}");

                if (rowsAffected <= 0)
                {
                    throw new InvalidOperationException($"Failed to update profile. " +
                        $"CollaboratorId: {profile.CollaboratorId}, ProfileId: {profile.ProfileId}, " +
                        $"ProfileExisted: {profileExisted}, RowsAffected: {rowsAffected}");
                }

                return true; // Return true if we get here (rowsAffected > 0)
            }
            catch (Exception ex) when (!(ex is InvalidOperationException))
            {
                // Log the exception details for debugging
                throw new InvalidOperationException($"Failed to update collaborator profile for CollaboratorId: {profile.CollaboratorId}. " +
                    $"Error: {ex.Message}. Parameters: CitizenId='{profile.CitizenId}', " +
                    $"BankName='{profile.BankName}', BankAccountNumber='{profile.BankAccountNumber}', " +
                    $"ProfileId: {profile.ProfileId}", ex);
            }
        }

        public async Task<CollaboratorProfile?> GetProfileByCtvIdAsync(Guid CollaboratorId)
        {
            using var connection = _connectionFactory.CreateConnection();

            try
            {
                System.Diagnostics.Debug.WriteLine($"GetProfileByCtvIdAsync: Searching for CollaboratorId={CollaboratorId}");

                var result = await connection.QueryFirstOrDefaultAsync<CollaboratorProfile>(
                    "sp_GetCollaboratorProfileByCollaboratorId",
                    new { CollaboratorId = CollaboratorId },
                    commandType: CommandType.StoredProcedure
                );

                System.Diagnostics.Debug.WriteLine($"GetProfileByCtvIdAsync: Result={(result != null ? $"Found ProfileId={result.ProfileId}" : "NULL")}");

                return result;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"GetProfileByCtvIdAsync: Error={ex.Message}");
                throw;
            }
        }

        public async Task<bool> CreateOrUpdateProfileFromExtractionAsync(Guid collaboratorId, string citizenIdExtracted, string fullNameExtracted, string dobString, string genderExtracted, string addressExtracted, string personalIdentificationExtracted, string idIssueDateString, string idIssueAuthorityExtracted, string extractionRawData)
        {
            using var connection = _connectionFactory.CreateConnection();

            var parameters = new
            {
                CollaboratorId = collaboratorId,
                CitizenIdExtracted = citizenIdExtracted,
                FullNameExtracted = fullNameExtracted,
                DateOfBirthString = dobString,
                GenderExtracted = genderExtracted,
                AddressExtracted = addressExtracted,
                PersonalIdentificationExtracted = personalIdentificationExtracted,
                IdIssueDateString = idIssueDateString,
                IdIssueAuthorityExtracted = idIssueAuthorityExtracted,
                ExtractionRawData = extractionRawData
            };

            try
            {
                var result = await connection.QueryFirstOrDefaultAsync<dynamic>(
                    "sp_CreateCollaboratorProfileFromExtraction",
                    parameters,
                    commandType: CommandType.StoredProcedure
                );

                var rowsAffected = result?.RowsAffected ?? 0;
                return rowsAffected > 0;
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"Failed to create/update collaborator profile from extraction for CollaboratorId: {collaboratorId}. Error: {ex.Message}", ex);
            }
        }

        public async Task<bool> UpdateInformationProfileAsync(Guid collaboratorId, string? currentAddress, string? bankName, string? bankAccountNumber, string? bankAccountName)
        {
            using var connection = _connectionFactory.CreateConnection();

            var parameters = new
            {
                CollaboratorId = collaboratorId,
                CurrentAddress = currentAddress,
                BankName = bankName,
                BankAccountNumber = bankAccountNumber,
                BankAccountName = bankAccountName
            };

            try
            {
                var result = await connection.QueryFirstOrDefaultAsync<dynamic>(
                    "sp_UpdateCollaboratorInformation",
                    parameters,
                    commandType: CommandType.StoredProcedure
                );

                var rowsAffected = result?.RowsAffected ?? 0;
                return rowsAffected > 0;
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"Failed to update collaborator information for CollaboratorId: {collaboratorId}. Error: {ex.Message}", ex);
            }
        }

        public async Task UpdateAvatarAsync(Guid collaboratorId, string avatarUrl)
        {
            using var connection = _connectionFactory.CreateConnection();

            const string sql = @"
                UPDATE collaborator_profiles
                SET avatar = @AvatarUrl, updated_at = GETDATE()
                WHERE collaborator_id = @CollaboratorId;

                -- Create profile if it doesn't exist
                IF @@ROWCOUNT = 0
                BEGIN
                    INSERT INTO collaborator_profiles (profile_id, collaborator_id, avatar, created_at, updated_at)
                    VALUES (NEWID(), @CollaboratorId, @AvatarUrl, GETDATE(), GETDATE());
                END";

            await connection.ExecuteAsync(sql, new { CollaboratorId = collaboratorId, AvatarUrl = avatarUrl });
        }

        public async Task<IEnumerable<dynamic>?> GetReferalHistoryAsync(Guid userId)
        {
            using var connection = _connectionFactory.CreateConnection();

            var parameters = new DynamicParameters();
            parameters.Add("@UserId", userId, DbType.Guid);

            return await connection.QueryAsync<dynamic>(
                "sp_GetCollaboratorReferalHistory",
                parameters,
                commandType: CommandType.StoredProcedure);
        }

        public async Task<object> GetLevelDetailByUserIdAsync(Guid userId)
        {
            using var connection = _connectionFactory.CreateConnection();

            var parameters = new DynamicParameters();
            parameters.Add("@UserId", userId, DbType.Guid);

            using var multi = await connection.QueryMultipleAsync(
                "sp_GetCollaboratorLevelDetail",
                parameters,
                commandType: CommandType.StoredProcedure);

            var currentLevel = await multi.ReadFirstOrDefaultAsync<dynamic>();
            var benefits = (await multi.ReadAsync<dynamic>()).ToList();
            var requirements = (await multi.ReadAsync<dynamic>()).ToList();
            var nextLevel = await multi.ReadFirstOrDefaultAsync<dynamic>();
            var levelHistory = (await multi.ReadAsync<dynamic>()).ToList();

            return new
            {
                CurrentLevel = currentLevel,
                Benefits = benefits,
                Requirements = requirements,
                NextLevel = nextLevel,
                LevelHistory = levelHistory
            };
        }

        public async Task<dynamic?> CheckLevelUpgradeEligibilityAsync(Guid userId)
        {
            using var connection = _connectionFactory.CreateConnection();

            var parameters = new DynamicParameters();
            parameters.Add("@UserId", userId, DbType.Guid);

            return await connection.QueryFirstOrDefaultAsync<dynamic>(
                "sp_CheckLevelUpgradeEligibility",
                parameters,
                commandType: CommandType.StoredProcedure);
        }

        public async Task<Guid?> GetByUserId(Guid userId)
        {
            using var connection = _connectionFactory.CreateConnection();

            return await connection.QueryFirstOrDefaultAsync<Guid?>(
                "SELECT collaborator_id FROM collaborators WHERE user_id = @userId",
                new { userId }
            );

           
        }

    }
}
