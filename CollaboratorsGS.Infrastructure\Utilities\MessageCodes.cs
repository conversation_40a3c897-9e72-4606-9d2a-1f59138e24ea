namespace CollaboratorsGS.Infrastructure.Utilities
{
    public static class MessageCodes
    {
        // Success Codes
        public const string SC2000 = "SC2000"; // Success
        public const string SC2001 = "SC2001"; // Created successfully
        public const string SC2002 = "SC2002"; // Updated successfully
        public const string SC2003 = "SC2003"; // Deleted successfully
        public const string SC2004 = "SC2004"; // Logged in successfully
        public const string SC2005 = "SC2005"; // Logged out successfully
        public const string SC2006 = "SC2006"; // Email sent successfully
        public const string SC2007 = "SC2007"; // File uploaded successfully
        public const string SC2008 = "SC2008"; // Authentication successful

        // Client Error Codes (4xx)
        public const string ER4001 = "ER4001"; // Invalid parameter
        public const string ER4002 = "ER4002"; // Missing required parameter
        public const string ER4003 = "ER4003"; // Invalid data format
        public const string ER4004 = "ER4004"; // Data not found
        public const string ER4005 = "ER4005"; // Bad request
        public const string ER4009 = "ER4009"; // Conflict

        // Server Error Codes (5xx)
        public const string ER5000 = "ER5000"; // System error
        public const string ER5001 = "ER5001"; // Database connection error
        public const string ER5002 = "ER5002"; // Data processing error
        public const string ER5005 = "ER5005"; // File upload error

        // Validation Error Codes
        public const string VE6001 = "VE6001"; // Required field must not be empty
        public const string VE6002 = "VE6002"; // Invalid length
        public const string VE6003 = "VE6003"; // Value is out of allowed range
        public const string VE6004 = "VE6004"; // Field must be unique

        //sql exception codes
        public const string E2601 = "E2601"; // SQL syntax error // Error Message

        // Warning Codes
        public const string WR3001 = "WR3001"; // Data is about to expire

        // Informational Codes
        public const string IN1001 = "IN1001"; // No data available
    }
}
