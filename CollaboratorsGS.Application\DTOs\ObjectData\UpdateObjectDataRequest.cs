using System.ComponentModel.DataAnnotations;

namespace CollaboratorsGS.Application.DTOs.ObjectData
{
    public class UpdateObjectDataRequest
    {
        [Required]
        [StringLength(100)]
        public string ObjectType { get; set; } = string.Empty;
        
        [StringLength(100)]
        public string? ObjectCode { get; set; }
        
        [Required]
        [StringLength(255)]
        public string ObjectValue { get; set; } = string.Empty;
        
        [StringLength(500)]
        public string? Description { get; set; }
    }
}
