using CollaboratorsGS.Application.Constants;
using CollaboratorsGS.Application.DTOs;
using CollaboratorsGS.Application.DTOs.CollaboratorKpiTarget;
using CollaboratorsGS.Application.Interfaces;
using CollaboratorsGS.Infrastructure.Utilities;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace CollaboratorsGS.API.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    [Authorize]
    public class CtvKpiTargetsController : ControllerBase
    {
        private readonly ICollaboratorKpiTargetService _targetService;
        private readonly ILogger<CtvKpiTargetsController> _logger;

        public CtvKpiTargetsController(
            ICollaboratorKpiTargetService targetService,
            ILogger<CtvKpiTargetsController> logger)
        {
            _targetService = targetService;
            _logger = logger;
        }

        // GET: api/CtvKpiTargets
        [HttpGet]
        [Authorize(Roles = RolesUser.AdminManager)]
        public async Task<IActionResult> GetAll()
        {
            try
            {
                var targets = await _targetService.GetAllAsync();
                return Ok(ApiResponse<object>.SuccessResponse(
                    MessageCodes.SC2000,
                    "Get targets successfully",
                    targets));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting all KPI targets");
                return StatusCode(500, ApiResponse<object>.ErrorResponse(
                    MessageCodes.ER5000,
                    "Internal server error",
                    500));
            }
        }

        // GET: api/CtvKpiTargets/{id}
        [HttpGet("{id}")]
        public async Task<IActionResult> GetById(Guid id)
        {
            try
            {
                var target = await _targetService.GetByIdAsync(id);
                
                if (target == null)
                    return NotFound(ApiResponse<object>.ErrorResponse(
                         MessageCodes.ER4004,
                         "Target not found",
                         404));
                
                return Ok(ApiResponse<object>.SuccessResponse(
                    MessageCodes.SC2000,
                    "Get target by id successfully",
                    target));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting KPI target with ID {TargetId}", id);
                return StatusCode(500, ApiResponse<object>.ErrorResponse(
                    MessageCodes.ER5000,
                    "Internal server error",
                    500));
            }
        }

        // GET: api/CtvKpiTargets/collaborator/{ctvId}
        [HttpGet("collaborator/{collaboratorId}")]
        public async Task<IActionResult> GetByCollaborator(Guid collaboratorId)
        {
            try
            {
                var targets = await _targetService.GetByCollaboratorIdAsync(collaboratorId);
                return Ok(ApiResponse<object>.SuccessResponse(
                    MessageCodes.SC2000,
                    "Get target by collaborator id successfully",
                    targets));
            }
            catch (InvalidOperationException ex)
            {
                _logger.LogWarning(ex, "Invalid operation: {Message}", ex.Message);
                return BadRequest(ApiResponse<object>.ErrorResponse(
                    MessageCodes.ER4005,
                    ex.Message,
                    400,
                    new List<ErrorDetail>
                    {
                        new ErrorDetail
                        {
                            Field = "collaboratorId",
                            ErrorCode = MessageCodes.ER4005,
                            Message = ex.Message
                        }
                    }));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting KPI targets for collaborator {collaboratorId}", collaboratorId);
                return StatusCode(500, ApiResponse<object>.ErrorResponse(
                    MessageCodes.ER5000,
                    "Internal server error",
                    500));
            }
        }

        // GET: api/CtvKpiTargets/period/{period}
        [HttpGet("period/{period}")]
        [Authorize(Roles = RolesUser.AdminManager)]
        public async Task<IActionResult> GetByPeriod(string period)
        {
            try
            {
                var targets = await _targetService.GetByPeriodAsync(period);
                return Ok(ApiResponse<object>.SuccessResponse(
                    MessageCodes.SC2000,
                    "Get period successfully",
                    targets));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting KPI targets for period {Period}", period);
                return StatusCode(500, ApiResponse<object>.ErrorResponse(
                    MessageCodes.ER5000,
                    "Internal server error",
                    500));
            }
        }

        // POST: api/CtvKpiTargets
        [HttpPost]
        [Authorize(Roles = RolesUser.AdminManager)]
        public async Task<IActionResult> Create([FromBody] CreateCollaboratorKpiTargetRequest request)
        {
            try
            {
                var targetId = await _targetService.CreateCollaboratorKpiTargetAsync(request);
                return CreatedAtAction(nameof(GetById), new { id = targetId }, 
                ApiResponse<object>.SuccessResponse(
                    MessageCodes.SC2001,
                    "Create kpi target successfully",
                    200));
            }
            catch (InvalidOperationException ex)
            {
                _logger.LogWarning(ex, "Invalid operation: {Message}", ex.Message);
                return BadRequest(ApiResponse<object>.ErrorResponse(
                    MessageCodes.ER4005,
                    ex.Message,
                    400,
                    new List<ErrorDetail>
                    {
                        new ErrorDetail
                        {
                            Field = "request",
                            ErrorCode = MessageCodes.ER4005,
                            Message = ex.Message
                        }
                    }));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating KPI target");
                return StatusCode(500, ApiResponse<object>.ErrorResponse(
                    MessageCodes.ER5000,
                    "Internal server error",
                    500));
            }
        }

        // PUT: api/CtvKpiTargets/{id}
        [HttpPut("{id}")]
        [Authorize(Roles = RolesUser.AdminManager)]
        public async Task<IActionResult> Update(Guid id, [FromBody] UpdateCollaboratorKpiTargetRequest request)
        {
            try
            {
                if (id != request.TargetId)
                    return StatusCode(400, ApiResponse<object>.ErrorResponse(
                    MessageCodes.ER4002,
                    "ID mismatch",
                    400));

                
                var result = await _targetService.UpdateCollaboratorKpiTargetAsync(request);
                
                if (result== null)
                    return NotFound(ApiResponse<object>.ErrorResponse(
                        MessageCodes.ER4004,
                        "Collaborator target not found",
                        404));
                
                return Ok(ApiResponse<object>.SuccessResponse(
                    MessageCodes.SC2002,
                    "Updated collaborator target updated successfully",
                    result));
            }
            catch (InvalidOperationException ex)
            {
                _logger.LogWarning(ex, "Invalid operation: {Message}", ex.Message);
                return BadRequest(ApiResponse<object>.ErrorResponse(
                    MessageCodes.ER4005,
                    ex.Message,
                    400,
                    new List<ErrorDetail>
                    {
                        new ErrorDetail
                        {
                            Field = "request",
                            ErrorCode = MessageCodes.ER4005,
                            Message = ex.Message
                        }
                    }));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating KPI target with ID {TargetId}", id);
                return StatusCode(500, ApiResponse<object>.ErrorResponse(
                    MessageCodes.ER5000,
                    "Internal server error",
                    500));
            }
        }

        // DELETE: api/CtvKpiTargets/{id}
        [HttpDelete("{id}")]
        [Authorize(Roles = RolesUser.Admin)]
        public async Task<IActionResult> Delete(Guid id)
        {
            try
            {
                var result = await _targetService.DeleteCollaboratorKpiTargetAsync(id);
                
                if (!result)
                    return NotFound();
                
                return Ok(ApiResponse<object>.SuccessResponse(
                        MessageCodes.SC2003,
                        "Deleted target successfully",
                        200));
            }
            catch (InvalidOperationException ex)
            {
                _logger.LogWarning(ex, "Invalid operation: {Message}", ex.Message);
                return BadRequest(ApiResponse<object>.ErrorResponse(
                    MessageCodes.ER4005,
                    ex.Message,
                    400,
                    new List<ErrorDetail>
                    {
                        new ErrorDetail
                        {
                            Field = "request",
                            ErrorCode = MessageCodes.ER4005,
                            Message = ex.Message
                        }
                    }));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting KPI target with ID {TargetId}", id);
                return StatusCode(500, ApiResponse<object>.ErrorResponse(
                    MessageCodes.ER5000,
                    "Internal server error",
                    500));
            }
        }
    }
}
