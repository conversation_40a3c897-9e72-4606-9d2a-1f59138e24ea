
using System;

namespace CollaboratorsGS.Application.DTOs.CandidateDocument
{
    public class CandidateDocumentDto
    {
        public Guid DocumentId { get; set; }
        public Guid CandidateId { get; set; }
        public string DocumentType { get; set; } = string.Empty;
        public string FilePath { get; set; } = string.Empty;
        public string FileType { get; set; } = string.Empty;
        public DateTime UploadedAt { get; set; }
    }
}
