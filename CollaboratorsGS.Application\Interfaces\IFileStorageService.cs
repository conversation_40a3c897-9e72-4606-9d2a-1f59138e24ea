using Microsoft.AspNetCore.Http;

namespace CollaboratorsGS.Application.Interfaces
{
    public interface IFileStorageService
    {
        /// <summary>
        /// Upload a file to storage
        /// </summary>
        /// <param name="file">The file to upload</param>
        /// <param name="fileName">Optional custom file name</param>
        /// <returns>The filename of the uploaded file</returns>
        Task<string> UploadFileAsync(IFormFile file, string? fileName = null);

        /// <summary>
        /// Delete a file from storage
        /// </summary>
        /// <param name="fileName">Name of the file to delete</param>
        /// <returns>True if successful, false otherwise</returns>
        Task<bool> DeleteFileAsync(string fileName);

        /// <summary>
        /// Get a temporary URL with time-limited access to a file
        /// </summary>
        /// <param name="fileName">Name of the file</param>
        /// <param name="expiryMinutes">Minutes until the URL expires</param>
        /// <returns>Temporary URL to access the file</returns>
        Task<string> GetTemporaryUrlAsync(string fileName, int expiryMinutes = 60);
    }
}
