
using CollaboratorsGS.Application.Interfaces;
using Microsoft.AspNetCore.Mvc;

namespace CollaboratorsGS.API.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class AddressController : ControllerBase
    {
        private readonly IAddressService _addressService;

        public AddressController(IAddressService addressService)
        {
            _addressService = addressService;
        }

        [HttpGet("Provinces/{isRestructure=false}")]
        public async Task<IActionResult> GetAllProvince(bool isRestructure = false)
        {
            var provinces = await _addressService.GetAllProvincesAsync(isRestructure);
            return Ok(provinces);
        }

        [HttpGet("Dictricts/{queryCode}")]
        public async Task<IActionResult> GetDictrisctByQueryCode(string queryCode)
        {
            var dictricts = await _addressService.GetDistrictsByProvinceNameAsync(queryCode);
            return Ok(dictricts);
        }

        [HttpGet("Wards/{queryCode}/{isRestructure=false}")]
        public async Task<IActionResult> GetWardByQueryCode(string queryCode, bool isRestructure = false)
        {
            var wards = await _addressService.GetWardsByDictricstNameAsync(queryCode,isRestructure);
            return Ok(wards);
        }
    }
}