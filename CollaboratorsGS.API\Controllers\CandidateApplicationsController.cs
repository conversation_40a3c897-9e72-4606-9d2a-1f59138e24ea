using System.Data.SqlClient;
using CollaboratorsGS.Application.Constants;
using CollaboratorsGS.Application.DTOs;
using CollaboratorsGS.Application.DTOs.CandidateApplication;
using CollaboratorsGS.Application.Interfaces;
using FirebaseAdmin.Messaging;
using Google.Protobuf.WellKnownTypes;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using CollaboratorsGS.Infrastructure.Utilities;
using System.CodeDom;

namespace CollaboratorsGS.API.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    [Authorize]
    public class CandidateApplicationsController : ControllerBase
    {
        private readonly ICandidateApplicationService _applicationService;
        private readonly ICandidateService _candidateService;
        private readonly ILogger<CandidateApplicationsController> _logger;

        public CandidateApplicationsController(
            ICandidateApplicationService applicationService,
            ILogger<CandidateApplicationsController> logger,
            ICandidateService candidateService)
        {
            _applicationService = applicationService;
            _logger = logger;
            _candidateService = candidateService;
        }

        // GET: api/CandidateApplications
        [HttpGet]
        [Authorize(Roles = RolesUser.AdminManagerRecruiter)]
        public async Task<IActionResult> GetAll()
        {
            try
            {

                var applications = await _applicationService.GetAllAsync();
                if (applications == null || !applications.Any())
                {
                    return NotFound(ApiResponse<object>.ErrorResponse(
                        MessageCodes.ER4004,
                        "No candidate applications found",
                        404, errors: new List<ErrorDetail>
                        {
                            new ErrorDetail
                            {
                                Field = "CandidateApplications",
                                ErrorCode = MessageCodes.ER4004,
                                Message ="No candidate applications found"
                            }
                        }
                ));
                }

                return Ok(ApiResponse<object>.SuccessResponse(
                    MessageCodes.SC2000,
                    "Retrieved all candidate applications successfully",
                    applications));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting all candidate applications");
                return StatusCode(500, ApiResponse<object>.ErrorResponse(
                                   MessageCodes.ER5000,
                                   ex.Message,
                                   500));
            }
        }

        // GET: api/CandidateApplications/{id}
        [HttpGet("{id}")]
        public async Task<IActionResult> GetById(Guid id)
        {
            try
            {
                var application = await _applicationService.GetByIdAsync(id);

                if (application == null)
                {
                    return NotFound(ApiResponse<object>.ErrorResponse(
                            MessageCodes.ER4004,
                            "No candidate applications found",
                            404, errors: new List<ErrorDetail>
                            {
                            new ErrorDetail
                            {
                                Field = "id",
                                ErrorCode = MessageCodes.ER4004,
                                Message ="No candidate applications by id found"
                            }
                    }
                ));
                }

                return Ok(ApiResponse<object>.SuccessResponse(
                    MessageCodes.SC2000,
                    "Retrieved candidate applications by id successfully",
                    application));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting candidate application with ID {ApplicationId}", id);
                return StatusCode(500, ApiResponse<object>.ErrorResponse(
                    MessageCodes.ER5000,
                    "Internal server error while retrieving candidate applications",
                    500, errors: new List<ErrorDetail>
                    {
                        new ErrorDetail
                        {
                            Field = "id",
                            ErrorCode = MessageCodes.ER5000,
                            Message = "An unexpected error occurred while processing your request."
                        }
                    }));
            }
        }

        // GET: api/CandidateApplications/candidate/{candidateId}
        [HttpGet("candidate/{candidateId}")]
        public async Task<IActionResult> GetByCandidate(Guid candidateId)
        {
            try
            {
                var applications = await _applicationService.GetByCandidateAsync(candidateId);
                return Ok(ApiResponse<object>.SuccessResponse(
                    MessageCodes.SC2000,
                    "Get applications by candidate successfully",
                    applications));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting applications for candidate {CandidateId}", candidateId);
                return StatusCode(500, ApiResponse<object>.ErrorResponse(
                    MessageCodes.ER5000,
                    ex.Message,
                    500));
            }
        }

        // GET: api/CandidateApplications/posting/{postingId}
        [HttpGet("posting/{postingId}")]
        public async Task<IActionResult> GetByPosting(Guid postingId)
        {
            try
            {
                var applications = await _applicationService.GetByPostingAsync(postingId);
                return Ok(ApiResponse<object>.SuccessResponse(
                    MessageCodes.SC2000,
                    "Get applications by posting successfully",
                    applications));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting applications for posting {PostingId}", postingId);
                return StatusCode(500, ApiResponse<object>.ErrorResponse(
                                   MessageCodes.ER5000,
                                   ex.Message,
                                   500));
            }
        }

        // GET: api/CandidateApplications/status/{status}
        [HttpGet("status/{status}")]
        [Authorize(Roles = RolesUser.AdminManagerRecruiterCollaborator)]
        public async Task<IActionResult> GetByStatus(string status)
        {
            try
            {
                var applications = await _applicationService.GetByStatusAsync(status);
                return Ok(ApiResponse<object>.SuccessResponse(
                    MessageCodes.SC2000,
                    "Get applications by status successfully",
                    applications));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting applications with status {Status}", status);
                return StatusCode(500, ApiResponse<object>.ErrorResponse(
                    MessageCodes.ER5000,
                    ex.Message,
                    500));
            }
        }

        // POST: api/CandidateApplications
        [HttpPost]
        public async Task<IActionResult> Create([FromBody] CreateCandidateApplicationRequest request)
        {
            var existsCandidate = await _candidateService.GetByIdAsync(request.CandidateId);
            if (existsCandidate == null)
            {
                return BadRequest(ApiResponse<object>.ErrorResponse(
                    MessageCodes.ER4004,
                    "Candidate does not exist",
                    400,
                    new List<ErrorDetail>
                    {
                        new ErrorDetail
                        {
                            Field = "CandidateId",
                            ErrorCode = MessageCodes.ER4004,
                            Message = "Candidate not found"
                        }
                    }));
            }

            try
            {
                var applicationId = await _applicationService.CreateApplicationAsync(request);
                var createdApplication = await _applicationService.GetByIdAsync(applicationId);
                if (createdApplication == null)
                    return BadRequest(ApiResponse<object>.ErrorResponse(
                        MessageCodes.ER5000,
                        "Failed to retrieve created application",
                        500));
                return CreatedAtAction(nameof(GetById), new { id = applicationId },
                        ApiResponse<object>.SuccessResponse(
                            MessageCodes.SC2001,
                            "Application created successfully",
                            createdApplication,
                            201));
            }
            catch (SqlException ex) when (ex.Number == 2601 || ex.Number == 2627)
            {
                return Conflict(ApiResponse<object>.ErrorResponse(
                    MessageCodes.ER4001,
                    "Application already exists",409,
                    new List<ErrorDetail>
                    { 
                        new ErrorDetail
                        {
                            Field = "CandidateId", 
                            ErrorCode = MessageCodes.E2601,
                            Message = "sql syntax error"
                        }   
                    }));
            }
            catch (InvalidOperationException ex)
            {
                return Conflict(ApiResponse<object>.ErrorResponse(
                    MessageCodes.ER4001,
                    "Application already exists",409,
                    new List<ErrorDetail>
                    { 
                        new ErrorDetail
                        {
                            Field = "CandidateId", 
                            ErrorCode = MessageCodes.ER4009,
                            Message = "Candidate has already applied for this posting" ?? ex.Message
                        }   
                    }));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating candidate application");
                return StatusCode(500, ApiResponse<object>.ErrorResponse(
                    MessageCodes.ER5000,
                    ex.Message,
                    500));
            }
        }

        // PUT: api/CandidateApplications/{id}
        [HttpPut("{id}")]
        [Authorize(Roles = RolesUser.AdminManagerRecruiter)]
        public async Task<IActionResult> Update(Guid id, [FromBody] UpdateCandidateApplicationRequest request)
        {
            var existsApplicationId = await _applicationService.GetByIdAsync(id);
            if (!ModelState.IsValid)
            {
                return ValidationHelper.CreateValidationErrorResponse(ModelState);
            }
            if (existsApplicationId == null)
            {
                return BadRequest(ApiResponse<object>.ErrorResponse(
                    MessageCodes.ER4004,
                    "Application does not exist",
                    400));
            }

            try
            {
                var result = await _applicationService.UpdateApplicationAsync(request);

                if (!result)
                    return NotFound(ApiResponse<object>.ErrorResponse(
                        MessageCodes.ER4004,
                        "Application not found",
                        404));

                return Ok(ApiResponse<object>.SuccessResponse(
                    MessageCodes.SC2002,
                    "Application updated successfully",
                    true));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating candidate application with ID {ApplicationId}", id);
               return StatusCode(500, ApiResponse<object>.ErrorResponse(
                    MessageCodes.ER5000,
                    ex.Message,
                    500));
            }
        }

        // DELETE: api/CandidateApplications/{id}
        [HttpDelete("{id}")]
        [Authorize(Roles = RolesUser.AdminRecruiter)]
        public async Task<IActionResult> Delete(Guid id)
        {
            try
            {
                var result = await _applicationService.DeleteApplicationAsync(id);

                if (!result)
                    return NotFound(ApiResponse<object>.ErrorResponse(
                        MessageCodes.ER4004,
                        "Application not found",
                        404));

                return Ok(ApiResponse<object>.SuccessResponse(
                    MessageCodes.SC2003,
                    "Application deleted successfully",
                    true));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting candidate application with ID {ApplicationId}", id);
                return StatusCode(500, ApiResponse<object>.ErrorResponse(
                    MessageCodes.ER5000,
                    ex.Message,
                    500));
            }
        }

        // GET: api/CandidateApplications/candidate/{candidateId}/applied-histories?posting_id={postingId}
        [HttpGet("candidate/{candidateId}/applied-history")]
        [Authorize(Roles = RolesUser.AdminManagerRecruiterCollaborator)]
        public async Task<IActionResult> GetCandidateAppliedHistories(Guid candidateId, [FromQuery] Guid? posting_id = null)
        {
            try
            {
                var appliedPositions = await _applicationService.GetCandidateAppliedHistoriesAsync(candidateId, posting_id);
                return Ok(ApiResponse<object>.SuccessResponse(
                    MessageCodes.SC2000,
                    "Get history candidate application successfully",
                    appliedPositions));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting applied positions for candidate {CandidateId}, posting {PostingId}", candidateId, posting_id);
               return StatusCode(500, ApiResponse<object>.ErrorResponse(
                    MessageCodes.ER5000,
                    ex.Message,
                    500));
            }
        }

        // GET: api/CandidateApplications/candidate/{candidateId}/detail?posting_id={postingId}
        [HttpGet("candidate/{candidateId}/detail")]
        [Authorize(Roles = RolesUser.AdminManagerRecruiterCollaborator)]
        public async Task<IActionResult> GetCandidateApplicationDetail(Guid candidateId, [FromQuery] Guid? posting_id = null)
        {
            try
            {
                var applicationDetail = await _applicationService.GetCandidateApplicationDetailAsync(candidateId, posting_id);
                return Ok(ApiResponse<object>.SuccessResponse(
                    MessageCodes.SC2000,
                    "Get candidate application detail successfully",
                    applicationDetail));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting application detail for candidate {CandidateId}, posting {PostingId}", candidateId, posting_id);
                return StatusCode(500, ApiResponse<object>.ErrorResponse(
                    MessageCodes.ER5000,
                    ex.Message,
                    500));
            }
        }


    }
}

