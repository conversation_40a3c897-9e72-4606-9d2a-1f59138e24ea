using System.ComponentModel.DataAnnotations;

namespace CollaboratorsGS.Application.DTOs.Branch
{
    public class UpdateBranchRequest
    {
        [Required]
        public Guid BranchId { get; set; }

        [Required]
        public Guid CompanyId { get; set; }

        [Required]
        [StringLength(255)]
        public string BranchName { get; set; } = string.Empty;

        [StringLength(20)]
        public string? PhoneNumber { get; set; }

        [EmailAddress]
        [StringLength(255)]
        public string? Email { get; set; }
    }
}
