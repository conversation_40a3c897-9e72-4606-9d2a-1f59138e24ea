-- Create KPI and Report related tables

-- Create CtCollaboratorKpis table
IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'collaborator_kpis' AND schema_id = SCHEMA_ID('dbo'))
BEGIN
CREATE TABLE [dbo].[collaborator_kpis] (
    [collaborator_kpis_id] UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
    [collaborator_id] UNIQUEIDENTIFIER NOT NULL,
    [period] NVARCHAR(7) NOT NULL,
    [total_candidates_imported] INT DEFAULT 0,
    [total_candidates_passed_round1] INT DEFAULT 0,
    [total_candidates_passed_round2] INT DEFAULT 0,
    [total_candidates_onboarded] INT DEFAULT 0,
    [total_candidates_failed] INT DEFAULT 0,
    [total_candidates_onboarded_warranty] INT DEFAULT 0,
    [success_rate] FLOAT,
    [calculated_at] DATETIME NOT NULL DEFAULT GETDATE(),
    CONSTRAINT [FK_CtvKpis_Collaborators] FOREIGN KEY ([collaborator_id]) REFERENCES [dbo].[collaborators] ([collaborator_id])
);
END
GO

-- Create CtvKpiTargets table
IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'collaborator_kpi_targets' AND schema_id = SCHEMA_ID('dbo'))
BEGIN
CREATE TABLE [dbo].[collaborator_kpi_targets] (
    [collaborator_targets_id] UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
    [collaborator_id] UNIQUEIDENTIFIER NOT NULL,
    [period] NVARCHAR(7) NOT NULL,
    [target_candidates_imported] INT DEFAULT 0,
    [target_candidates_passed_round1] INT DEFAULT 0,
    [target_candidates_onboarded] INT DEFAULT 0,
    [created_at] DATETIME NOT NULL DEFAULT GETDATE(),
    [updated_at] DATETIME
    CONSTRAINT [FK_Collaborators_KpiTargets] FOREIGN KEY ([collaborator_id]) REFERENCES [dbo].[collaborators] ([collaborator_id])
);
END
GO

-- Create CtvReports table
IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'collaborator_reports' AND schema_id = SCHEMA_ID('dbo'))
BEGIN
CREATE TABLE [dbo].[collaborator_reports] (
    [collaborator_reports_id] UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
    [collaborator_id] UNIQUEIDENTIFIER NOT NULL,
    [report_period] NVARCHAR(7) NOT NULL,
    [total_candidates] INT DEFAULT 0,
    [total_payment] DECIMAL(18, 2) DEFAULT 0,
    [report_date] DATE NOT NULL DEFAULT GETDATE(),
    [data] NVARCHAR(MAX)
    CONSTRAINT [FK_Collaborators_Reports] FOREIGN KEY ([collaborator_id]) REFERENCES [dbo].[collaborators] ([collaborator_id])
);
END
GO

-- Create DashboardMetrics table

IF NOT EXISTS (
    SELECT * FROM sys.tables 
    WHERE name = 'dashboard_metrics' AND schema_id = SCHEMA_ID('dbo')
)
BEGIN
CREATE TABLE [dbo].[dashboard_metrics] (
    [dashboard_metrics_id] UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
    [period] NVARCHAR(7) NOT NULL,
    [total_ctvs] INT DEFAULT 0,
    [total_candidates] INT DEFAULT 0,
    [total_onboarded] INT DEFAULT 0,
    [calculated_at] DATETIME NOT NULL DEFAULT GETDATE()
);
END
