
namespace CollaboratorsGS.Domain.Entities
{
    // This entity will store information about external authentication providers
    public class ExternalAuthInfo
    {
        public Guid Id { get; set; }
        public Guid UserId { get; set; }
        public string Provider { get; set; } = string.Empty; // "Google", "Facebook", etc.
        public string ExternalId { get; set; } = string.Empty;
        public string? Email { get; set; }
        public string? Name { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime? LastLogin { get; set; }

        // Navigation properties
        public User? User { get; set; }
    }
}
