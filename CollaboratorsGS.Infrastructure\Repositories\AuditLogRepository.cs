using CollaboratorsGS.Application.Interfaces;
using CollaboratorsGS.Domain.Entities;
using CollaboratorsGS.Domain.Repositories;
using CollaboratorsGS.Infrastructure.Data;
using Dapper;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace CollaboratorsGS.Infrastructure.Repositories
{
    public class AuditLogRepository : IAuditLogRepository
    {
        private readonly IConnectionFactory _connectionFactory;

        public AuditLogRepository(IConnectionFactory connectionFactory)
        {
            _connectionFactory = connectionFactory;
        }

        public async Task<Guid> CreateAsync(AuditLogs auditLog)
        {
            const string sql = @"
                INSERT INTO [dbo].[audit_logs] 
                    ([log_id], [user_id], [action], [entity_type], [entity_id], 
                     [old_value], [new_value], [action_date], [ip_address], [status])
                VALUES 
                    (@LogId, @UserId, @Action, @EntityType, @EntityId, 
                     @OldValue, @NewValue, @ActionDate, @IpAddress, @Status);";

            using var connection = _connectionFactory.CreateConnection();
            await connection.ExecuteAsync(sql, auditLog);  
            return auditLog.LogId;
        }

        public async Task<IEnumerable<AuditLogs>> GetByUserIdAsync(Guid userId)
        {
            const string sql = @"
                SELECT * FROM [dbo].[audit_logs]
                WHERE [user_id] = @UserId
                ORDER BY [action_date] DESC;";

            using var connection = _connectionFactory.CreateConnection();
            return await connection.QueryAsync<AuditLogs>(sql, new { UserId = userId });
        }

        public async Task<IEnumerable<AuditLogs>> GetByEntityTypeAndIdAsync(string entityType, Guid entityId)
        {
            const string sql = @"
                SELECT * FROM [dbo].[audit_logs]
                WHERE [entity_type] = @EntityType AND [entity_id] = @EntityId
                ORDER BY [action_date] DESC;";

            using var connection = _connectionFactory.CreateConnection();
            return await connection.QueryAsync<AuditLogs>(sql, new { EntityType = entityType, EntityId = entityId });
        }

        public async Task<IEnumerable<AuditLogs>> GetRecentActivitiesByUserIdAsync(Guid userId, int limit)
        {
            const string sql = @"
                SELECT TOP (@Limit) al.*
                FROM [dbo].[audit_logs] al
                WHERE (
                    -- Exclude HTTP request logs (only get business activities)
                    al.[entity_type] != 'Request'
                    AND
                    (
                        -- Activities done by the collaborator themselves
                        al.[user_id] = @UserId
                        OR
                        -- Activities on candidates owned by this collaborator
                        (al.[entity_type] = 'Candidate' AND al.[entity_id] IN (
                            SELECT c.candidate_id FROM Candidates c
                            INNER JOIN Collaborators ctv ON c.collaborator_id = ctv.collaborator_id
                            WHERE ctv.user_id = @UserId
                        ))
                        OR
                        -- Activities on candidate applications for candidates owned by this collaborator
                        (al.[entity_type] = 'candidate_application' AND al.[entity_id] IN (
                            SELECT ca.application_id FROM candidate_applications ca
                            INNER JOIN candidates c ON ca.candidate_id = c.candidate_id
                            INNER JOIN collaborators ctv ON c.collaborator_id = ctv.collaborator_id
                            WHERE ctv.user_id = @UserId
                        ))
                        OR
                        -- Activities on rewards for this collaborator
                        (al.[entity_type] = 'collaborator_rewards' AND al.[entity_id] IN (
                            SELECT cr.reward_id FROM collaborator_rewards cr
                            INNER JOIN collaborators ctv ON cr.collaborator_id = ctv.collaborator_id
                            WHERE ctv.user_id = @UserId
                        ))
                        OR
                        -- All recruitment posting activities (visible to all collaborators)
                        al.[entity_type] = 'recruitment_posting'
                    )
                )
                ORDER BY al.[action_date] DESC;";

            using var connection = _connectionFactory.CreateConnection();
            return await connection.QueryAsync<AuditLogs>(sql, new { UserId = userId, Limit = limit });
        }
    }
}