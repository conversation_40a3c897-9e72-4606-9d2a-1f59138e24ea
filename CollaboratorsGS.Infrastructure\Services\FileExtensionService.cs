using System.Collections.Concurrent;

namespace CollaboratorsGS.Infrastructure.Services
{
    public class FileExtensionService
    {
        private readonly ConcurrentDictionary<string, string> _fileExtensions = new ConcurrentDictionary<string, string>();

        public void AddFileExtension(string fileId, string extension)
        {
            _fileExtensions.TryAdd(fileId, extension);
        }

        public string GetFileExtension(string fileId)
        {
            if (_fileExtensions.TryGetValue(fileId, out var extension))
            {
                return extension;
            }
            
            return ".png";
        }

        public void RemoveFileExtension(string fileId)
        {
            _fileExtensions.TryRemove(fileId, out _);
        }
    }
}
