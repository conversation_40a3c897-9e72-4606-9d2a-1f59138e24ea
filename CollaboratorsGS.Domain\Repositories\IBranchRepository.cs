using CollaboratorsGS.Domain.Entities;

namespace CollaboratorsGS.Domain.Repositories
{
    public interface IBranchRepository
    {
        Task<Branch?> GetByIdAsync(Guid branchId);
        Task<IEnumerable<Branch>> GetAllAsync();
        Task<IEnumerable<Branch>> GetByCompanyAsync(Guid companyId);
        Task<Guid> CreateAsync(Branch branch);
        Task<bool> UpdateAsync(Branch branch);
        Task<bool> DeleteAsync(Guid branchId);
    }
}
