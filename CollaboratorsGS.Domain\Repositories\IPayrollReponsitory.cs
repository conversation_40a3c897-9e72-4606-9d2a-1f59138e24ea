using CollaboratorsGS.Domain.Entities;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace CollaboratorsGS.Domain.Repositories
{
    public interface IPayrollRepository
    {
        public Task<Payroll> GetByIdAsync(int payroll_id, int UserId);
        Task<IEnumerable<Payroll>> GetListAsync(int Year, int month, int UserId);
    }

 
}
