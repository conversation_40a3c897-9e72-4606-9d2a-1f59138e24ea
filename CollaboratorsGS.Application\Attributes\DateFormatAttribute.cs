using System.ComponentModel.DataAnnotations;
using CollaboratorsGS.Application.DTOs.Common;

namespace CollaboratorsGS.Application.Attributes
{
    /// <summary>
    /// Validation attribute to validate date string format (dd/MM/yyyy)
    /// </summary>
    public class DateFormatAttribute : ValidationAttribute
    {
        public DateFormatAttribute() : base("Invalid date format. Expected format: dd/MM/yyyy")
        {
        }

        public override bool IsValid(object? value)
        {
            if (value == null)
            {
                return true; // null is valid for optional fields
            }

            if (value is string dateString)
            {
                return DateHelper.IsValidDateFormat(dateString);
            }

            return false;
        }
    }
}
