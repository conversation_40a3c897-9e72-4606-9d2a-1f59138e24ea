
namespace CollaboratorsGS.Domain.Entities
{
    public class AuditLogs
    {
        public Guid LogId { get; set; }

        public Guid UserId { get; set; }

        public string? Action { get; set; }

        public string? EntityType { get; set; }

        public Guid EntityId { get; set; }

        public string? OldValue { get; set; }

        public string? NewValue { get; set; }

        public DateTime ActionDate { get; set; }

        public string? IpAddress { get; set; }

        public string? Status { get; set; }

        // Navigation properties
        public User? User { get; set; }
    }
}