
namespace CollaboratorsGS.Domain.Entities
{
    public class CollaboratorReport
    {
        public Guid ReportId { get; set; }
        public Guid CollaboratorId { get; set; }
        public string ReportPeriod { get; set; } = string.Empty;
        public int TotalCandidates { get; set; } = 0;
        public decimal TotalPayment { get; set; } = 0;
        public DateTime ReportDate { get; set; }
        public string? Data { get; set; }
        public string? CollaboratorName { get; set; }

        // Navigation properties
        public Collaborator? Collaborator { get; set; }
    }
}
