
namespace CollaboratorsGS.Domain.Entities
{
    public class Department
    {
        public Guid DepartmentId { get; set; }
        public string DepartmentName { get; set; } = string.Empty;
        public Guid? ManagerId { get; set; }
        public Guid? BranchId { get; set; }

        // Navigation properties
        public User? Manager { get; set; }
        public ICollection<Position>? Positions { get; set; }
    }
}
