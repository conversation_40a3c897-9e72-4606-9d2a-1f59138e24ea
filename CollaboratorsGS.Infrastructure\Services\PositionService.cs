using AutoMapper;
using CollaboratorsGS.Application.DTOs.Position;
using CollaboratorsGS.Application.Interfaces;
using CollaboratorsGS.Domain.Entities;
using CollaboratorsGS.Domain.Repositories;
using Microsoft.Extensions.Logging;

namespace CollaboratorsGS.Infrastructure.Services
{
    public class PositionService : IPositionService
    {
        private readonly IPositionRepository _positionRepository;
        private readonly IMapper _mapper;
        private readonly ILogger<PositionService> _logger;

        public PositionService(
            IPositionRepository positionRepository,
            IMapper mapper,
            ILogger<PositionService> logger)
        {
            _positionRepository = positionRepository;
            _mapper = mapper;
            _logger = logger;
        }

        public async Task<PositionDto?> GetByIdAsync(Guid positionId)
        {
            try
            {
                var position = await _positionRepository.GetByIdAsync(positionId);
                if (position == null)
                    return null;

                return _mapper.Map<PositionDto>(position);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting position with ID {PositionId}", positionId);
                throw;
            }
        }

        public async Task<IEnumerable<PositionDto>> GetAllAsync()
        {
            try
            {
                var positions = await _positionRepository.GetAllAsync();
                return _mapper.Map<IEnumerable<PositionDto>>(positions);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting all positions");
                throw;
            }
        }

        public async Task<IEnumerable<PositionDto>> GetByDepartmentAsync(Guid departmentId)
        {
            try
            {
                var positions = await _positionRepository.GetByDepartmentAsync(departmentId);
                return _mapper.Map<IEnumerable<PositionDto>>(positions);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting positions for department {DepartmentId}", departmentId);
                throw;
            }
        }

        public async Task<Guid> CreatePositionAsync(CreatePositionRequest request)
        {
            try
            {
                var position = _mapper.Map<Position>(request);
                position.PositionId = Guid.NewGuid();

                return await _positionRepository.CreateAsync(position);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating position");
                throw;
            }
        }

        public async Task<PositionDto?> GetCreatedPositionAsync(Guid positionId)
        {
            return await GetByIdAsync(positionId);
        }

        public async Task<PositionDto?> UpdatePositionAsync(UpdatePositionRequest request)
        {
            try
            {
                // Check if position exists
                var existingPosition = await _positionRepository.GetByIdAsync(request.PositionId);
                if (existingPosition == null)
                {
                    _logger.LogWarning("Position with ID {PositionId} not found", request.PositionId);
                    return null;
                }

                var position = _mapper.Map<Position>(request);
                var result = await _positionRepository.UpdateAsync(position);

                if (!result)
                    return null;

                // Get the updated position
                return await GetByIdAsync(request.PositionId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating position with ID {PositionId}", request.PositionId);
                throw;
            }
        }

        public async Task<bool> DeletePositionAsync(Guid positionId)
        {
            try
            {
                // Check if position exists
                var existingPosition = await _positionRepository.GetByIdAsync(positionId);
                if (existingPosition == null)
                {
                    _logger.LogWarning("Position with ID {PositionId} not found", positionId);
                    return false;
                }

                return await _positionRepository.DeleteAsync(positionId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting position with ID {PositionId}", positionId);
                throw;
            }
        }
    }
}
