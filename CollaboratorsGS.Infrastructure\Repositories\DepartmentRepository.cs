using CollaboratorsGS.Domain.Entities;
using CollaboratorsGS.Domain.Repositories;
using CollaboratorsGS.Infrastructure.Data;
using Dapper;
using System.Data;

namespace CollaboratorsGS.Infrastructure.Repositories
{
    public class DepartmentRepository : IDepartmentRepository
    {
        private readonly IConnectionFactory _connectionFactory;

        public DepartmentRepository(IConnectionFactory connectionFactory)
        {
            _connectionFactory = connectionFactory;
        }

        public async Task<Department?> GetByIdAsync(Guid departmentId)
        {
            using var connection = _connectionFactory.CreateConnection();

            var parameters = new DynamicParameters();
            parameters.Add("@DepartmentId", departmentId, DbType.Guid);

            return await connection.QuerySingleOrDefaultAsync<Department>(
                "sp_GetDepartmentById",
                parameters,
                commandType: CommandType.StoredProcedure);
        }

        public async Task<IEnumerable<Department>> GetAllAsync()
        {
            using var connection = _connectionFactory.CreateConnection();

            return await connection.QueryAsync<Department>(
                "sp_GetAllDepartments",
                commandType: CommandType.StoredProcedure);
        }

        public async Task<Guid> CreateAsync(Department department)
        {
            // Generate a new UUID if not provided
            if (department.DepartmentId == Guid.Empty)
            {
                department.DepartmentId = Guid.NewGuid();
            }

            using var connection = _connectionFactory.CreateConnection();

            var parameters = new DynamicParameters();
            parameters.Add("@DepartmentId", department.DepartmentId, DbType.Guid);
            parameters.Add("@DepartmentName", department.DepartmentName, DbType.String);
            parameters.Add("@ManagerId", department.ManagerId, DbType.Guid);

            await connection.ExecuteAsync(
                "sp_CreateDepartment",
                parameters,
                commandType: CommandType.StoredProcedure);

            return department.DepartmentId;
        }

        public async Task<bool> UpdateAsync(Department department)
        {
            using var connection = _connectionFactory.CreateConnection();

            var parameters = new DynamicParameters();
            parameters.Add("@DepartmentId", department.DepartmentId, DbType.Guid);
            parameters.Add("@DepartmentName", department.DepartmentName, DbType.String);
            parameters.Add("@ManagerId", department.ManagerId, DbType.Guid);

            var rowsAffected = await connection.ExecuteAsync(
                "sp_UpdateDepartment",
                parameters,
                commandType: CommandType.StoredProcedure);

            return rowsAffected > 0;
        }

        public async Task<bool> DeleteAsync(Guid departmentId)
        {
            using var connection = _connectionFactory.CreateConnection();

            var parameters = new DynamicParameters();
            parameters.Add("@DepartmentId", departmentId, DbType.Guid);

            var rowsAffected = await connection.ExecuteAsync(
                "sp_DeleteDepartment",
                parameters,
                commandType: CommandType.StoredProcedure);

            return rowsAffected > 0;
        }
    }
}
