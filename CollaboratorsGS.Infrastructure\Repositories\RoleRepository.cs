using CollaboratorsGS.Domain.Entities;
using CollaboratorsGS.Domain.Repositories;
using CollaboratorsGS.Infrastructure.Data;
using Dapper;

namespace CollaboratorsGS.Infrastructure.Repositories
{
    public class RoleRepository : IRoleRepository
    {
        private readonly IConnectionFactory _connectionFactory;

        public RoleRepository(IConnectionFactory connectionFactory)
        {
            _connectionFactory = connectionFactory;
        }

        public async Task<Role?> GetByIdAsync(Guid roleId)
        {
            using var connection = _connectionFactory.CreateConnection();

            var query = "SELECT * FROM Roles WHERE role_id = @RoleId";

            return await connection.QuerySingleOrDefaultAsync<Role>(query, new { RoleId = roleId });
        }

        public async Task<Role?> GetByNameAsync(string roleName)
        {
            using var connection = _connectionFactory.CreateConnection();

            var query = "SELECT * FROM Roles WHERE Role_Name = @RoleName";

            return await connection.QuerySingleOrDefaultAsync<Role>(query, new { RoleName = roleName });
        }

        public async Task<IEnumerable<Role>> GetAllAsync()
        {
            using var connection = _connectionFactory.CreateConnection();

            var query = "SELECT * FROM Roles";

            return await connection.QueryAsync<Role>(query);
        }

        public async Task<IEnumerable<Permission>> GetPermissionsByRoleIdAsync(Guid roleId)
        {
            using var connection = _connectionFactory.CreateConnection();

            var query = @"
                SELECT p.*
                FROM Permissions p
                INNER JOIN role_permissions rp ON p.permission_id = rp.permission_id
                WHERE rp.role_id = @RoleId";

            return await connection.QueryAsync<Permission>(query, new { RoleId = roleId });
        }

        public async Task<Guid> CreateAsync(Role role)
        {
            // Generate a new UUID if not provided
            if (role.RoleId == Guid.Empty)
            {
                role.RoleId = Guid.NewGuid();
            }
            
            using var connection = _connectionFactory.CreateConnection();

            var query = @"
                INSERT INTO roles (role_id, role_name, description)
                VALUES (@RoleId, @RoleName, @Description)";

            await connection.ExecuteAsync(query, role);
            return role.RoleId;
        }

        public async Task<bool> UpdateAsync(Role role)
        {
            using var connection = _connectionFactory.CreateConnection();

            var query = @"
                UPDATE roles
                SET role_name = @RoleName,
                    description = @Description
                WHERE role_id = @Role_Id";

            var rowsAffected = await connection.ExecuteAsync(query, role);
            return rowsAffected > 0;
        }

        public async Task<bool> DeleteAsync(Guid roleId)
        {
            using var connection = _connectionFactory.CreateConnection();

            // First check if the role is used by any users
            var checkUsersQuery = "SELECT COUNT(1) FROM users WHERE role_id = @RoleId";
            var usersCount = await connection.ExecuteScalarAsync<int>(checkUsersQuery, new { RoleId = roleId });

            if (usersCount > 0)
                throw new InvalidOperationException("Cannot delete role because it is assigned to users");

            // Delete role permissions first
            var deleteRolePermissionsQuery = "DELETE FROM role_permissions WHERE role_id = @RoleId";
            await connection.ExecuteAsync(deleteRolePermissionsQuery, new { RoleId = roleId });

            // Then delete the role
            var deleteRoleQuery = "DELETE FROM roles WHERE role_id = @RoleId";
            var rowsAffected = await connection.ExecuteAsync(deleteRoleQuery, new { RoleId = roleId });

            return rowsAffected > 0;
        }

        
    }
}
