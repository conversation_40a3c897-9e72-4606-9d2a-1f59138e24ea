
namespace CollaboratorsGS.Domain.Entities
{
    public class CollaboratorViolation
    {
        public Guid ViolationId { get; set; }
        public Guid CollaboratorId { get; set; }
        public string ViolationType { get; set; } = string.Empty;
        public string? Description { get; set; }
        public DateTime CreatedAt { get; set; }
        public Guid? HandledBy { get; set; }
        public DateTime? HandledAt { get; set; }
        public string? CollaboratorName { get; set; }
        public string? HandlerName { get; set; }

        // Navigation properties
        public Collaborator? Collaborator { get; set; }
        public User? Handler { get; set; }
    }
}
