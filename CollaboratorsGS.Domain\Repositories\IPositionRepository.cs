using CollaboratorsGS.Domain.Entities;

namespace CollaboratorsGS.Domain.Repositories
{
    public interface IPositionRepository
    {
        Task<Position?> GetByIdAsync(Guid positionId);
        Task<IEnumerable<Position>> GetAllAsync();
        Task<IEnumerable<Position>> GetByDepartmentAsync(Guid departmentId);
        Task<Guid> CreateAsync(Position position);
        Task<bool> UpdateAsync(Position position);
        Task<bool> DeleteAsync(Guid positionId);
    }
}
