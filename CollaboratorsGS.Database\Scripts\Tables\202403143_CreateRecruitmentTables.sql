-- Create Recruitment related tables

-- Create Positions table
IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'positions' AND schema_id = SCHEMA_ID('dbo'))
BEGIN
CREATE TABLE [dbo].[Positions] (
    [position_id] UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
    [position_name] NVARCHAR(255) NOT NULL,
    [department_id] UNIQUEIDENTIFIER NOT NULL,
    [description] NVARCHAR(255),
    [branch_id] UNIQUEIDENTIFIER NULL,
    CONSTRAINT [fk_positions_departments] FOREIGN KEY ([department_id]) REFERENCES [dbo].[departments] ([department_id]),
    CONSTRAINT [fk_positions_branches] FOREIGN KEY ([branch_id]) REFERENCES [dbo].[branches] ([branch_id])
);
END
GO


-- Create recruitment_postings table
IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'recruitment_postings' AND schema_id = SCHEMA_ID('dbo'))
BEGIN
  CREATE TABLE recruitment_postings (
    posting_id UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),             -- Primary key, unique identifier for the posting
    refer_code NVARCHAR(50) NOT NULL,                                    -- Posting code (e.g., JOB20240612)
    title NVARCHAR(255) NOT NULL,                                        -- Job title

    project NVARCHAR(255) NULL,                                          -- Project name (if applicable)
    [level] NVARCHAR(50) NULL,                                           -- Job level (fresher, junior, ...)
    position NVARCHAR(100) NULL,                                         -- Position's ObjectCode from ObjectData

    salary_from INT NULL,                                               -- Minimum salary
    salary_to INT NULL,                                                 -- Maximum salary
    commission INT NULL,                                                -- Referral commission
    commission_warranty_months INT NULL,                                -- Warranty period in months for referred candidate

    working_location NVARCHAR(255) NULL,                                -- Work location
    working_time NVARCHAR(100) NULL,                                    -- Working hours (e.g., Mon–Fri, 9 AM–6 PM)

    view_count INT DEFAULT 0,                                           -- Number of views
    referral_count INT DEFAULT 0,                                       -- Number of referrals

    is_urgent BIT DEFAULT 0,                                            -- Is this posting urgent?
    is_hot BIT DEFAULT 0,                                               -- Is this a hot job post?
    is_saved BIT DEFAULT 0,                                             -- Has this post been saved by a user (e.g., for dashboard)?

    status TINYINT DEFAULT 1,                                           -- Posting status: 1=active, 2=paused, 3=expired, etc.

    job_detail_json NVARCHAR(MAX) NULL,                                 -- Job description, requirements, and benefits in JSON format

    -- Metadata
    created_at DATETIME DEFAULT SYSUTCDATETIME(),                       -- Created date
    updated_at DATETIME NULL,                                           -- Last updated date
    expired_at DATETIME NULL                                            -- Expiration date
  );
END


END
GO
IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'object_data' AND schema_id = SCHEMA_ID('dbo'))
CREATE TABLE object_data (
    object_id UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),              -- Primary key

    object_type NVARCHAR(100) NOT NULL,        -- Object type (e.g., 'recruitment_position')
    object_code NVARCHAR(100) NULL,            -- Code (optional)
    object_value NVARCHAR(MAX) NOT NULL,       -- Display value (e.g., 'Sales staff')

    description NVARCHAR(500) NULL,            -- Additional description (optional)

    created_at DATETIME DEFAULT SYSUTCDATETIME(), -- Created timestamp
    updated_at DATETIME NULL                       -- Last updated timestamp
);


