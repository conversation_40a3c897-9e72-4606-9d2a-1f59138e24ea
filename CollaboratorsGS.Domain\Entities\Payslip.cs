using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace CollaboratorsGS.Domain.Entities
{
    public class Payslip
    {
        public int year { get; set; }
        public int month { get; set; }
        public DataTable PayslipConfig { get; set; }
        public List<PayslipDetail> TimeSheet { get; set; }
        public List<Salarydetail> salary { get; set; }

        public DataTable ConvertToDataTable(List<Salarydetail> list)
        {
            DataTable table = new DataTable();

            table.Columns.Add("ColumnIndex", typeof(int));
            table.Columns.Add("EmployeeCode", typeof(string));
            table.Columns.Add("ValueColumn", typeof(string));
            table.Columns.Add("headerlevel2", typeof(string));
            table.Columns.Add("headerlevel1", typeof(string));
            table.Columns.Add("configtype", typeof(int));
            table.Columns.Add("type", typeof(int));

            foreach (var item in list)
            {
                table.Rows.Add(
                    item.ColumnIndex,
                    item.EmployeeCode,
                    item.ValueColumn,
                    item.headerlevel2,
                    item.headerlevel1
                // item.configtype,
                // item.type
                );
            }

            return table;
        }
    }

    public class PayslipDetail
    {
        public int ColumnIndex { get; set; }
        public string EmployeeCode { get; set; }
        public string ValueColumn { get; set; }
        public DateTime Date { get; set; }
        public string headerlevel2 { get; set; }
        public string headerlevel1 { get; set; }
        public int payrollId { get; set; }
    }

    public class Salarydetail
    {
        public int ColumnIndex { get; set; }
        public string EmployeeCode { get; set; }
        public string ValueColumn { get; set; }
        public DateTime Date { get; set; }
        public string headerlevel2 { get; set; }
        public string headerlevel1 { get; set; }
        public int payrollId { get; set; }
        public int configtype { get; set; }
        public int type { get; set; }
    }

    public class Payroll
    {
        
        public int payrollId { get; set; }

        public string payrollName { get; set; } = string.Empty;
        
        public int Id {get; set; }
        public int ColumnIndex { get; set; }
        public string EmployeeCode { get; set; }
        public string ValueColumn { get; set; }
        public DateTime Date { get; set; }
        public int vesion { get; set; } 
        public string benefitName { get; set; }
        public string benefitPhone { get; set; }
        public string headerlevel2 { get; set; }
        public string headerlevel1 { get; set; }
    }
}
