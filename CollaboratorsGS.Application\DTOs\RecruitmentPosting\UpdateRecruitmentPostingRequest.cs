using System;
using System.ComponentModel.DataAnnotations;
using CollaboratorsGS.Domain.Enums;

namespace CollaboratorsGS.Application.DTOs.RecruitmentPosting
{
    public class UpdateRecruitmentPostingRequest
    {
        [StringLength(50)]
        public string? refer_code { get; set; }

        [StringLength(255)]
        public string? title { get; set; }

        [StringLength(255)]
        public string? project { get; set; }

        [StringLength(50)]
        public string? level { get; set; }

        [StringLength(100)]
        public string? position { get; set; } // ObjectCode from ObjectData

        public int? salary_from { get; set; }
        public int? salary_to { get; set; }
        public int? commission { get; set; }
        public int? commission_warranty_months { get; set; }

        [StringLength(255)]
        public string? working_location { get; set; }

        [StringLength(100)]
        public string? working_time { get; set; }

        public bool? is_urgent { get; set; }
        public bool? is_hot { get; set; }

        public RecruitmentPostingStatus? status { get; set; }

        public string? job_detail_json { get; set; }

        public DateTime? expired_at { get; set; }
    }
}
