
namespace CollaboratorsGS.Domain.Entities
{
    public class Position
    {
        public Guid PositionId { get; set; }
        public string PositionName { get; set; } = string.Empty;
        public Guid DepartmentId { get; set; }
        public string? Description { get; set; }
        public string? DepartmentName { get; set; }

        // Navigation properties
        public Department? Department { get; set; }
        public ICollection<RecruitmentPosting>? RecruitmentPostings { get; set; }
    }
}
