-- Working version with quarterly history

IF EXISTS (SELECT * FROM sys.objects WHERE type = 'P' AND name = 'sp_GetCollaboratorKpiDetail')
    DROP PROCEDURE sp_GetCollaboratorKpiDetail
GO

CREATE PROCEDURE sp_GetCollaboratorKpiDetail
    @UserId UNIQUEIDENTIFIER
AS
BEGIN
    SET NOCOUNT ON;
    
    DECLARE @CollaboratorId UNIQUEIDENTIFIER
    DECLARE @CurrentPeriod NVARCHAR(7) = FORMAT(GETDATE(), 'yyyy-MM')
    DECLARE @StartDate DATE = DATEFROMPARTS(YEAR(GETDATE()), MONTH(GETDATE()), 1)
    DECLARE @EndDate DATE = EOMONTH(GETDATE())
    DECLARE @DaysRemaining INT = DATEDIFF(DAY, GETDATE(), @EndDate)

    -- Get collaborator ID
    SELECT @CollaboratorId = collaborator_id
    FROM collaborators
    WHERE user_id = @UserId

    -- If no collaborator found
    IF @CollaboratorId IS NULL
    BEGIN
        SELECT '' as period, CAST('1900-01-01' AS DATE) as start_date, CAST('1900-01-01' AS DATE) as end_date, 0 as days_remaining, 0 as target_onboard, 0 as achieved_onboard, CAST(0 AS DECIMAL(10,2)) as success_percentage, N'Không đạt' as status, CAST(0 AS DECIMAL(18,2)) as total_reward
        SELECT N'Ứng viên onboard' as title, 0 as count, 0 as target, CAST(0 AS DECIMAL(10,2)) as percentage WHERE 1 = 0
        SELECT '' as period, 0 as target_onboard, 0 as achieved_onboard, N'Không đạt' as status, CAST(0 AS DECIMAL(18,2)) as total_reward WHERE 1 = 0
        RETURN
    END

    -- Get current KPI data
    DECLARE @OnboardCount INT = 0
    DECLARE @Round1Count INT = 0
    DECLARE @ImportCount INT = 0

    SELECT 
        @OnboardCount = ISNULL(total_candidates_onboarded, 0),
        @Round1Count = ISNULL(total_candidates_passed_round1, 0),
        @ImportCount = ISNULL(total_candidates_imported, 0)
    FROM collaborator_kpis 
    WHERE collaborator_id = @CollaboratorId 
        AND period = @CurrentPeriod

    -- 1. Current Period
    SELECT 
        @CurrentPeriod as period,
        @StartDate as start_date,
        @EndDate as end_date,
        @DaysRemaining as days_remaining,
        10 as target_onboard,
        @OnboardCount as achieved_onboard,
        CAST(@OnboardCount * 100.0 / 10 AS DECIMAL(10,2)) as success_percentage,
        CASE WHEN @OnboardCount >= 10 THEN N'Đạt' ELSE N'Không đạt' END as status,
        CAST(0 AS DECIMAL(18,2)) as total_reward

    -- 2. Progress Details (exactly 3 rows)
    SELECT N'Ứng viên onboard' as title, @OnboardCount as count, 10 as target, CAST(@OnboardCount * 100.0 / 10 AS DECIMAL(10,2)) as percentage
    UNION ALL
    SELECT N'Ứng viên qua vòng 1', @Round1Count, 15, CAST(@Round1Count * 100.0 / 15 AS DECIMAL(10,2))
    UNION ALL
    SELECT N'Ứng viên import', @ImportCount, 20, CAST(@ImportCount * 100.0 / 20 AS DECIMAL(10,2))

    -- 3. KPI History (quarterly)
    SELECT 
    CONCAT('Q', quarter, '/', year) as period,
    30 as target_onboard,
    SUM(ISNULL(total_candidates_onboarded, 0)) as achieved_onboard,
    CASE WHEN SUM(ISNULL(total_candidates_onboarded, 0)) >= 30 THEN N'Đạt' ELSE N'Không đạt' END as status,
    CAST(0 AS DECIMAL(18,2)) as total_reward
FROM (
    SELECT 
        total_candidates_onboarded,
        CAST(LEFT(period, 4) AS INT) AS year,
        CASE 
            WHEN RIGHT(period, 2) IN ('01', '02', '03') THEN 1
            WHEN RIGHT(period, 2) IN ('04', '05', '06') THEN 2
            WHEN RIGHT(period, 2) IN ('07', '08', '09') THEN 3
            ELSE 4
        END AS quarter
    FROM collaborator_kpis
    WHERE collaborator_id = @CollaboratorId
        AND period IS NOT NULL
        AND period != 'Pend'
        AND LEN(period) = 7
        AND period LIKE '____-__'
        AND period >= FORMAT(DATEADD(MONTH, -15, GETDATE()), 'yyyy-MM')
) AS KPIWithQuarter
GROUP BY year, quarter
ORDER BY year DESC, quarter DESC
END
GO
