-- Tạo bảng companies và branches, cập nhật bảng departments và positions


-- Tạo bảng branches
IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'branches' AND schema_id = SCHEMA_ID('dbo'))
BEGIN
CREATE TABLE [dbo].[branches] (
    [branch_id] UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
    [company_id] UNIQUEIDENTIFIER NOT NULL,
    [branch_name] NVARCHAR(255) NOT NULL,
    [phone_number] NVARCHAR(20),
    [email] NVARCHAR(255),
    [created_at] DATETIME DEFAULT GETDATE(),
    [updated_at] DATETIME,
    CONSTRAINT [fk_branches_companies] FOREIGN KEY ([company_id]) REFERENCES [dbo].[companies] ([company_id])
);
END
GO


-- Cập nhật bảng Departments
IF EXISTS (SELECT * FROM sys.tables WHERE name = 'departments' AND schema_id = SCHEMA_ID('dbo'))
BEGIN
    -- <PERSON><PERSON><PERSON> tra xem cột branch_id đã tồn tại chưa
    IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('dbo.departments') AND name = 'branch_id')
    BEGIN
        -- Thêm cột branch_id
        ALTER TABLE [dbo].[departments]
        ADD [branch_id] UNIQUEIDENTIFIER NULL;

        -- Thêm ràng buộc khóa ngoại
        -- ALTER TABLE [dbo].[Departments]
        -- ADD CONSTRAINT [FK_Departments_Branches] FOREIGN KEY ([BranchId]) REFERENCES [dbo].[Branches] ([BranchId]);
    END
END
GO

-- Cập nhật bảng Positions
IF EXISTS (SELECT * FROM sys.tables WHERE name = 'Positions' AND schema_id = SCHEMA_ID('dbo'))
BEGIN
    -- Kiểm tra xem cột branch_id đã tồn tại chưa
    IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('dbo.Positions') AND name = 'branch_id')
    BEGIN
        -- Thêm cột branch_id
        ALTER TABLE [dbo].[Positions]
        ADD [branch_id] UNIQUEIDENTIFIER NULL;

        -- Thêm ràng buộc khóa ngoại
        -- ALTER TABLE [dbo].[Positions]
        -- ADD CONSTRAINT [FK_Positions_Branches] FOREIGN KEY ([BranchId]) REFERENCES [dbo].[Branches] ([BranchId]);
    END
END
GO
