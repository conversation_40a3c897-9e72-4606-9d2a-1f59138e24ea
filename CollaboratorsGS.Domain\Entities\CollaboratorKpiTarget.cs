
namespace CollaboratorsGS.Domain.Entities
{
    public class CollaboratorKpiTarget
    {
        public Guid TargetId { get; set; }
        public Guid CollaboratorId { get; set; }
        public string Period { get; set; } = string.Empty;
        public int TargetCandidatesImported { get; set; } = 0;
        public int TargetCandidatesPassedRound1 { get; set; } = 0;
        public int TargetCandidatesOnboarded { get; set; } = 0;
        public DateTime CreatedAt { get; set; }
        public DateTime? UpdatedAt { get; set; }
        public string? CollaboratorName { get; set; }

        // Navigation properties
        public Collaborator? Collaborator { get; set; }
    }
}
