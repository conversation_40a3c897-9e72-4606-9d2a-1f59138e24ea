<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>Exe</OutputType>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <IsAspireHost>true</IsAspireHost>
    <UserSecretsId>3c2b5da9-4b95-48d7-bc78-84214cae744a</UserSecretsId>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Aspire.Hosting.AppHost" Version="8.2.2" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\CollaboratorsGS.API\CollaboratorsGS.API.csproj" />
    <ProjectReference Include="..\CollaboratorsGS.ServiceDefaults\CollaboratorsGS.ServiceDefaults.csproj">
      <IsAspireProjectResource>false</IsAspireProjectResource>
    </ProjectReference>
  </ItemGroup>

</Project>
