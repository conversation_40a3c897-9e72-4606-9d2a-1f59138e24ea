using System;
using System.ComponentModel.DataAnnotations;

namespace CollaboratorsGS.Application.DTOs.CandidateDocument
{
    public class CreateCandidateDocumentRequest
    {
        [Required]
        public Guid CandidateId { get; set; }
        
        [Required]
        [StringLength(50)]
        public string DocumentType { get; set; } = string.Empty;
        
        [Required]
        public string FilePath { get; set; } = string.Empty;
        
  
    }
}