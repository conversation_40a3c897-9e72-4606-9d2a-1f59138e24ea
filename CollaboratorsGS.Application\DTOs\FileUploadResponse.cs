namespace CollaboratorsGS.Application.DTOs
{
    public class FileUploadResponse
    {
        public bool IsSuccess { get; set; }
        public string? Message { get; set; }
        public string? FileUrl { get; set; }
        public string? FileName { get; set; }
        public string? FileId { get; set; }
        public long FileSize { get; set; }
        public string? ContentType { get; set; }
        public string? DocumentType { get; set; } // image, document, text, other
        public string? FileType { get; set; } // pdf, png, jpg, docx, txt, etc.
    }
}
