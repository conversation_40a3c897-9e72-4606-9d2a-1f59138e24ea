using System;
using OfficeOpenXml;
using System.IO;

namespace TestMergeCells
{
    class Program
    {
        static void Main(string[] args)
        {
            Console.WriteLine("Testing merge cells fix...");
            
            // Test the SafeMergeCells logic
            try
            {
                using (var package = new ExcelPackage())
                {
                    var worksheet = package.Workbook.Worksheets.Add("Test");
                    
                    // Test case 1: Normal merge - should work
                    SafeMergeCells(worksheet, 1, 1, 1, 2);
                    Console.WriteLine("✓ Test 1 passed: Normal merge");
                    
                    // Test case 2: Try to merge overlapping range - should be prevented
                    SafeMergeCells(worksheet, 1, 2, 1, 3); // This overlaps with previous merge
                    Console.WriteLine("✓ Test 2 passed: Overlapping merge prevented");
                    
                    // Test case 3: Non-overlapping merge - should work
                    SafeMergeCells(worksheet, 2, 1, 2, 2);
                    Console.WriteLine("✓ Test 3 passed: Non-overlapping merge");
                    
                    Console.WriteLine("All tests passed! The merge cells fix is working correctly.");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Test failed: {ex.Message}");
            }
        }
        
        /// <summary>
        /// Check if a cell range is already merged to avoid merge conflicts
        /// </summary>
        private static bool IsCellRangeMerged(ExcelWorksheet sheet, string rangeAddress)
        {
            try
            {
                var targetRange = new ExcelAddress(rangeAddress);
                foreach (var mergedRange in sheet.MergedCells)
                {
                    var existingRange = new ExcelAddress(mergedRange);
                    // Check if ranges overlap
                    if (targetRange.Start.Row <= existingRange.End.Row && 
                        targetRange.End.Row >= existingRange.Start.Row &&
                        targetRange.Start.Column <= existingRange.End.Column && 
                        targetRange.End.Column >= existingRange.Start.Column)
                    {
                        return true;
                    }
                }
                return false;
            }
            catch
            {
                // If there's any error checking, assume it's safe to merge
                return false;
            }
        }

        /// <summary>
        /// Safely merge cells with conflict checking
        /// </summary>
        private static void SafeMergeCells(ExcelWorksheet sheet, int startRow, int startCol, int endRow, int endCol)
        {
            try
            {
                var rangeAddress = $"{ExcelCellAddress.GetColumnLetter(startCol)}{startRow}:{ExcelCellAddress.GetColumnLetter(endCol)}{endRow}";
                if (!IsCellRangeMerged(sheet, rangeAddress))
                {
                    using (var range = sheet.Cells[startRow, startCol, endRow, endCol])
                    {
                        range.Merge = true;
                    }
                }
            }
            catch (Exception ex)
            {
                // Log the error but don't throw to prevent breaking the entire process
                Console.WriteLine($"Warning: Error merging cells: {ex.Message}");
            }
        }
    }
}
