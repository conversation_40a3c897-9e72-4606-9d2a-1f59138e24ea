-- Create Reward related tables

-- Create CtvRewards table
IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'collaborator_rewards' AND schema_id = SCHEMA_ID('dbo'))
BEGIN
CREATE TABLE [dbo].[collaborator_rewards] (
 [reward_id] UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
    [collaborator_id] UNIQUEIDENTIFIER NOT NULL,
    [application_id] UNIQUEIDENTIFIER NOT NULL,
    [reward_type] NVARCHAR(50) NOT NULL,
    [amount] DECIMAL(18, 2) NOT NULL,
    [level_id] UNIQUEIDENTIFIER NOT NULL,
    [reward_date] DATE NOT NULL DEFAULT GETDATE(),
    [scheduled_payment_date] DATE NOT NULL,
    [status] NVARCHAR(50) NOT NULL,
    CONSTRAINT [fk_ctv_rewards_collaborators] FOREIGN KEY ([collaborator_id]) REFERENCES [dbo].[collaborators] ([collaborator_id]),
    CONSTRAINT [fk_ctv_rewards_applications] FOREIGN KEY ([application_id]) REFERENCES [dbo].[candidate_applications] ([application_id]),
    CONSTRAINT [fk_ctv_rewards_level] FOREIGN KEY ([level_id]) REFERENCES [dbo].[collaborator_levels] ([level_id])
);
END
GO

-- Create CtvRewardHistory table
IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'collaborator_reward_history' AND schema_id = SCHEMA_ID('dbo'))
BEGIN
CREATE TABLE [dbo].[collaborator_reward_history] (
    [history_id] UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
    [collaborator_id] UNIQUEIDENTIFIER NOT NULL,
    [reward_id] UNIQUEIDENTIFIER NOT NULL,
    [amount] DECIMAL(18, 2) NOT NULL,
    [payment_date] DATE NOT NULL,
    [payment_method] NVARCHAR(50) NOT NULL,
    [status] NVARCHAR(50) NOT NULL,
    CONSTRAINT [fk_ctv_reward_history_collaborators] FOREIGN KEY ([collaborator_id]) REFERENCES [dbo].[collaborators] ([collaborator_id]),
    CONSTRAINT [fk_ctv_reward_history_rewards] FOREIGN KEY ([reward_id]) REFERENCES [dbo].[collaborator_rewards] ([reward_id])
);
END
GO