using CollaboratorsGS.Application.Constants;
using CollaboratorsGS.Application.DTOs;
using CollaboratorsGS.Application.DTOs.ObjectData;
using CollaboratorsGS.Application.Interfaces;
using CollaboratorsGS.Domain.Contants;
using CollaboratorsGS.Infrastructure.Utilities;
using Google.Protobuf.WellKnownTypes;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace CollaboratorsGS.API.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class ObjectDataController : ControllerBase
    {
        private readonly IObjectDataService _objectDataService;
        private readonly ILogger<ObjectDataController> _logger;

        public ObjectDataController(
            IObjectDataService objectDataService,
            ILogger<ObjectDataController> logger)
        {
            _objectDataService = objectDataService;
            _logger = logger;
        }

        // GET: api/ObjectData
        [HttpGet]
        public async Task<IActionResult> GetAll()
        {
            try
            {
                var objectDataList = await _objectDataService.GetAllAsync();
                return Ok(ApiResponse<object>.SuccessResponse(
                        MessageCodes.SC2000,
                        "Get all object data successfully",
                        objectDataList,
                        200));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting all object data");
                return StatusCode(500, ApiResponse<object>.ErrorResponse(
                        MessageCodes.ER5000,
                        "Internal server error" +  ex.Message,
                        500));
            }
        }

        // GET: api/ObjectData/{id}
        [HttpGet("{id}")]
        public async Task<IActionResult> GetById(Guid id)
        {
            try
            {
                var objectData = await _objectDataService.GetByIdAsync(id);

                if (objectData == null)
                    return NotFound(ApiResponse<object>.ErrorResponse(
                        MessageCodes.ER4004,
                        "Not Found object data by id",
                        404));

                return Ok(ApiResponse<object>.SuccessResponse(
                    MessageCodes.SC2000,
                    "Get object data by id successfully",
                    objectData,
                    200));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting object data with ID {ObjectId}", id);
                return StatusCode(500, ApiResponse<object>.ErrorResponse(
                        MessageCodes.ER5000,
                        "Internal server error" +  ex.Message,
                        500));
            }
        }

        // GET: api/ObjectData/type/{objectType}
        [HttpGet("type/{objectType}")]
        public async Task<IActionResult> GetByType(string objectType)
        {
            try
            {
                var objectDataList = await _objectDataService.GetByTypeAsync(objectType);
                return Ok(ApiResponse<object>.SuccessResponse(
                        MessageCodes.SC2000,
                        "Get object data by type successfully",
                        objectDataList,
                        200));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting object data by type {ObjectType}", objectType);
                return StatusCode(500, ApiResponse<object>.ErrorResponse(
                        MessageCodes.ER5000,
                        "Internal server error" +  ex.Message,
                        500));
            }
        }

        // GET: api/ObjectData/code/{objectCode}
        [HttpGet("code/{objectCode}")]
        public async Task<IActionResult> GetByCode(string objectCode)
        {
            try
            {
                var objectData = await _objectDataService.GetByCodeAsync(objectCode);

                if (objectData == null)
                  return NotFound(ApiResponse<object>.ErrorResponse(
                        MessageCodes.ER4004,
                        $"No collaborator found for user ID: {objectCode}",
                        404));

                return Ok(ApiResponse<object>.SuccessResponse(
                    MessageCodes.SC2000,
                    "Get object data by Objectcode successfully",
                    objectData,
                    200));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting object data by code {ObjectCode}", objectCode);
                return StatusCode(500, ApiResponse<object>.ErrorResponse(
                        MessageCodes.ER5000,
                        "Internal server error" +  ex.Message,
                        500));
            }
        }

        // GET: api/ObjectData/value/{objectValue}
        [HttpGet("value/{objectValue}")]
        public async Task<IActionResult> GetByValue(string objectValue)
        {
            try
            {
                var objectDataList = await _objectDataService.GetByValueAsync(objectValue);
                return Ok(ApiResponse<object>.SuccessResponse(
                        MessageCodes.SC2000,
                        "Get object data by value successfully",
                        objectDataList,
                        200));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting object data by value {ObjectValue}", objectValue);
                return StatusCode(500, ApiResponse<object>.ErrorResponse(
                        MessageCodes.ER5000,
                        "Internal server error" +  ex.Message,
                        500));
            }
        }

        // POST: api/ObjectData
        [HttpPost]
        [Authorize(Roles = RolesUser.AdminManager)]
        public async Task<IActionResult> Create([FromBody] CreateObjectDataRequest request)
        {
            try
            {
                var objectId = await _objectDataService.CreateAsync(request);
                return CreatedAtAction(nameof(GetById), new { id = objectId },
                 ApiResponse<object>.SuccessResponse(
                        MessageCodes.SC2001,
                        "Object data created successfully",
                        201));
            }
            catch (InvalidOperationException ex)
            {
                _logger.LogWarning(ex, "Validation error creating object data");
                return BadRequest(ApiResponse<object>.ErrorResponse(
                    MessageCodes.ER4005,
                    ex.Message,
                    400,
                    new List<ErrorDetail>
                    {
                        new ErrorDetail
                        {
                            Field = "request",
                            ErrorCode = MessageCodes.ER4005,
                            Message = ex.Message
                        }
                    }));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating object data");
                return StatusCode(500, ApiResponse<object>.ErrorResponse(
                    MessageCodes.ER5000,
                    "Internal server error",
                    500));
            }
        }

        // PUT: api/ObjectData/{id}
        [HttpPut("{id}")]
        [Authorize(Roles = RolesUser.AdminManager)]
        public async Task<IActionResult> Update(Guid id, [FromBody] UpdateObjectDataRequest request)
        {
            try
            {
                var result = await _objectDataService.UpdateAsync(id, request);

                if (!result)
                    return NotFound(ApiResponse<object>.ErrorResponse(
                        MessageCodes.ER4004,
                        $"No object data found for ID: {id}",
                        404));

                return Ok(ApiResponse<object>.SuccessResponse(
                    MessageCodes.SC2002,
                    "Get collaborator by user ID successfully",
                    200));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating object data with ID {ObjectId}", id);
                return StatusCode(500,ApiResponse<object>.ErrorResponse(
                    MessageCodes.ER5000,
                    "Internal server error",
                    500));
            }
        }

        // DELETE: api/ObjectData/{id}
        [HttpDelete("{id}")]
        [Authorize(Roles = RolesUser.AdminManager)]
        public async Task<IActionResult> Delete(Guid id)
        {
            try
            {
                var result = await _objectDataService.DeleteAsync(id);

                if (!result)
                    return NotFound(ApiResponse<object>.ErrorResponse(
                        MessageCodes.ER4004,
                        $"No object data found for ID: {id}",
                        404));

                return Ok(ApiResponse<object>.SuccessResponse(
                    MessageCodes.SC2003,
                    "Delete object data successfully",
                    200));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting object data with ID {ObjectId}", id);
                return StatusCode(500, ApiResponse<object>.ErrorResponse(
                    MessageCodes.ER5000,
                    "Internal server error",
                    500));
            }
        }
    }
}
