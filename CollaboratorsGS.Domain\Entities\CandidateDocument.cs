using System;
using System.ComponentModel.DataAnnotations.Schema;

namespace CollaboratorsGS.Domain.Entities
{
    public class CandidateDocument
    {
        public Guid DocumentId { get; set; }
        public Guid CandidateId { get; set; }
        public string DocumentType { get; set; } = string.Empty;
        public string FilePath { get; set; } = string.Empty;
        public string FileType { get; set; } = string.Empty;
        public DateTime UploadedAt { get; set; }

        // Navigation property
        public Candidate? Candidate { get; set; }
    }
}
