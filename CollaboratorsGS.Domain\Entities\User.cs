using System.ComponentModel.DataAnnotations.Schema;

namespace CollaboratorsGS.Domain.Entities
{
    public class User
    {
        public Guid UserId { get; set; }

        public string Username { get; set; } = string.Empty;

        public string Password { get; set; } = string.Empty;

        public string? PasswordHash { get; set; }
    
        public Guid RoleId { get; set; }

        public string? FullName { get; set; }

        public string? Email { get; set; }

        public string? PhoneNumber { get; set; }

        public DateTime CreatedAt { get; set; }

        public DateTime? LastLogin { get; set; }

        public bool IsActive { get; set; }

        public int? Group { get; set; }

        public string? Type { get; set; }

        public int EmployeeId { get; set; }

        // Navigation properties
        public Role? Role { get; set; }
        public ICollection<Authen>? AuthTokens { get; set; }
        public ICollection<PasswordResetToken>? PasswordResetTokens { get; set; }
        public ICollection<AuditLogs>? AuditLogs { get; set; }
    }
}
