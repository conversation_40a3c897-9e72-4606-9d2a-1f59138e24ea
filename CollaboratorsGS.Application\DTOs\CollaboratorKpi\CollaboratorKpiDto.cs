using System;

namespace CollaboratorsGS.Application.DTOs.CollaboratorKpi
{
    public class CollaboratorKpiDto
    {
        public Guid KpiId { get; set; }
        public Guid CollaboratorId { get; set; }
        public string CollaboratorName { get; set; } = string.Empty;
        public string Period { get; set; } = string.Empty;
        public int TotalCandidatesImported { get; set; }
        public int TotalCandidatesPassedRound1 { get; set; }
        public int TotalCandidatesPassedRound2 { get; set; }
        public int TotalCandidatesOnboarded { get; set; }
        public int TotalCandidatesFailed { get; set; }
        public int TotalCandidatesOnboardedWarranty { get; set; }
        public float? SuccessRate { get; set; }
        public DateTime CalculatedAt { get; set; }
    }

    public class CollaboratorKpiReport
    {
        public int TargetCandidatesOnboarded { get; set; }

        public int CurrentCandidatesOnboarded { get; set; }

        public int DateRemaining { get; set; }

        public int TotalCandidatesImported { get; set; }

        public int TotalCandidatesPassedRound1 { get; set; }

        public int TotalCandidatesPassedRound2 { get; set; }

        public int TotalCandidatesFailed { get; set; }

        public int TotalCandidatesApplication { get; set; }

        public string EvaluationPeriod { get; set; } = string.Empty;

        public decimal Bonus { get; set; }
        
        public string Result { get; set; } = string.Empty;

    }
}
