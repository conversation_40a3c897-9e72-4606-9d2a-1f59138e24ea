using System;

namespace CollaboratorsGS.Application.DTOs.CollaboratorKpiTarget
{
    public class CollaboratorKpiTargetDto
    {
        public Guid TargetId { get; set; }
        public Guid CollaboratorId { get; set; }
        public string CollaboratorName { get; set; } = string.Empty;
        public string Period { get; set; } = string.Empty;
        public int TargetCandidatesImported { get; set; }
        public int TargetCandidatesPassedRound1 { get; set; }
        public int TargetCandidatesOnboarded { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime? UpdatedAt { get; set; }
    }
}
