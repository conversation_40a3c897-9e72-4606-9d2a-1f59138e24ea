using CollaboratorsGS.Application.DTOs;
using CollaboratorsGS.Domain.Repositories;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Threading.Tasks;

namespace CollaboratorsGS.Infrastructure.Services
{
    public class CvScannerService
    {
        private readonly HttpClient _httpClient;
        private readonly IConfiguration _configuration;
        private readonly ICandidateRepository _candidateRepository;
        private readonly ILogger<CvScannerService> _logger;

        public CvScannerService(
            HttpClient httpClient,
            IConfiguration configuration,
            ICandidateRepository candidateRepository,
            ILogger<CvScannerService> logger)
        {
            _httpClient = httpClient;
            _configuration = configuration;
            _candidateRepository = candidateRepository;
            _logger = logger;
            
            // Cấu hình base address từ appsettings.json
            _httpClient.BaseAddress = new Uri(_configuration["ScannerEndpoint"] ?? "https://api-extract.greenspeed.vn");
        }

        public async Task<CvScanResponse> ScanCvAsync(byte[] fileBytes, string fileName)
        {
            try
            {
                // Tạo form content để gửi file
                using var formContent = new MultipartFormDataContent();
                var fileContent = new ByteArrayContent(fileBytes);
                
                // Xác định content type dựa trên phần mở rộng của file
                string contentType = "application/octet-stream";
                if (fileName.EndsWith(".pdf", StringComparison.OrdinalIgnoreCase))
                    contentType = "application/pdf";
                else if (fileName.EndsWith(".docx", StringComparison.OrdinalIgnoreCase))
                    contentType = "application/vnd.openxmlformats-officedocument.wordprocessingml.document";
                else if (fileName.EndsWith(".doc", StringComparison.OrdinalIgnoreCase))
                    contentType = "application/msword";
                else if (fileName.EndsWith(".jpg", StringComparison.OrdinalIgnoreCase) || fileName.EndsWith(".jpeg", StringComparison.OrdinalIgnoreCase))
                    contentType = "image/jpeg";
                else if (fileName.EndsWith(".png", StringComparison.OrdinalIgnoreCase))
                    contentType = "image/png";
                
                fileContent.Headers.ContentType = new MediaTypeHeaderValue(contentType);
                formContent.Add(fileContent, "cv_files", fileName);  // Changed "file" to "cv_files"

                // Gửi request đến API scan CV
                var response = await _httpClient.PostAsync("/cv/scan-cv", formContent);
                
                // Đọc response
                var responseContent = await response.Content.ReadAsStringAsync();
                
                if (!response.IsSuccessStatusCode)
                {
                    _logger.LogError($"Error scanning CV: {responseContent}");
                    return new CvScanResponse
                    {
                        IsSuccess = false,
                        Message = $"Error scanning CV: {response.StatusCode}",
                        StatusCode = (int)response.StatusCode
                    };
                }
                
                // Parse JSON response
                var scanResult = JsonConvert.DeserializeObject<CvScanApiResponse>(responseContent);
                
                if (scanResult == null || scanResult.Results == null || scanResult.Results.Count == 0)
                {
                    return new CvScanResponse
                    {
                        IsSuccess = false,
                        Message = "No data extracted from CV",
                        StatusCode = 400
                    };
                }
                
                // Lấy thông tin từ kết quả scan
                var candidateInfo = scanResult.Results[0];
                
                // Kiểm tra trùng lặp trong database
                var duplicateCheckResult = await CheckDuplicateCandidateAsync(
                    candidateInfo.FullName,
                    candidateInfo.Email,
                    candidateInfo.Phone);
                
                return new CvScanResponse
                {
                    IsSuccess = !duplicateCheckResult.IsDuplicate,
                    Message = duplicateCheckResult.Message,
                    StatusCode = duplicateCheckResult.IsDuplicate ? 409 : 200,
                    FieldDuplicate = duplicateCheckResult.FieldDuplicate,
                    CandidateInfo = new CandidateInfo
                    {
                        FullName = candidateInfo.FullName,
                        Email = candidateInfo.Email,
                        PhoneNumber = candidateInfo.Phone,
                        Education = candidateInfo.Education,
                        WorkExperience = candidateInfo.WorkExperience,
                        Skills = candidateInfo.Skills
                    }
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error scanning CV");
                return new CvScanResponse
                {
                    IsSuccess = false,
                    Message = $"Error scanning CV: {ex.Message}",
                    StatusCode = 500
                };
            }
        }

        private async Task<(bool IsDuplicate, string Message, string? FieldDuplicate)> CheckDuplicateCandidateAsync(
            string fullName, string email, string phoneNumber)
        {
            try
            {
                if (!string.IsNullOrWhiteSpace(phoneNumber))
                {
                    var existingByPhone = await _candidateRepository.GetByPhoneNumberAsync(phoneNumber);
                    if (existingByPhone != null)
                    {
                        return (true, $"Candidate with phone number {phoneNumber} already exists", "phoneNumber");
                    }
                }

                if (!string.IsNullOrWhiteSpace(fullName) && !string.IsNullOrWhiteSpace(email))
                {
                    var existingByEmail = await _candidateRepository.GetByEmailAsync(email);
                    if (existingByEmail != null)
                    {
                        return (true, $"Candidate with email {email} already exists", "email");
                    }
                }

                return (false, "No duplicate found", null);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking duplicate candidate");
                throw;
            }
        }


        public async Task<Domain.Entities.Candidate?> CheckCandidateByEmailAsync(string email)
        {
            return await _candidateRepository.GetByEmailAsync(email);
        }
        
        public async Task<Domain.Entities.Candidate?> CheckCandidateByPhoneNumberAsync(string phoneNumber)
        {
            return await _candidateRepository.GetByPhoneNumberAsync(phoneNumber);
        }
    }

    // Classes để parse response từ API scan CV
    public class CvScanApiResponse
    {
        [JsonProperty("results")]
        public List<CvScanResult> Results { get; set; } = new List<CvScanResult>();

        [JsonProperty("message")]
        public string Message { get; set; } = string.Empty;

        [JsonProperty("status_code")]
        public int StatusCode { get; set; }
    }

    public class CvScanResult
    {
        [JsonProperty("full_name")]
        public string FullName { get; set; } = string.Empty;

        [JsonProperty("phone")]
        public string Phone { get; set; } = string.Empty;

        [JsonProperty("email")]
        public string Email { get; set; } = string.Empty;

        [JsonProperty("education")]
        public string Education { get; set; } = string.Empty;

        [JsonProperty("work_experience")]
        public string WorkExperience { get; set; } = string.Empty;

        [JsonProperty("skills")]
        public string Skills { get; set; } = string.Empty;

        [JsonProperty("file_name")]
        public string FileName { get; set; } = string.Empty;
    }
}

