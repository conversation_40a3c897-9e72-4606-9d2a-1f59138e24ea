using CollaboratorsGS.Application.DTOs.AuditLog;

namespace CollaboratorsGS.Application.Interfaces
{
    public interface IActivityLogService
    {
        Task<IEnumerable<RecentActivityDto>> GetRecentActivitiesAsync(Guid collaboratorId, int limit = 10);
        Task LogActivityAsync(Guid userId, string action, string entityType, Guid entityId, string? oldValue = null, string? newValue = null, string? ipAddress = null);
    }
}
