-- Stored Procedures for Company operations

-- Get Company by ID
IF EXISTS (SELECT * FROM sys.objects WHERE type = 'P' AND name = 'sp_GetCompanyById')
    DROP PROCEDURE sp_GetCompanyById
GO

CREATE PROCEDURE sp_GetCompanyById
    @CompanyId UNIQUEIDENTIFIER
AS
BEGIN
    SELECT *
    FROM companies
    WHERE company_id = @CompanyId
END
GO

-- Get All Companies
IF EXISTS (SELECT * FROM sys.objects WHERE type = 'P' AND name = 'sp_GetAllCompanies')
    DROP PROCEDURE sp_GetAllCompanies
GO

CREATE PROCEDURE sp_GetAllCompanies
AS
BEGIN
    SELECT *
    FROM companies
    ORDER BY company_name
END
GO

-- Create Company
IF EXISTS (SELECT * FROM sys.objects WHERE type = 'P' AND name = 'sp_CreateCompany')
    DROP PROCEDURE sp_CreateCompany
GO

CREATE PROCEDURE sp_CreateCompany
    @CompanyId UNIQUEIDENTIFIER,
    @CompanyName NVARCHAR(255),
    @PhoneNumber NVARCHAR(20) = NULL,
    @Email NVARCHAR(255) = NULL
AS
BEGIN
    INSERT INTO companies (company_id, company_name, phone_number, email, created_at, updated_at)
    VALUES (@CompanyId, @CompanyName, @PhoneNumber, @Email, GETDATE(), NULL)

    SELECT @CompanyId AS company_id
END
GO

-- Update Company
IF EXISTS (SELECT * FROM sys.objects WHERE type = 'P' AND name = 'sp_UpdateCompany')
    DROP PROCEDURE sp_UpdateCompany
GO

CREATE PROCEDURE sp_UpdateCompany
    @CompanyId UNIQUEIDENTIFIER,
    @CompanyName NVARCHAR(255),
    @PhoneNumber NVARCHAR(20) = NULL,
    @Email NVARCHAR(255) = NULL
AS
BEGIN
    UPDATE companies
    SET company_name = @CompanyName,
        phone_number = @PhoneNumber,
        email = @Email,
        updated_at = GETDATE()
    WHERE company_id = @CompanyId

    SELECT @@ROWCOUNT
END
GO

-- Delete Company
IF EXISTS (SELECT * FROM sys.objects WHERE type = 'P' AND name = 'sp_DeleteCompany')
    DROP PROCEDURE sp_DeleteCompany
GO

CREATE PROCEDURE sp_DeleteCompany
    @CompanyId UNIQUEIDENTIFIER
AS
BEGIN
    -- Check if company has branches
    IF EXISTS (SELECT 1 FROM branches WHERE company_id = @CompanyId)
    BEGIN
        RAISERROR('Cannot delete company with existing branches', 16, 1)
        RETURN
    END

    DELETE FROM companies
    WHERE company_id = @CompanyId

    SELECT @@ROWCOUNT
END
GO
