-- Stored Procedures for CollaboratorKpiTarget operations

-- Get CollaboratorKpiTarget by ID
IF EXISTS (SELECT * FROM sys.objects WHERE type = 'P' AND name = 'sp_GetCollaboratorKpiTargetById')
    DROP PROCEDURE sp_GetCollaboratorKpiTargetById
GO

CREATE PROCEDURE sp_GetCollaboratorKpiTargetById
    @TargetId UNIQUEIDENTIFIER
AS
BEGIN
    SELECT t.*, ctv.full_name as collaborator_name
    FROM collaborator_kpi_targets t
    INNER JOIN collaborators ctv ON t.collaborator_id = ctv.collaborator_id
    WHERE t.collaborator_targets_id = @TargetId
END
GO

-- Get collaborator_kpi_targets by CollaboratorId
IF EXISTS (SELECT * FROM sys.objects WHERE type = 'P' AND name = 'sp_GetCollaboratorKpiTargetsByCollaboratorId')
    DROP PROCEDURE sp_GetCollaboratorKpiTargetsByCollaboratorId
GO

CREATE PROCEDURE sp_GetCollaboratorKpiTargetsByCollaboratorId
    @CollaboratorId UNI<PERSON><PERSON><PERSON>EN<PERSON>FIER
AS
BEGIN
    SELECT t.*, ctv.full_name as collaborator_name
    FROM collaborator_kpi_targets t
    INNER JOIN collaborators ctv ON t.collaborator_id = ctv.collaborator_id
    WHERE t.collaborator_id = @CollaboratorId
    ORDER BY t.period DESC
END
GO

-- Get collaborator_kpi_targets by Period
IF EXISTS (SELECT * FROM sys.objects WHERE type = 'P' AND name = 'sp_GetCollaboratorKpiTargetsByPeriod')
    DROP PROCEDURE sp_GetCollaboratorKpiTargetsByPeriod
GO

CREATE PROCEDURE sp_GetCollaboratorKpiTargetsByPeriod
    @Period VARCHAR(7)
AS
BEGIN
    SELECT t.*, ctv.full_name as collaborator_name
    FROM collaborator_kpi_targets t
    INNER JOIN collaborators ctv ON t.collaborator_id = ctv.collaborator_id
    WHERE t.period = @Period
    ORDER BY ctv.full_name
END
GO

-- Get All collaborator_kpi_targets
IF EXISTS (SELECT * FROM sys.objects WHERE type = 'P' AND name = 'sp_GetAllCollaboratorKpiTargets')
    DROP PROCEDURE sp_GetAllCollaboratorKpiTargets
GO

CREATE PROCEDURE sp_GetAllCollaboratorKpiTargets
AS
BEGIN
    SELECT t.*, ctv.full_name as collaborator_name
    FROM collaborator_kpi_targets t
    INNER JOIN collaborators ctv ON t.collaborator_id = ctv.collaborator_id
    ORDER BY t.period DESC, ctv.full_name
END
GO

-- Create CollaboratorKpiTarget
IF EXISTS (SELECT * FROM sys.objects WHERE type = 'P' AND name = 'sp_CreateCollaboratorKpiTarget')
    DROP PROCEDURE sp_CreateCollaboratorKpiTarget
GO

CREATE PROCEDURE sp_CreateCollaboratorKpiTarget
    @TargetId UNIQUEIDENTIFIER,
    @CollaboratorId UNIQUEIDENTIFIER,
    @Period VARCHAR(7),
    @TargetCandidatesImported INT = 0,
    @TargetCandidatesPassedRound1 INT = 0,
    @TargetCandidatesOnboarded INT = 0,
    @CreatedAt DATETIME,
    @UpdatedAt DATETIME = NULL
AS
BEGIN
    INSERT INTO collaborator_kpi_targets (
        collaborator_targets_id, collaborator_id, period, target_candidates_imported,
        target_candidates_passed_round1, target_candidates_onboarded,
        created_at, updated_at
    )
    VALUES (
        @TargetId, @CollaboratorId, @Period, @TargetCandidatesImported,
        @TargetCandidatesPassedRound1, @TargetCandidatesOnboarded,
        @CreatedAt, @UpdatedAt
    )
END
GO

-- Update CollaboratorKpiTarget
IF EXISTS (SELECT * FROM sys.objects WHERE type = 'P' AND name = 'sp_UpdateCollaboratorKpiTarget')
    DROP PROCEDURE sp_UpdateCollaboratorKpiTarget
GO

CREATE PROCEDURE sp_UpdateCollaboratorKpiTarget
    @TargetId UNIQUEIDENTIFIER,
    @TargetCandidatesImported INT = NULL,
    @TargetCandidatesPassedRound1 INT = NULL,
    @TargetCandidatesOnboarded INT = NULL,
    @UpdatedAt DATETIME
AS
BEGIN
    UPDATE collaborator_kpi_targets
    SET target_candidates_imported = ISNULL(@TargetCandidatesImported, target_candidates_imported),
        target_candidates_passed_round1 = ISNULL(@TargetCandidatesPassedRound1, target_candidates_passed_round1),
        target_candidates_onboarded = ISNULL(@TargetCandidatesOnboarded, target_candidates_onboarded),
        updated_at = @UpdatedAt
    WHERE collaborator_targets_id = @TargetId
END
GO

-- Delete CollaboratorKpiTarget
IF EXISTS (SELECT * FROM sys.objects WHERE type = 'P' AND name = 'sp_DeleteCollaboratorKpiTarget')
    DROP PROCEDURE sp_DeleteCollaboratorKpiTarget
GO

CREATE PROCEDURE sp_DeleteCollaboratorKpiTarget
    @TargetId UNIQUEIDENTIFIER
AS
BEGIN
    DELETE FROM collaborator_kpi_targets
    WHERE collaborator_targets_id = @TargetId
END
GO

