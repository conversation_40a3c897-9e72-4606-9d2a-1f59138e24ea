
namespace CollaboratorsGS.Domain.Entities
{
    public class CandidateApplication
    {
        public Guid ApplicationId { get; set; }
        public Guid CandidateId { get; set; }
        public string CandidateName { get; set; } = string.Empty;

        public string Title { get; set; } = string.Empty;
        public Guid PostingId { get; set; }
        public DateTime ApplicationDate { get; set; }
        public string Status { get; set; } = string.Empty;
        public string? InterviewRound1Result { get; set; }
        public DateTime? InterviewRound1Date { get; set; }
        public string? InterviewRound2Result { get; set; }
        public DateTime? InterviewRound2Date { get; set; }
        public DateTime? OnboardDate { get; set; }
        public DateTime? WarrantyEndDate { get; set; }
        public DateTime? UpdatedAt { get; set; }


        // Navigation properties
        public Candidate? Candidate { get; set; }
        public RecruitmentPosting? RecruitmentPosting { get; set; }
    }
}
