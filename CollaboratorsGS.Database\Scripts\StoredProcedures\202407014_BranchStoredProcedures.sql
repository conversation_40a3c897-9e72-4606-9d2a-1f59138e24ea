-- Stored Procedures for Branch operations

-- Get Branch by ID
IF EXISTS (SELECT * FROM sys.objects WHERE type = 'P' AND name = 'sp_GetBranchById')
    DROP PROCEDURE sp_GetBranchById
GO

CREATE PROCEDURE sp_GetBranchById
    @BranchId UNIQUEIDENTIFIER
AS
BEGIN
    SELECT b.*, c.company_name
    FROM branches b
    INNER JOIN companies c ON b.company_id = c.company_id
    WHERE b.branch_id = @BranchId
END
GO

-- Get All Branches
IF EXISTS (SELECT * FROM sys.objects WHERE type = 'P' AND name = 'sp_GetAllBranches')
    DROP PROCEDURE sp_GetAllBranches
GO

CREATE PROCEDURE sp_GetAllBranches
AS
BEGIN
    SELECT b.*, c.company_name
    FROM branches b
    INNER JOIN companies c ON b.company_id = c.company_id
    ORDER BY b.branch_name
END
GO

-- Get Branches by Company
IF EXISTS (SELECT * FROM sys.objects WHERE type = 'P' AND name = 'sp_GetBranchesByCompany')
    DROP PROCEDURE sp_GetBranchesByCompany
GO

CREATE PROCEDURE sp_GetBranchesByCompany
    @CompanyId UNIQUEIDENTIFIER
AS
BEGIN
    SELECT b.*, c.company_name
    FROM branches b
    INNER JOIN companies c ON b.company_id = c.company_id
    WHERE b.company_id = @CompanyId
    ORDER BY b.branch_name
END
GO

-- Create Branch
IF EXISTS (SELECT * FROM sys.objects WHERE type = 'P' AND name = 'sp_CreateBranch')
    DROP PROCEDURE sp_CreateBranch
GO

CREATE PROCEDURE sp_CreateBranch
    @BranchId UNIQUEIDENTIFIER,
    @CompanyId UNIQUEIDENTIFIER,
    @BranchName NVARCHAR(255),
    @PhoneNumber NVARCHAR(20) = NULL,
    @Email NVARCHAR(255) = NULL
AS
BEGIN
    INSERT INTO branches (branch_id, company_id, branch_name, phone_number, email, created_at, updated_at)
    VALUES (@BranchId, @CompanyId, @BranchName, @PhoneNumber, @Email, GETDATE(), NULL)

    SELECT @BranchId AS branch_id
END
GO

-- Update Branch
IF EXISTS (SELECT * FROM sys.objects WHERE type = 'P' AND name = 'sp_UpdateBranch')
    DROP PROCEDURE sp_UpdateBranch
GO

CREATE PROCEDURE sp_UpdateBranch
    @BranchId UNIQUEIDENTIFIER,
    @CompanyId UNIQUEIDENTIFIER,
    @BranchName NVARCHAR(255),
    @PhoneNumber NVARCHAR(20) = NULL,
    @Email NVARCHAR(255) = NULL
AS
BEGIN
    UPDATE branches
    SET company_id = @CompanyId,
        branch_name = @BranchName,
        phone_number = @PhoneNumber,
        email = @Email,
        updated_at = GETDATE()
    WHERE branch_id = @BranchId

    SELECT @@ROWCOUNT
END
GO

-- Delete Branch
IF EXISTS (SELECT * FROM sys.objects WHERE type = 'P' AND name = 'sp_DeleteBranch')
    DROP PROCEDURE sp_DeleteBranch
GO

CREATE PROCEDURE sp_DeleteBranch
    @BranchId UNIQUEIDENTIFIER
AS
BEGIN
    -- Check if branch is referenced by departments
    IF EXISTS (SELECT 1 FROM departments WHERE branch_id = @BranchId)
    BEGIN
        RAISERROR('Cannot delete branch with existing departments', 16, 1)
        RETURN
    END

    -- Check if branch is referenced by positions
    IF EXISTS (SELECT 1 FROM positions WHERE branch_id = @BranchId)
    BEGIN
        RAISERROR('Cannot delete branch with existing positions', 16, 1)
        RETURN
    END

    DELETE FROM branches
    WHERE branch_id = @BranchId

    SELECT @@ROWCOUNT
END
GO
