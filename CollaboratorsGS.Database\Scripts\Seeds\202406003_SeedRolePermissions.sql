-- Seed Role Permissions with <PERSON><PERSON><PERSON>
IF NOT EXISTS (SELECT TOP 1 1 FROM RolePermissions)
BEGIN
    PRINT 'Seeding Role Permissions with UUID...';

    -- Get role IDs
    DECLARE @admin_role_id UNIQUEIDENTIFIER;
    <PERSON><PERSON><PERSON><PERSON> @user_role_id UNIQUEIDENTIFIER;
    <PERSON><PERSON><PERSON><PERSON> @recruiter_role_id UNIQUEIDENTIFIER;
    <PERSON><PERSON><PERSON><PERSON> @manager_role_id UNIQUEIDENTIFIER;
    <PERSON><PERSON>AR<PERSON> @collaborator_role_id UNIQUEIDENTIFIER;

    SELECT @admin_role_id = role_id FROM Roles WHERE role_name = 'Admin';
    SELECT @user_role_id = role_id FROM Roles WHERE role_name = 'User';
    SELECT @recruiter_role_id = role_id FROM Roles WHERE role_name = 'Recruiter';
    SELECT @manager_role_id = role_id FROM Roles WHERE role_name = 'Manager';
    SELECT @collaborator_role_id = role_id FROM Roles WHERE role_name = 'Collaborator';

    -- Assign all permissions to Admin role
    INSERT INTO RolePermissions (role_id, permission_id)
    SELECT @admin_role_id, permission_id FROM Permissions;

    -- Assign permissions to User role
    INSERT INTO RolePermissions (role_id, permission_id)
    SELECT @user_role_id, permission_id 
    FROM Permissions 
    WHERE permission_name IN (
        'users.view',
        'candidates.view',
        'recruitments.view',
        'applications.view'
    );

    -- Assign permissions to Recruiter role
    INSERT INTO RolePermissions (role_id, permission_id)
    SELECT @recruiter_role_id, permission_id 
    FROM Permissions 
    WHERE permission_name IN (
        'users.view',
        'candidates.view', 'candidates.create', 'candidates.edit',
        'recruitments.view', 'recruitments.create', 'recruitments.edit',
        'applications.view', 'applications.create', 'applications.edit', 'applications.process',
        'collaborators.view',
        'reports.view'
    );

    -- Assign permissions to Manager role
    INSERT INTO RolePermissions (role_id, permission_id)
    SELECT @manager_role_id, permission_id 
    FROM Permissions 
    WHERE permission_name IN (
        'users.view',
        'candidates.view', 'candidates.create', 'candidates.edit', 'candidates.delete',
        'recruitments.view', 'recruitments.create', 'recruitments.edit', 'recruitments.delete',
        'applications.view', 'applications.create', 'applications.edit', 'applications.delete', 'applications.process',
        'collaborators.view', 'collaborators.approve',
        'rewards.view', 'rewards.create', 'rewards.edit', 'rewards.approve',
        'kpis.view', 'kpis.create', 'kpis.edit',
        'reports.view', 'reports.create'
    );

    -- Assign permissions to Collaborator role
    INSERT INTO RolePermissions (role_id, permission_id)
    SELECT @collaborator_role_id, permission_id 
    FROM Permissions 
    WHERE permission_name IN (
        'candidates.view', 'candidates.create',
        'recruitments.view',
        'applications.view',
        'rewards.view',
        'kpis.view'
    );

    PRINT 'Role Permissions seeded successfully.';
END
ELSE
BEGIN
    PRINT 'Role Permissions already exist. Skipping...';
END
GO
