using CollaboratorsGS.Infrastructure.Middlewares;
using Microsoft.AspNetCore.Builder;

namespace CollaboratorsGS.Infrastructure.Extensions
{
    public static class MiddlewareExtensions
    {
        public static IApplicationBuilder UseExceptionHandling(this IApplicationBuilder app)
        {
            app.UseMiddleware<ExceptionHandlingMiddleware>();
            
            return app;
        }

        public static IApplicationBuilder UseRequestLogging(this IApplicationBuilder app)
        {
            app.UseMiddleware<RequestLoggingMiddleware>();
            
            return app;
        }
    }
}
