namespace CollaboratorsGS.Infrastructure.Configurations
{
    public class FileStorageOptions
    {
        public string StorageType { get; set; } = "Local"; // "Local" or "Minio"
        
        // Local storage options
        public string LocalBasePath { get; set; } = "uploads";
        public string LocalBaseUrl { get; set; } = "/uploads";
        
        // Minio storage options
        public string MinioEndpoint { get; set; } = "localhost:9000";
        public string MinioAccessKey { get; set; } = "";
        public string MinioSecretKey { get; set; } = "";
        public string MinioBucketName { get; set; } = "gstalent";
        public bool MinioWithSSL { get; set; } = false;
    }
}
