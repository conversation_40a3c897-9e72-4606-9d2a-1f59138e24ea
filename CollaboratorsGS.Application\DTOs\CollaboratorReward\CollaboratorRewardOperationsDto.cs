namespace CollaboratorsGS.Application.DTOs.CollaboratorReward
{
    public class CollaboratorRewardOperationsDto
    {
        public RewardSummaryDto? Summary { get; set; }
        public IEnumerable<RewardByTypeDto>? RewardsByType { get; set; }
        public UpcomingPaymentDto? UpcomingPayment { get; set; }
        public IEnumerable<RewardDetailDto>? RewardDetails { get; set; }
        public IEnumerable<RewardHistoryDto>? RewardHistory { get; set; }
    }
}
