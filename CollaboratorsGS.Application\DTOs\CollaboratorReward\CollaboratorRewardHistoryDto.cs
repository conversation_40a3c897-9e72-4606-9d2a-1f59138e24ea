using System;

namespace CollaboratorsGS.Application.DTOs.CollaboratorReward
{
    public class CollaboratorRewardHistoryDto
    {
        public Guid HistoryId { get; set; }
        public Guid CollaboratorId { get; set; }
        public string CollaboratorName { get; set; } = string.Empty;
        public Guid RewardId { get; set; }
        public decimal Amount { get; set; }
        public DateTime PaymentDate { get; set; }
        public string PaymentMethod { get; set; } = string.Empty;
        public string Status { get; set; } = string.Empty;
    }
}
