using CollaboratorsGS.Application.DTOs;
using CollaboratorsGS.Application.Interfaces;
using CollaboratorsGS.Infrastructure.Services;
using CollaboratorsGS.Infrastructure.Utilities;
using Microsoft.AspNetCore.Mvc;
using System;
using System.IO;
using System.Threading.Tasks;

namespace CollaboratorsGS.API.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class CvScannerController : ControllerBase
    {
        private readonly CvScannerService _cvScannerService;
        private readonly IFileStorageService _fileStorageService;
        private readonly ILogger<CvScannerController> _logger;

        public CvScannerController(
            CvScannerService cvScannerService,
            IFileStorageService fileStorageService,
            ILogger<CvScannerController> logger)
        {
            _cvScannerService = cvScannerService;
            _fileStorageService = fileStorageService;
            _logger = logger;
        }

        [HttpPost("scan")]
        public async Task<IActionResult> ScanCv(IFormFile file)
        {
            if (file == null || file.Length == 0)
            {
                return BadRequest(ApiResponse<object>.ErrorResponse(
                    MessageCodes.ER4001,
                    "No file was uploaded",
                    400,
                    null
                )
               );
            }

            try
            {
                using var memoryStream = new MemoryStream();
                await file.CopyToAsync(memoryStream);
                var fileBytes = memoryStream.ToArray();

                var scanResult = await _cvScannerService.ScanCvAsync(fileBytes, file.FileName);

                if (!scanResult.IsSuccess)
                {
                    return StatusCode(scanResult.StatusCode, scanResult);
                }


                var fileName = await _fileStorageService.UploadFileAsync(file);
                var fileId = Path.GetFileNameWithoutExtension(fileName);
                var fileUrl = $"{Request.Scheme}://{Request.Host}/api/files/{fileId}";

                return Ok(ApiResponse<object>.SuccessResponse(
                    MessageCodes.SC2000,
                    "Scan CV successfully",
                    new
                    {
                        scanResult.IsSuccess,
                        scanResult.Message,
                        scanResult.CandidateInfo,
                        FileInfo = new
                        {
                            FileId = fileId,
                            FileName = fileName,
                            FileUrl = fileUrl,
                            FileSize = file.Length,
                            ContentType = file.ContentType
                        }
                    }));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error scanning CV");
                return StatusCode(500, ApiResponse<object>.ErrorResponse(
                    MessageCodes.ER5000,
                    "Internal server error" + ex.Message,
                    500));
            }
        }

        [HttpPost("check-duplicate")]
        public async Task<IActionResult> CheckDuplicate([FromBody] CandidateInfo candidateInfo)
        {
            if (candidateInfo == null)
            {
                return BadRequest(ApiResponse<object>.ErrorResponse(
                    MessageCodes.ER4001,
                    "No candidate information provided",
                    400,
                    null
                ));
            }
            try
            {
                // Kiểm tra trùng lặp email
                if (!string.IsNullOrEmpty(candidateInfo.Email))
                {
                    var existingByEmail = await _cvScannerService.CheckCandidateByEmailAsync(candidateInfo.Email);
                    if (existingByEmail != null)
                    {
                        return Conflict(ApiResponse<object>.ErrorResponse(
                            MessageCodes.ER4009,
                            $"Candidate with email {candidateInfo.Email} already exists",
                            409,
                            null
                        ));
                    }
                }


                // Kiểm tra trùng lặp số điện thoại
                if (!string.IsNullOrEmpty(candidateInfo.PhoneNumber))
                {
                    var existingByPhone = await _cvScannerService.CheckCandidateByPhoneNumberAsync(candidateInfo.PhoneNumber);
                    if (existingByPhone != null)
                    {
                        return Conflict(ApiResponse<object>.ErrorResponse(
                            MessageCodes.ER4009,
                            $"Candidate with phone number {candidateInfo.PhoneNumber} already exists",
                            409,
                            null
                        ));
                    }
                }

                return Ok(ApiResponse<object>.SuccessResponse(
                    MessageCodes.SC2000,
                    "No duplicate found",
                    200
                ));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking duplicate candidate");
                return StatusCode(500, ApiResponse<object>.ErrorResponse(
                    MessageCodes.ER5000,
                    "Internal server error",
                    500,
                    null
                ));
            }
        }
    }  
}



