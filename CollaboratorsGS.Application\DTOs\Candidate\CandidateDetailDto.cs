namespace CollaboratorsGS.Application.DTOs.Candidate
{
    public class CandidateDetailDto
    {
        // Candidate properties
        public Guid CandidateId { get; set; }
        public string FullName { get; set; } = string.Empty;
        public string PhoneNumber { get; set; } = string.Empty;
        public string? Email { get; set; }
        public DateTime? DateOfBirth { get; set; }
        public string? Gender { get; set; }
        public string? Address { get; set; }
        public string? ProfilePicture { get; set; }
        public string? Level { get; set; }
        public string? Source { get; set; }
        public Guid? CtvId { get; set; }
        public string? CollaboratorName { get; set; }
        public string? CitizenId { get; set; }
        public string? CitizenIdAddress { get; set; }
        public DateTime? CitizenIdIssueDate { get; set; }
        public string? CitizenIdIssuePlace { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime? UpdatedAt { get; set; }
        
        // CandidateApplication properties
        public DateTime? ApplicationDate { get; set; }
        public string? ApplicationStatus { get; set; }
        public DateTime? InterviewRound1Date { get; set; }
        public string? InterviewRound1Result { get; set; }
        public DateTime? InterviewRound2Date { get; set; }
        public string? InterviewRound2Result { get; set; }
        public DateTime? OnboardDate { get; set; }
        public Guid? PostingId { get; set; }
        
        // RecruitmentPosting properties
        public string? Title { get; set; }
        public string? PositionName { get; set; }
        public string? DepartmentName { get; set; }
    }
}
