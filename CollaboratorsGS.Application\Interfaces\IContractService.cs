using CollaboratorsGS.Application.DTOs.Contract;

namespace CollaboratorsGS.Application.Interfaces
{
    public interface IContractService
    {
        Task<IEnumerable<ContractDto>> GetAllAsync();
        Task<ContractDto?> GetByIdAsync(Guid contractId);
        Task<IEnumerable<ContractDto>> GetByCollaboratorIdAsync(Guid collaboratorId);
        Task<IEnumerable<ContractDto>> GetByStatusAsync(string status);
        Task<Guid> CreateContractAsync(CreateContractRequest request);
        Task<bool> UpdateContractAsync(UpdateContractRequest request);
        Task<bool> DeleteContractAsync(Guid contractId);
    }
}
