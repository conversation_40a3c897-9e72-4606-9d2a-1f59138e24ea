using System;
using System.ComponentModel.DataAnnotations;

namespace CollaboratorsGS.Application.DTOs.CollaboratorKpiTarget
{
    public class UpdateCollaboratorKpiTargetRequest
    {
        [Required]
        public Guid TargetId { get; set; }
        
        [Range(0, int.MaxValue, ErrorMessage = "Target candidates imported must be non-negative")]
        public int TargetCandidatesImported { get; set; }
        
        [Range(0, int.MaxValue, ErrorMessage = "Target candidates passed round 1 must be non-negative")]
        public int TargetCandidatesPassedRound1 { get; set; }
        
        [Range(0, int.MaxValue, ErrorMessage = "Target candidates onboarded must be non-negative")]
        public int TargetCandidatesOnboarded { get; set; }
    }
}
