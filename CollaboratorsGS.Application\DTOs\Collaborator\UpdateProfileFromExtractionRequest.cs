using System.ComponentModel.DataAnnotations;
using System.Text.Json.Serialization;

namespace CollaboratorsGS.Application.DTOs.Collaborator
{
    public class UpdateProfileFromExtractionRequest
    {
        [Required]
        public CitizenInfoRequest Info { get; set; } = new();
    }

    public class CitizenInfoRequest
    {
        [JsonPropertyName("citizen_id")]
        public string CitizenId { get; set; } = string.Empty;

        [JsonPropertyName("full_name")]
        public string FullName { get; set; } = string.Empty;

        [JsonPropertyName("dob")]
        public string DateOfBirth { get; set; } = string.Empty; // "13/01/2001"

        [JsonPropertyName("sex")]
        public string Gender { get; set; } = string.Empty;

        [JsonPropertyName("add_str")]
        public string Address { get; set; } = string.Empty;

        [JsonPropertyName("personal_identification")]
        public string PersonalIdentification { get; set; } = string.Empty;

        [JsonPropertyName("date_issue")]
        public string DateIssue { get; set; } = string.Empty; // "12/02/2023"

        [JsonPropertyName("director_general_police_department")]
        public string IssueAuthority { get; set; } = string.Empty;
    }
}
