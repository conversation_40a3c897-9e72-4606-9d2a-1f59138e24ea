using CollaboratorsGS.Domain.Entities;

namespace CollaboratorsGS.Domain.Repositories
{
    public interface IContractRepository
    {
        Task<Contract?> GetByIdAsync(Guid contractId);
        Task<IEnumerable<Contract>> GetAllAsync();
        Task<IEnumerable<Contract>> GetByCollaboratorIdAsync(Guid ctvId);
        Task<IEnumerable<Contract>> GetByStatusAsync(string status);
        Task<Guid> CreateAsync(Contract contract);
        Task<bool> UpdateAsync(Contract contract);
        Task<bool> DeleteAsync(Guid contractId);
    }
}
