using CollaboratorsGS.Application.DTOs.Collaborator;
using CollaboratorsGS.Application.DTOs.CollaboratorLevel;

namespace CollaboratorsGS.Application.Interfaces
{
    public interface ICollaboratorService
    {
        Task<CollaboratorDto?> GetByIdAsync(Guid ctvId);
        Task<CollaboratorDto?> GetByUserIdAsync(Guid userId);
        Task<Guid?> GetByUserId(Guid userId);
        Task<IEnumerable<CollaboratorDto>> GetAllAsync();
        Task<IEnumerable<CollaboratorDto>> GetByStatusAsync(string status);
        Task<Guid> CreateCollaboratorAsync(Guid userId, CreateCollaboratorRequest request);
        Task<CollaboratorDto?> GetCreatedCollaboratorAsync(Guid ctvId);
        Task<CollaboratorDto?> UpdateCollaboratorAsync(UpdateCollaboratorRequest request);
        Task<CollaboratorProfileDto?> UpdateCollaboratorProfileAsync(Guid collaboratorId, UpdateCollaboratorProfileRequest request);
        Task<CollaboratorProfileDto?> UpdateInformationAsync(Guid collaboratorId, UpdateInformationRequest request);
        Task<CollaboratorProfileDto?> UpdateProfileFromExtractionAsync(Guid collaboratorId, UpdateProfileFromExtractionRequest request);
        Task<CollaboratorProfileDto?> GetCollaboratorProfileAsync(Guid collaboratorId);
        Task<CollaboratorDto?> ApproveCollaboratorAsync(ApproveCollaboratorRequest request);
        Task<bool> DeleteCollaboratorAsync(Guid ctvId);
        Task UpdateAvatarAsync(Guid collaboratorId, string avatarUrl);
        Task<IEnumerable<ConbutritorReferalHistoryDto>?> GetReferalHistoryAsync(Guid userId);
        Task<CollaboratorLevelDetailDto> GetLevelDetailByUserIdAsync(Guid userId);
        Task<LevelUpgradeCheckDto> CheckLevelUpgradeEligibilityAsync(Guid userId);
    }
}
