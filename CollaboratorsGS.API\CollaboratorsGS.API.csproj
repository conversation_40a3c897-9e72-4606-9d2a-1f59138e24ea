<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <InvariantGlobalization>false</InvariantGlobalization>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.AspNetCore.OpenApi" Version="8.0.3" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="8.0.3">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\CollaboratorsGS.Application\CollaboratorsGS.Application.csproj" />
    <ProjectReference Include="..\CollaboratorsGS.Infrastructure\CollaboratorsGS.Infrastructure.csproj" />
    <ProjectReference Include="..\CollaboratorsGS.ServiceDefaults\CollaboratorsGS.ServiceDefaults.csproj" />
  </ItemGroup>

</Project>
