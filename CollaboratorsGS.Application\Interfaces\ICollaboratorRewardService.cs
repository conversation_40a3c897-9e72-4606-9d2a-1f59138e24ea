using CollaboratorsGS.Application.DTOs.CollaboratorReward;

namespace CollaboratorsGS.Application.Interfaces
{
    public interface ICollaboratorRewardService
    {
        Task<CollaboratorRewardDto?> GetByIdAsync(Guid rewardId);
        Task<IEnumerable<CollaboratorRewardDto>> GetAllAsync();
        Task<IEnumerable<CollaboratorRewardDto>> GetByCollaboratorIdAsync(Guid collaboratorId);
        Task<IEnumerable<CollaboratorRewardDto>> GetByStatusAsync(string status);
        Task<Guid> CreateCollaboratorRewardAsync(CreateCollaboratorRewardRequest request);
        Task<CollaboratorRewardDto?> GetCreatedCollaboratorRewardAsync(Guid rewardId);
        Task<CollaboratorRewardDto?> UpdateCollaboratorRewardAsync(UpdateCollaboratorRewardRequest request);
        Task<bool> DeleteCollaboratorRewardAsync(Guid rewardId);
        Task<bool> ProcessPaymentAsync(Guid rewardId, string paymentMethod);

        // Unified Reward Operations Method
        Task<CollaboratorRewardOperationsDto> GetRewardOperationsAsync(Guid collaboratorId, int? month, int? year, string action);
        Task<IEnumerable<CollaboratorRewardHistoryDto>> GetHistoryByRewardIdAsync(Guid rewardId);
        Task<Guid> CreateCollaboratorRewardHistoryAsync(CreateCollaboratorRewardHistoryRequest request);
    }
}
