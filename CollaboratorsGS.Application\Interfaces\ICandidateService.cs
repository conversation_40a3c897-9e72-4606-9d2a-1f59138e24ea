using CollaboratorsGS.Application.DTOs.Candidate;

namespace CollaboratorsGS.Application.Interfaces
{
    public interface ICandidateService
    {
        Task<CandidateDto?> GetByIdAsync(Guid candidateId);
        Task<IEnumerable<CandidateDto>> GetAllAsync();
        Task<IEnumerable<CandidateDto>> GetByCollaboratorIdAsync(Guid collaboratorId);
        Task<Guid> CreateCandidateAsync(CreateCandidateRequest request);

        Task<Guid> CreateCandidateFromCVAsync(CreateCandidateFromCVRequest request, Guid collaboratorId, dynamic CandidateInfo, string fileName, string fileExtension );
        Task<CandidateDto?> GetCreatedCandidateAsync(Guid candidateId);
        Task<CandidateDto?> UpdateCandidateAsync(Guid candidateId, UpdateCandidateRequest request);
        Task<bool> DeleteCandidateAsync(Guid candidateId);

        // New methods for detailed candidate information
        Task<CandidateDetailDto?> GetDetailByIdAsync(Guid candidateId);
        Task<IEnumerable<CandidateDetailDto>> GetAllDetailsAsync();
        Task<IEnumerable<CandidateDetailDto>> GetDetailsByCollaboratorIdAsync(Guid CollaboratorId);
    }
}
