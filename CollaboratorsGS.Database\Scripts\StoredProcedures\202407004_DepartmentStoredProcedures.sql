-- Stored Procedures for Department operations

-- Get Department by ID
IF EXISTS (SELECT * FROM sys.objects WHERE type = 'P' AND name = 'sp_GetDepartmentById')
    DROP PROCEDURE sp_GetDepartmentById
GO

CREATE PROCEDURE sp_GetDepartmentById
    @department_id UNIQUEIDENTIFIER
AS
BEGIN
    SELECT d.*, u.full_name as manager_name
    FROM departments d
    LEFT JOIN users u ON d.manager_id = u.user_id
    WHERE d.department_id = @department_id
END
GO

-- Get All Departments
IF EXISTS (SELECT * FROM sys.objects WHERE type = 'P' AND name = 'sp_GetAllDepartments')
    DROP PROCEDURE sp_GetAllDepartments
GO

CREATE PROCEDURE sp_GetAllDepartments
AS
BEGIN
    SELECT d.*, u.full_name as manager_name
    FROM departments d
    LEFT JOIN users u ON d.manager_id = u.user_id
    ORDER BY d.department_name
END
GO

-- Create Department
IF EXISTS (SELECT * FROM sys.objects WHERE type = 'P' AND name = 'sp_CreateDepartment')
    DROP PROCEDURE sp_CreateDepartment
GO

CREATE PROCEDURE sp_CreateDepartment
    @department_id UNIQUEIDENTIFIER,
    @department_name VARCHAR(255),
    @manager_id UNIQUEIDENTIFIER
AS
BEGIN
    INSERT INTO departments (department_id, department_name, manager_id)
    VALUES (@department_id, @department_name, @manager_id)
END
GO

-- Update Department
IF EXISTS (SELECT * FROM sys.objects WHERE type = 'P' AND name = 'sp_UpdateDepartment')
    DROP PROCEDURE sp_UpdateDepartment
GO

CREATE PROCEDURE sp_UpdateDepartment
    @department_id UNIQUEIDENTIFIER,
    @department_name VARCHAR(255),
    @manager_id UNIQUEIDENTIFIER
AS
BEGIN
    UPDATE departments
    SET department_name = @department_name,
        manager_id = @manager_id
    WHERE department_id = @department_id
END
GO

-- Delete Department
IF EXISTS (SELECT * FROM sys.objects WHERE type = 'P' AND name = 'sp_DeleteDepartment')
    DROP PROCEDURE sp_DeleteDepartment
GO

CREATE PROCEDURE sp_DeleteDepartment
    @department_id UNIQUEIDENTIFIER
AS
BEGIN
    DELETE FROM departments
    WHERE department_id = @department_id
END
GO
