using System;
using System.ComponentModel.DataAnnotations;

namespace CollaboratorsGS.Application.DTOs.CollaboratorViolation
{
    public class UpdateCollaboratorViolationRequest
    {
        [Required]
        public Guid ViolationId { get; set; }
        
        public string? ViolationType { get; set; }
        
        public string? Description { get; set; }
        
        public Guid? HandledBy { get; set; }
        
        public DateTime? HandledAt { get; set; }
    }
}
