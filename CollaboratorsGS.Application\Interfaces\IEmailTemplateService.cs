namespace CollaboratorsGS.Application.Interfaces
{
    public interface IEmailTemplateService
    {
        /// <summary>
        /// Send password reset email using template
        /// </summary>
        Task<bool> SendPasswordResetEmailAsync(string toEmail, string userName, string resetToken, string resetCode);

        /// <summary>
        /// Send collaborator approval email using template
        /// </summary>
        Task<bool> SendCollaboratorApprovalEmailAsync(string toEmail, string collaboratorName, string email, string phoneNumber, string approvedBy);

        /// <summary>
        /// Send recruitment notification email using template
        /// </summary>
        // Task<bool> SendRecruitmentNotificationEmailAsync(string toEmail, string collaboratorName, object jobDetails);

        /// <summary>
        /// Read template from file and replace placeholders
        /// </summary>
        Task<string> LoadTemplateAsync(string templateName, Dictionary<string, string> placeholders);
    }
}
