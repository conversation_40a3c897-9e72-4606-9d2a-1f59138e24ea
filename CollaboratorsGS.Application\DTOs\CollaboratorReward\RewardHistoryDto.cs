namespace CollaboratorsGS.Application.DTOs.CollaboratorReward
{
    public class RewardHistoryDto
    {
        public Guid RewardId { get; set; }
        public decimal Amount { get; set; }
        public DateTime PaymentDate { get; set; }
        public string PaymentMethod { get; set; } = string.Empty;
        public string RewardStatus { get; set; } = string.Empty;
        public string CandidateName { get; set; } = string.Empty;
        public string PositionName { get; set; } = string.Empty;
        public string RewardType { get; set; } = string.Empty;
        public DateTime RewardDate { get; set; }
    }
}
