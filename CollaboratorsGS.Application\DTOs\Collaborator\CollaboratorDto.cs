namespace CollaboratorsGS.Application.DTOs.Collaborator
{
    public class CollaboratorDto
    {
        public Guid CollaboratorId { get; set; }
        public Guid UserId { get; set; }
        public string? PhotoUrl { get; set; } = null;
        public string FullName { get; set; } = string.Empty;
        public string PhoneNumber { get; set; } = string.Empty;
        public string Email { get; set; } = string.Empty;
        public Guid LevelId { get; set; }
        public string LevelName { get; set; } = string.Empty;
        public string Status { get; set; } = string.Empty;
        public DateTime? LastLevelUpdatedAt { get; set; }
        public DateTime CreatedAt { get; set; }
        public Guid? ApprovedBy { get; set; }
        public string? ApproverName { get; set; }
        public DateTime? ApprovedDate { get; set; }
        public DateTime? UpdatedAt { get; set; }

        // Profile information
        public string? CitizenId { get; set; }
        public string? PermanentAddress { get; set; }
        public string? CurrentAddress { get; set; }
        public DateTime? IssueDate{ get; set; }
        public string? IssueAuthority { get; set; }
        public string? BankName { get; set; }
        public string? BankAccountNumber { get; set; }
        public string? BankAccountName { get; set; } // Bank account owner name


        // Statistics
        public int TotalCandidates { get; set; }
        public int TotalOnboarded { get; set; }
        public decimal TotalEarnings { get; set; }
    }
}
