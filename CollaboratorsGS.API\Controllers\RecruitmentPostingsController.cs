using CollaboratorsGS.Application.Constants;
using CollaboratorsGS.Application.DTOs;
using CollaboratorsGS.Application.DTOs.Common;
using CollaboratorsGS.Application.DTOs.RecruitmentPosting;
using CollaboratorsGS.Application.Interfaces;
using CollaboratorsGS.Domain.Enums;
using CollaboratorsGS.Infrastructure.Utilities;
using Google.Protobuf.WellKnownTypes;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace CollaboratorsGS.API.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    [Authorize]
    public class RecruitmentPostingsController : ControllerBase
    {
        private readonly IRecruitmentPostingService _recruitmentPostingService;
        private readonly ILogger<RecruitmentPostingsController> _logger;

        public RecruitmentPostingsController(
            IRecruitmentPostingService recruitmentPostingService,
            ILogger<RecruitmentPostingsController> logger)
        {
            _recruitmentPostingService = recruitmentPostingService;
            _logger = logger;
        }

        // GET: api/RecruitmentPostings
        [HttpGet]
        public async Task<IActionResult> GetAll()
        {
            try
            {
                var postings = await _recruitmentPostingService.GetAllAsync();
                return Ok(postings);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting all recruitment postings");
                return StatusCode(500, "Internal server error");
            }
        }

        // GET: api/RecruitmentPostings/{id}
        [HttpGet("{id}")]
        public async Task<IActionResult> GetById(Guid id)
        {
            try
            {
                var posting = await _recruitmentPostingService.GetByIdAsync(id);

                if (posting == null)
                    return NotFound(ApiResponse<object>.ErrorResponse(
                         MessageCodes.ER4004,
                         "Collaborator not found",
                         404));

                return Ok(ApiResponse<object>.SuccessResponse(
                    MessageCodes.SC2000,
                    "Get recruitment posting by id successfully",
                    posting));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting recruitment posting with ID {PostingId}", id);
                return StatusCode(500, ApiResponse<object>.ErrorResponse(
                    MessageCodes.ER5000,
                    "Internal server error",
                    500));
            }
        }

        // GET: api/RecruitmentPostings/{id}/detail
        [HttpGet("{id}/detail")]
        public async Task<IActionResult> GetDetail(Guid id)
        {
            try
            {
                var posting = await _recruitmentPostingService.GetDetailByIdAsync(id);

                if (posting == null)
                    return NotFound(ApiResponse<object>.ErrorResponse(
                         MessageCodes.ER4004,
                         "Collaborator not found",
                         404));

                return Ok(ApiResponse<object>.SuccessResponse(
                    MessageCodes.SC2000,
                    "Get recruitment posting detail successfully",
                    posting));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting recruitment posting detail with ID {PostingId}", id);
                return StatusCode(500, ApiResponse<object>.ErrorResponse(
                    MessageCodes.ER5000,
                    "Internal server error",
                    500));
            }
        }

        // GET: api/RecruitmentPostings/refer-code/{referCode}
        [HttpGet("refer-code/{referCode}")]
        public async Task<IActionResult> GetByReferCode(string referCode)
        {
            try
            {
                var postings = await _recruitmentPostingService.GetByReferCodeAsync(referCode);
                return Ok(ApiResponse<object>.SuccessResponse(
                    MessageCodes.SC2000,
                    "Get recruitment posting by refer code successfully",
                    postings));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting recruitment postings with refer code {ReferCode}", referCode);
                return StatusCode(500, ApiResponse<object>.ErrorResponse(
                    MessageCodes.ER5000,
                    "Internal server error",
                    500));
            }
        }



        // GET: api/RecruitmentPostings/status/{status}
        [HttpGet("status/{status}")]
        public async Task<IActionResult> GetPostingsByStatus(RecruitmentPostingStatus status)
        {
            try
            {
                // Using helper method to create request with default pagination
                var request = CreateDefaultSearchRequest();
                request.status = status;

                var result = await _recruitmentPostingService.SearchAsync(request);
                return Ok(ApiResponse<object>.SuccessResponse(
                    MessageCodes.SC2000,
                    "Get recruitment postings by status successfully",
                    result));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting recruitment postings by status {Status}", status);
                return StatusCode(500, ApiResponse<object>.ErrorResponse(
                    MessageCodes.ER5000,
                    "Internal server error",
                    500));
            }
        }

        [HttpGet("search")]
        public async Task<IActionResult> Search([FromQuery] SearchRecruitmentPostingFilters filters)
        {
            try
            {
               // Using helper method to create request with default pagination
                var request = CreateDefaultSearchRequest();

                // Apply filters
                request.keyword = filters.keyword;
                request.level = filters.level;
                request.working_location = filters.working_location;
                request.salary_from = filters.salary_from;
                request.salary_to = filters.salary_to;
                request.is_urgent = filters.is_urgent;
                request.is_hot = filters.is_hot;
                request.status = filters.status;

                var result = await _recruitmentPostingService.SearchAsync(request);
                return Ok(ApiResponse<object>.SuccessResponse(
                    MessageCodes.SC2000,
                    "Search recruitment postings successfully",
                    result));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error searching recruitment postings");
                return StatusCode(500, ApiResponse<object>.ErrorResponse(
                    MessageCodes.ER5000,
                    "Internal server error",
                    500));
            }
        }

        // GET: api/RecruitmentPostings/search/advanced
        [HttpGet("search/advanced")]
        public async Task<IActionResult> SearchAdvanced([FromQuery] SearchRecruitmentPostingRequest request)
        {
            try
            {
                var result = await _recruitmentPostingService.SearchAsync(request);
                return Ok(ApiResponse<object>.SuccessResponse(
                    MessageCodes.SC2000,
                    "Search recruitment postings successfully",
                    result));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error searching recruitment postings with advanced options");
                return StatusCode(500, ApiResponse<object>.ErrorResponse(
                    MessageCodes.ER5000,
                    "Internal server error",
                    500));
            }
        }

        // POST: api/RecruitmentPostings/{id}/view
        [HttpPost("{id}/view")]
        public async Task<IActionResult> IncrementViewCount(Guid id)
        {
            try
            {
                var result = await _recruitmentPostingService.IncrementViewCountAsync(id);
                if (!result)
                    return NotFound(ApiResponse<object>.ErrorResponse(
                        MessageCodes.ER4004,
                        "Recruitment posting not found",
                        404));

                return Ok(ApiResponse<object>.SuccessResponse(
                    MessageCodes.SC2000,
                    "Increment view count successfully",
                    201));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error incrementing view count for posting {PostingId}", id);
                return StatusCode(500, ApiResponse<object>.ErrorResponse(
                    MessageCodes.ER5000,
                    "Internal server error",
                    500));
            }
        }

        // POST: api/RecruitmentPostings/{id}/referral
        [HttpPost("{id}/referral")]
        public async Task<IActionResult> IncrementReferralCount(Guid id)
        {
            try
            {
                var result = await _recruitmentPostingService.IncrementReferralCountAsync(id);
                if (!result)
                    return NotFound(ApiResponse<object>.ErrorResponse(
                        MessageCodes.ER4004,
                        "Recruitment posting not found",
                        404));

                return Ok(ApiResponse<object>.SuccessResponse(
                    MessageCodes.SC2000,
                    "Increment referral count successfully",
                    201));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error incrementing referral count for posting {PostingId}", id);
                return StatusCode(500, ApiResponse<object>.ErrorResponse(
                    MessageCodes.ER5000,
                    "Internal server error",
                    500));
            }
        }

        // POST: api/RecruitmentPostings
        [HttpPost]
        [Authorize(Roles = RolesUser.AdminManagerRecruiter)]
        public async Task<IActionResult> Create([FromBody] CreateRecruitmentPostingRequest request)
        {
            try
            {
                var postingId = await _recruitmentPostingService.CreateAsync(request);
                return CreatedAtAction(nameof(GetById), new { id = postingId }, 
                ApiResponse<object>.SuccessResponse(
                        MessageCodes.SC2001,
                        "Recruitment posting created successfully",
                        201));
            }
            catch (InvalidOperationException ex)
            {
                _logger.LogWarning(ex, "Validation error creating recruitment posting");
                return BadRequest(ApiResponse<object>.ErrorResponse(
                    MessageCodes.ER4005,
                    ex.Message,
                    400,
                    new List<ErrorDetail>
                    {
                        new ErrorDetail
                        {
                            Field = "request",
                            ErrorCode = MessageCodes.ER4005,
                            Message = ex.Message
                        }
                    }));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating recruitment posting");
                return StatusCode(500, ApiResponse<object>.ErrorResponse(
                    MessageCodes.ER5000,
                    "Internal server error",
                    500));
            }
        }

        // PUT: api/RecruitmentPostings/{id}
        [HttpPut("{id}")]
        [Authorize(Roles = RolesUser.AdminManagerRecruiter)]
        public async Task<IActionResult> Update(Guid id, [FromBody] UpdateRecruitmentPostingRequest request)
        {
            try
            {
                var result = await _recruitmentPostingService.UpdateAsync(id, request);

                if (!result)
                    return NotFound(ApiResponse<object>.ErrorResponse(
                        MessageCodes.ER4004,
                        "Recruitment posting not found",
                        404));

                return Ok(ApiResponse<object>.SuccessResponse(
                    MessageCodes.SC2002,
                    "Recruitment posting updated successfully",
                    200));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating recruitment posting with ID {PostingId}", id);
                return StatusCode(500, ApiResponse<object>.ErrorResponse(
                    MessageCodes.ER5000,
                    "Internal server error",
                    500));
            }
        }

        // DELETE: api/RecruitmentPostings/{id}
        [HttpDelete("{id}")]
        [Authorize(Roles = RolesUser.AdminManagerRecruiter)]
        public async Task<IActionResult> Delete(Guid id)
        {
            try
            {
                var result = await _recruitmentPostingService.DeleteAsync(id);

                if (!result)
                    return NotFound(ApiResponse<object>.ErrorResponse(
                        MessageCodes.ER4004,
                        "Recruitment posting not found",
                        404));

                return Ok(ApiResponse<object>.SuccessResponse(
                    MessageCodes.SC2003,
                    "Recruitment posting deleted successfully",
                    200));
            }
            catch (InvalidOperationException ex)
            {
                _logger.LogWarning(ex, "Cannot delete recruitment posting with ID {PostingId}", id);
                return BadRequest(ApiResponse<object>.ErrorResponse(
                    MessageCodes.ER4005,
                    ex.Message,
                    400,
                    new List<ErrorDetail>
                    {
                        new ErrorDetail
                        {
                            Field = "id",
                            ErrorCode = MessageCodes.ER4005,
                            Message = ex.Message
                        }
                    }));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting recruitment posting with ID {PostingId}", id);
                return StatusCode(500, ApiResponse<object>.ErrorResponse(
                    MessageCodes.ER5000,
                    "Internal server error",
                    500));
            }
        }

        /// <summary>
        /// Helper method to create a default search request with pagination
        /// </summary>
        private static SearchRecruitmentPostingRequest CreateDefaultSearchRequest()
        {
            return PaginationHelper.ApplyDefaultPagination(new SearchRecruitmentPostingRequest());
        }

    }
}
