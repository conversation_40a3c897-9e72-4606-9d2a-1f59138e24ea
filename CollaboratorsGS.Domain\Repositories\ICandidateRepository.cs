using CollaboratorsGS.Domain.Entities;

namespace CollaboratorsGS.Domain.Repositories
{
    public interface ICandidateRepository
    {
        Task<Candidate?> GetByIdAsync(Guid candidateId);
        Task<IEnumerable<Candidate>> GetAllAsync();
        Task<IEnumerable<Candidate>> GetByCollaboratorIdAsync(Guid collaboratorId);
        Task<Guid> CreateAsync(Candidate candidate);
        Task<Guid> CreateAsync(Candidate candidate, CandidateDocument document);
        Task<bool> UpdateAsync(Candidate candidate);
        Task<bool> DeleteAsync(Guid candidateId);
        Task<Candidate?> GetByEmailAsync(string? email);
        Task<Candidate?> GetByPhoneNumberAsync(string phoneNumber);
        Task<Candidate?> GetByCitizenIdAsync(string? citizenId);

        // New methods for detailed candidate information
        Task<dynamic?> GetDetailByIdAsync(Guid candidateId);
        Task<IEnumerable<dynamic>> GetAllDetailsAsync();
        Task<IEnumerable<dynamic>> GetDetailsByCollaboratorIdAsync(Guid ctvId);
    }
}
