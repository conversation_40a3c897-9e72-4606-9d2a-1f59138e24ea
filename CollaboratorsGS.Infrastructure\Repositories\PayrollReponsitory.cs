using CollaboratorsGS.Domain.Entities;
using CollaboratorsGS.Domain.Repositories;
using CollaboratorsGS.Infrastructure.Data;
using Dapper;
using Microsoft.Extensions.Configuration;
using System.Data;
using System.Data.SqlClient;
using System.Drawing;

namespace CollaboratorsGS.Infrastructure.Repositories
{
    public class PayrollRepository : IPayrollRepository
    {
        private readonly IConnectionFactory _connectionFactory;
        private readonly string _connectionString; // Replace with your actual connection string

        public PayrollRepository(IConnectionFactory connectionFactory,IConfiguration configuration)
        {
            _connectionFactory = connectionFactory;
            _connectionString = configuration.GetConnectionString("ECMConnection");
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="Year"></param>
        /// <param name="month"></param>
        /// <param name="UserId"></param>
        /// <param name="payroll_id"></param>
        /// <returns></returns>
        public async Task<IEnumerable<Payroll>> GetListAsync(int Year, int month, int UserId)
        {
            using (SqlConnection connection = new SqlConnection(_connectionString)) //---ConnectionString
            {
                var parameters = new DynamicParameters();
                parameters.Add("@Year", Year);
                parameters.Add("@month", month);
                parameters.Add("@UserId", UserId);

                var result = await connection.QueryAsync<Payroll>(
                    "Get_PayrollByUserId_v2",  
                    parameters,
                    commandType: CommandType.StoredProcedure);
                return result; // Fix: Await the result and then call ToList
            }
        }

        public async Task<Payroll> GetByIdAsync(int payroll_id, int UserId)
        {
            using (SqlConnection  connection = new SqlConnection(_connectionString)) //---ConnectionString
            {
                var parameters = new DynamicParameters();
                parameters.Add("@Id", payroll_id);
                parameters.Add("@UserId", UserId);

                var result = await connection.QueryAsync<Payroll>(
                    "Get_PayrollInfoById",
                    parameters,
                    commandType: CommandType.StoredProcedure);
                return result.FirstOrDefault(); // Fix: Await the result and then call ToList
            }
        }
    }
}
