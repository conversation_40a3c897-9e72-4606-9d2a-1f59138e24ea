using CollaboratorsGS.Application.DTOs.Position;

namespace CollaboratorsGS.Application.Interfaces
{
    public interface IPositionService
    {
        Task<PositionDto?> GetByIdAsync(Guid positionId);
        Task<IEnumerable<PositionDto>> GetAllAsync();
        Task<IEnumerable<PositionDto>> GetByDepartmentAsync(Guid departmentId);
        Task<Guid> CreatePositionAsync(CreatePositionRequest request);
        Task<PositionDto?> GetCreatedPositionAsync(Guid positionId);
        Task<PositionDto?> UpdatePositionAsync(UpdatePositionRequest request);
        Task<bool> DeletePositionAsync(Guid positionId);
    }
}
