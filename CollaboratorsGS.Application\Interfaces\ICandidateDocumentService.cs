using CollaboratorsGS.Application.DTOs.CandidateDocument;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace CollaboratorsGS.Application.Interfaces
{
    public interface ICandidateDocumentService
    {
        Task<Guid> CreateDocumentAsync(CreateCandidateDocumentRequest request);
        Task<CandidateDocumentDto?> GetByIdAsync(Guid documentId);
        Task<IEnumerable<CandidateDocumentDto>> GetByCandidateIdAsync(Guid candidateId);
        Task<bool> DeleteDocumentAsync(Guid documentId);
    }
}