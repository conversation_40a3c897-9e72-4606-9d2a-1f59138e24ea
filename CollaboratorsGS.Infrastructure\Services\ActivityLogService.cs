using CollaboratorsGS.Application.DTOs.AuditLog;
using CollaboratorsGS.Application.Interfaces;
using CollaboratorsGS.Domain.Entities;
using CollaboratorsGS.Domain.Repositories;
using CollaboratorsGS.Infrastructure.Utilities;
using Microsoft.Extensions.Logging;

namespace CollaboratorsGS.Infrastructure.Services
{
    public class ActivityLogService : IActivityLogService
    {
        private readonly IAuditLogRepository _auditLogRepository;
        private readonly ICollaboratorRepository _collaboratorRepository;
        private readonly ICandidateRepository _candidateRepository;
        private readonly IRecruitmentPostingRepository _recruitmentPostingRepository;
        private readonly ICollaboratorRewardRepository _collaboratorRewardRepository;
        private readonly ILogger<ActivityLogService> _logger;

        public ActivityLogService(
            IAuditLogRepository auditLogRepository,
            ICollaboratorRepository collaboratorRepository,
            ICandidateRepository candidateRepository,
            IRecruitmentPostingRepository recruitmentPostingRepository,
            ICollaboratorRewardRepository collaboratorRewardRepository,
            ILogger<ActivityLogService> logger)
        {
            _auditLogRepository = auditLogRepository;
            _collaboratorRepository = collaboratorRepository;
            _candidateRepository = candidateRepository;
            _recruitmentPostingRepository = recruitmentPostingRepository;
            _collaboratorRewardRepository = collaboratorRewardRepository;
            _logger = logger;
        }

        public async Task<IEnumerable<RecentActivityDto>> GetRecentActivitiesAsync(Guid collaboratorId, int limit = 10)
        {
            try
            {
                // Get collaborator to find their userId
                var collaborator = await _collaboratorRepository.GetByIdAsync(collaboratorId);
                if (collaborator == null)
                {
                    _logger.LogWarning("Collaborator {CollaboratorId} not found", collaboratorId);
                    return Enumerable.Empty<RecentActivityDto>();
                }

                // Get audit logs using the collaborator's userId
                var auditLogs = await _auditLogRepository.GetRecentActivitiesByUserIdAsync(collaborator.UserId, limit);

                var activities = new List<RecentActivityDto>();

                foreach (var log in auditLogs)
                {
                    // Get entity name based on entity type
                    var entityName = await GetEntityNameAsync(log.EntityType ?? string.Empty, log.EntityId);

                    var activity = new RecentActivityDto
                    {
                        LogId = log.LogId,
                        Action = log.Action ?? string.Empty,
                        EntityType = log.EntityType ?? string.Empty,
                        EntityId = log.EntityId,
                        Title = ActivityHelper.GetActivityTitle(log.Action ?? string.Empty, log.EntityType ?? string.Empty, entityName),
                        Description = ActivityHelper.GetActivityDescription(log.Action ?? string.Empty, log.EntityType ?? string.Empty),
                        TimeAgo = ActivityHelper.GetTimeAgo(log.ActionDate),
                        ActionDate = log.ActionDate
                    };

                    activities.Add(activity);
                }

                return activities;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting recent activities for collaborator {CollaboratorId}", collaboratorId);
                throw;
            }
        }

        public async Task LogActivityAsync(Guid userId, string action, string entityType, Guid entityId, string? oldValue = null, string? newValue = null, string? ipAddress = null)
        {
            try
            {
                var auditLog = new AuditLogs
                {
                    LogId = Guid.NewGuid(),
                    UserId = userId,
                    Action = action,
                    EntityType = entityType,
                    EntityId = entityId,
                    OldValue = oldValue,
                    NewValue = newValue,
                    ActionDate = DateTime.UtcNow,
                    IpAddress = ipAddress,
                    Status = "Success"
                };

                await _auditLogRepository.CreateAsync(auditLog);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error logging activity for user {UserId}", userId);
                // Don't throw here to avoid breaking the main operation
            }
        }

        private async Task<string?> GetEntityNameAsync(string entityType, Guid entityId)
        {
            try
            {
                return entityType.ToLower() switch
                {
                    "candidate" => await GetCandidateNameAsync(entityId),
                    "recruitmentposting" => await GetRecruitmentPostingTitleAsync(entityId),
                    "collaboratorreward" => await GetCollaboratorRewardInfoAsync(entityId),
                    _ => null
                };
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Failed to get entity name for {EntityType} {EntityId}", entityType, entityId);
                return null;
            }
        }

        private async Task<string?> GetCandidateNameAsync(Guid candidateId)
        {
            try
            {
                var candidate = await _candidateRepository.GetByIdAsync(candidateId);
                return candidate?.FullName;
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Failed to get candidate name for {CandidateId}", candidateId);
                return null;
            }
        }

        private async Task<string?> GetRecruitmentPostingTitleAsync(Guid postingId)
        {
            try
            {
                var posting = await _recruitmentPostingRepository.GetByIdAsync(postingId);
                return posting?.Title;
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Failed to get recruitment posting title for {PostingId}", postingId);
                return null;
            }
        }

        private async Task<string?> GetCollaboratorRewardInfoAsync(Guid rewardId)
        {
            try
            {
                var reward = await _collaboratorRewardRepository.GetByIdAsync(rewardId);
                if (reward != null)
                {
                    // Format as currency
                    return $"{reward.Amount:N0} VND";
                }
                return null;
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Failed to get collaborator reward info for {RewardId}", rewardId);
                return null;
            }
        }
    }
}
