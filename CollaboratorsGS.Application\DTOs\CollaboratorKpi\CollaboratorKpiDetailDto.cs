using System;

namespace CollaboratorsGS.Application.DTOs.CollaboratorKpi
{
    public class CollaboratorKpiDetailDto
    {
        public CollaboratorKpiCurrentPeriodDto CurrentPeriod { get; set; } = new();
        public List<CollaboratorKpiProgressDto> ProgressDetails { get; set; } = new();
        public List<CollaboratorKpiHistoryDetailDto> KpiHistory { get; set; } = new();
    }

    public class CollaboratorKpiCurrentPeriodDto
    {
        public string Period { get; set; } = string.Empty;
        public DateTime StartDate { get; set; }
        public DateTime EndDate { get; set; }
        public int DaysRemaining { get; set; }
        
        // Target vs Achievement
        public int TargetOnboard { get; set; }
        public int AchievedOnboard { get; set; }
        public decimal SuccessPercentage { get; set; }
        
        // Status
        public string Status { get; set; } = string.Empty; // "Pass" or "Fail"
        public decimal TotalReward { get; set; }
    }

    public class CollaboratorKpiProgressDto
    {
        public string Title { get; set; } = string.Empty;
        public int Count { get; set; }
        public int Target { get; set; }
        public decimal Percentage { get; set; }
    }

    public class CollaboratorKpiHistoryDetailDto
    {
        public string Period { get; set; } = string.Empty; // Format: "Q1/2024", "Q2/2024", etc.
        public int TargetOnboard { get; set; }
        public int AchievedOnboard { get; set; }
        public string Status { get; set; } = string.Empty; // "Pass" or "Fail"
        public decimal TotalReward { get; set; }
    }
}
