using CollaboratorsGS.Application.DTOs.CollaboratorReport;

namespace CollaboratorsGS.Application.Interfaces
{
    public interface ICollaboratorReportService
    {
        Task<CollaboratorReportDto?> GetByIdAsync(Guid reportId);
        Task<IEnumerable<CollaboratorReportDto>> GetAllAsync();
        Task<IEnumerable<CollaboratorReportDto>> GetByCollaboratorIdAsync(Guid collaboratorId);
        Task<IEnumerable<CollaboratorReportDto>> GetByPeriodAsync(string period);
        Task<Guid> CreateCollaboratorReportAsync(CreateCollaboratorReportRequest request);
        Task<CollaboratorReportDto?> GetCreatedCollaboratorReportAsync(Guid reportId);
        Task<CollaboratorReportDto?> UpdateCollaboratorReportAsync(UpdateCollaboratorReportRequest request);
        Task<bool> DeleteCollaboratorReportAsync(Guid reportId);
        Task<bool> GenerateReportAsync(Guid collaboratorId, string period);
    }
}
