using System;

namespace CollaboratorsGS.Application.DTOs.CollaboratorProfile
{
    public class CollaboratorProfileDto
    {
        public Guid ProfileId { get; set; }
        public Guid CtvId { get; set; }
        public string? CitizenId { get; set; }
        public string? CitizenIdFront { get; set; }
        public string? CitizenIdBack { get; set; }
        public string? PermanentAddress { get; set; }
        public string? CurrentAddress { get; set; }
        public string? BankName { get; set; }
        public string? BankAccountNumber { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime? UpdatedAt { get; set; }
    }
}
