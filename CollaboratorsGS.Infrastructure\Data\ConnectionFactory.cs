using Microsoft.Extensions.Configuration;
using System.Data;
using System.Data.SqlClient;

namespace CollaboratorsGS.Infrastructure.Data
{
    public class ConnectionFactory : IConnectionFactory
    {
        private readonly string _connectionString;
        private readonly string _connectionStringecm;
        public ConnectionFactory(IConfiguration configuration)
        {
            _connectionString = configuration.GetConnectionString("DefaultConnection")
                ?? throw new ArgumentNullException("DefaultConnection string is missing in configuration");
            _connectionStringecm = configuration.GetConnectionString("ECMConnection")
                ?? throw new ArgumentNullException("ECMConnection string is missing in configuration");
        }

        public IDbConnection CreateConnection()
        {
            var connection = new SqlConnection(_connectionString);
            if (connection.State == ConnectionState.Closed)
                connection.Open();

            return connection;
        }

        public IDbConnection CreateConnectionEcm()
        {
            var connectionecm = new SqlConnection(_connectionStringecm);
            if (connectionecm.State == ConnectionState.Closed)
                connectionecm.Open();
            return connectionecm;
        }
    }
}
