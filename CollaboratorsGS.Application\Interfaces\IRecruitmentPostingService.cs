using CollaboratorsGS.Application.DTOs.RecruitmentPosting;

namespace CollaboratorsGS.Application.Interfaces
{
    public interface IRecruitmentPostingService
    {
        Task<RecruitmentPostingDto?> GetByIdAsync(Guid postingId);
        Task<RecruitmentPostingDetailDto?> GetDetailByIdAsync(Guid postingId);
        Task<IEnumerable<RecruitmentPostingDto>> GetAllAsync();
        Task<IEnumerable<RecruitmentPostingDto>> GetByReferCodeAsync(string referCode);
        Task<SearchRecruitmentPostingResponse> SearchAsync(SearchRecruitmentPostingRequest request);
        Task<Guid> CreateAsync(CreateRecruitmentPostingRequest request);
        Task<bool> UpdateAsync(Guid postingId, UpdateRecruitmentPostingRequest request);
        Task<bool> DeleteAsync(Guid postingId);
        Task<bool> IncrementViewCountAsync(Guid postingId);
        Task<bool> IncrementReferralCountAsync(Guid postingId);
    }
}
