namespace CollaboratorsGS.Application.DTOs
{
    public class UserDto
    {
        public Guid user_id { get; set; }
        public string user_name { get; set; } = string.Empty;
        public string? full_name { get; set; }
        public string? email { get; set; }
        public string? phone_number { get; set; }
        public string role { get; set; } = string.Empty;
        public DateTime created_at { get; set; }
        public DateTime? last_login { get; set; }
        public bool is_active { get; set; }
        public int? group { get; set; }
        public string? type { get; set; }
        public int employee_id { get; set; }
    }
}
