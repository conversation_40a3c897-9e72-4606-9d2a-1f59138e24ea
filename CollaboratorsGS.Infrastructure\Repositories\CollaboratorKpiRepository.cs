using CollaboratorsGS.Domain.Entities;
using CollaboratorsGS.Domain.Repositories;
using CollaboratorsGS.Infrastructure.Data;
using Dapper;
using System.Data;

namespace CollaboratorsGS.Infrastructure.Repositories
{
    public class CollaboratorKpiRepository : ICollaboratorKpiRepository
    {
        private readonly IConnectionFactory _connectionFactory;

        public CollaboratorKpiRepository(IConnectionFactory connectionFactory)
        {
            _connectionFactory = connectionFactory;
        }

        public async Task<CollaboratorKpi?> GetByIdAsync(Guid kpiId)
        {
            using var connection = _connectionFactory.CreateConnection();

            var parameters = new DynamicParameters();
            parameters.Add("@KpiId", kpiId, DbType.Guid);

            return await connection.QuerySingleOrDefaultAsync<CollaboratorKpi>(
                "sp_GetCollaboratorKpiById",
                parameters,
                commandType: CommandType.StoredProcedure);
        }

        public async Task<IEnumerable<CollaboratorKpi>> GetAllAsync()
        {
            using var connection = _connectionFactory.CreateConnection();

            return await connection.QueryAsync<CollaboratorKpi>(
                "sp_GetAllCollaboratorKpis",
                commandType: CommandType.StoredProcedure);
        }

        public async Task<IEnumerable<CollaboratorKpi>> GetByCollaboratorIdAsync(Guid collaboratorId)
        {
            using var connection = _connectionFactory.CreateConnection();

            var parameters = new DynamicParameters();
            parameters.Add("@CollaboratorId", collaboratorId, DbType.Guid);

            return await connection.QueryAsync<CollaboratorKpi>(
                "sp_GetCollaboratorKpiByCollaboratorId",
                parameters,
                commandType: CommandType.StoredProcedure);
        }

        public async Task<IEnumerable<CollaboratorKpi>> GetByPeriodAsync(string period)
        {
            using var connection = _connectionFactory.CreateConnection();

            var parameters = new DynamicParameters();
            parameters.Add("@Period", period, DbType.String);

            return await connection.QueryAsync<CollaboratorKpi>(
                "sp_GetCollaboratorKpisByPeriod",
                parameters,
                commandType: CommandType.StoredProcedure);
        }

        public async Task<Guid> CreateAsync(CollaboratorKpi kpi)
        {
            // Generate a new UUID if not provided
            if (kpi.KpiId == Guid.Empty)
            {
                kpi.KpiId = Guid.NewGuid();
            }

            kpi.CalculatedAt = DateTime.UtcNow;

            using var connection = _connectionFactory.CreateConnection();

            var parameters = new DynamicParameters();
            parameters.Add("@KpiId", kpi.KpiId, DbType.Guid);
            parameters.Add("@CollaboratorId", kpi.CollaboratorId, DbType.Guid);
            parameters.Add("@Period", kpi.Period, DbType.String);
            parameters.Add("@TotalCandidatesImported", kpi.TotalCandidatesImported, DbType.Int32);
            parameters.Add("@TotalCandidatesPassedRound1", kpi.TotalCandidatesPassedRound1, DbType.Int32);
            parameters.Add("@TotalCandidatesPassedRound2", kpi.TotalCandidatesPassedRound2, DbType.Int32);
            parameters.Add("@TotalCandidatesOnboarded", kpi.TotalCandidatesOnboarded, DbType.Int32);
            parameters.Add("@TotalCandidatesFailed", kpi.TotalCandidatesFailed, DbType.Int32);
            parameters.Add("@TotalCandidatesOnboardedWarranty", kpi.TotalCandidatesOnboardedWarranty, DbType.Int32);
            parameters.Add("@SuccessRate", kpi.SuccessRate, DbType.Single);
            parameters.Add("@CalculatedAt", kpi.CalculatedAt, DbType.DateTime);

            await connection.ExecuteAsync(
                "sp_CreateCollaboratorKpi",
                parameters,
                commandType: CommandType.StoredProcedure);

            return kpi.KpiId;
        }

        public async Task<bool> UpdateAsync(CollaboratorKpi kpi)
        {
            kpi.CalculatedAt = DateTime.UtcNow;

            using var connection = _connectionFactory.CreateConnection();

            var parameters = new DynamicParameters();
            parameters.Add("@KpiId", kpi.KpiId, DbType.Guid);
            parameters.Add("@TotalCandidatesImported", kpi.TotalCandidatesImported, DbType.Int32);
            parameters.Add("@TotalCandidatesPassedRound1", kpi.TotalCandidatesPassedRound1, DbType.Int32);
            parameters.Add("@TotalCandidatesPassedRound2", kpi.TotalCandidatesPassedRound2, DbType.Int32);
            parameters.Add("@TotalCandidatesOnboarded", kpi.TotalCandidatesOnboarded, DbType.Int32);
            parameters.Add("@TotalCandidatesFailed", kpi.TotalCandidatesFailed, DbType.Int32);
            parameters.Add("@TotalCandidatesOnboardedWarranty", kpi.TotalCandidatesOnboardedWarranty, DbType.Int32);
            parameters.Add("@SuccessRate", kpi.SuccessRate, DbType.Single);
            parameters.Add("@CalculatedAt", kpi.CalculatedAt, DbType.DateTime);

            var rowsAffected = await connection.ExecuteAsync(
                "sp_UpdateCollaboratorKpi",
                parameters,
                commandType: CommandType.StoredProcedure);

            return rowsAffected > 0;
        }

        public async Task<bool> DeleteAsync(Guid kpiId)
        {
            using var connection = _connectionFactory.CreateConnection();

            var parameters = new DynamicParameters();
            parameters.Add("@KpiId", kpiId, DbType.Guid);

            var rowsAffected = await connection.ExecuteAsync(
                "sp_DeleteCollaboratorKpi",
                parameters,
                commandType: CommandType.StoredProcedure);

            return rowsAffected > 0;
        }

        public async Task<IEnumerable<CollaboratorKpi>> GetKpiSummaryByCollaboratorIdAsync(Guid collaboratorId)
        {
            using var connection = _connectionFactory.CreateConnection();

            var parameters = new DynamicParameters();
            parameters.Add("@CollaboratorId", collaboratorId, DbType.Guid);
             return await connection.QueryAsync<CollaboratorKpi>(
                "sp_GetCollaboratorKpisByPeriod",
                parameters,
                commandType: CommandType.StoredProcedure);
        }

        public async Task<object> GetKpiDetailByUserIdAsync(Guid userId)
        {
            using var connection = _connectionFactory.CreateConnection();

            var parameters = new DynamicParameters();
            parameters.Add("@UserId", userId, DbType.Guid);

            using var multi = await connection.QueryMultipleAsync(
                "sp_GetCollaboratorKpiDetail",
                parameters,
                commandType: CommandType.StoredProcedure);

            var currentPeriod = await multi.ReadFirstOrDefaultAsync<dynamic>();
            var progressDetails = (await multi.ReadAsync<dynamic>()).ToList();
            var kpiHistory = (await multi.ReadAsync<dynamic>()).ToList();

            return new
            {
                CurrentPeriod = currentPeriod,
                ProgressDetails = progressDetails,
                KpiHistory = kpiHistory
            };
        }
    }
}
