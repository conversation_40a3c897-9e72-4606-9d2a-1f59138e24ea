using CollaboratorsGS.Domain.Entities;

namespace CollaboratorsGS.Domain.Repositories
{
    public interface IAuthRepository
    {
        Task<Authen> CreateTokenAsync(Authen token);
        Task<bool> RevokeTokenAsync(string refreshToken);
        Task<Authen?> GetByRefreshTokenAsync(string refreshToken);
        Task<bool> RevokeAllUserTokensAsync(Guid userId);

        Task<PasswordResetToken> CreatePasswordResetTokenAsync(PasswordResetToken token);
        Task<PasswordResetToken?> GetPasswordResetTokenAsync(string token);
        Task<bool> InvalidatePasswordResetTokenAsync(string token);

        Task<ExternalAuthInfo?> GetExternalAuthInfoAsync(string provider, string externalId);
        Task<ExternalAuthInfo> CreateExternalAuthInfoAsync(ExternalAuthInfo externalAuthInfo);
        Task<bool> UpdateExternalAuthInfoAsync(ExternalAuthInfo externalAuthInfo);
    }
}
