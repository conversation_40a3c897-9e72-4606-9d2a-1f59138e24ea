FROM mcr.microsoft.com/dotnet/aspnet:8.0 AS base
WORKDIR /app
EXPOSE 80
EXPOSE 443

FROM mcr.microsoft.com/dotnet/sdk:8.0 AS build
WORKDIR /src
COPY ["CollaboratorsGS.API/CollaboratorsGS.API.csproj", "CollaboratorsGS.API/"]
COPY ["CollaboratorsGS.Application/CollaboratorsGS.Application.csproj", "CollaboratorsGS.Application/"]
COPY ["CollaboratorsGS.Domain/CollaboratorsGS.Domain.csproj", "CollaboratorsGS.Domain/"]
COPY ["CollaboratorsGS.Infrastructure/CollaboratorsGS.Infrastructure.csproj", "CollaboratorsGS.Infrastructure/"]
COPY ["CollaboratorsGS.ServiceDefaults/CollaboratorsGS.ServiceDefaults.csproj", "CollaboratorsGS.ServiceDefaults/"]
COPY ["CollaboratorsGS.Database/CollaboratorsGS.Database.csproj", "CollaboratorsGS.Database/"]
RUN dotnet workload install aspire
RUN dotnet restore "CollaboratorsGS.API/CollaboratorsGS.API.csproj"
COPY . .
WORKDIR "/src/CollaboratorsGS.API"
RUN dotnet build "CollaboratorsGS.API.csproj" -c Release -o /app/build

FROM build AS publish
RUN dotnet publish "CollaboratorsGS.API.csproj" -c Release -o /app/publish /p:UseAppHost=false

FROM base AS final
WORKDIR /app
COPY --from=publish /app/publish .
ENTRYPOINT ["dotnet", "CollaboratorsGS.API.dll"]
