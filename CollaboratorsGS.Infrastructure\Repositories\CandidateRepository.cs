using CollaboratorsGS.Domain.Entities;
using CollaboratorsGS.Domain.Repositories;
using CollaboratorsGS.Infrastructure.Data;
using Dapper;
using System.Data;

namespace CollaboratorsGS.Infrastructure.Repositories
{
    public class CandidateRepository : ICandidateRepository
    {
        private readonly IConnectionFactory _connectionFactory;

        public CandidateRepository(IConnectionFactory connectionFactory)
        {
            _connectionFactory = connectionFactory;
        }

        public async Task<Candidate?> GetByIdAsync(Guid candidateId)
        {
            using var connection = _connectionFactory.CreateConnection();

            var candidateDictionary = new Dictionary<Guid, Candidate>();

            var result = await connection.QueryAsync<Candidate, Collaborator, Candidate>(
                "sp_GetCandidateById",
                (candidate, collaborator) =>
                {
                    if (!candidateDictionary.TryGetValue(candidate.CandidateId, out var existingCandidate))
                    {
                        existingCandidate = candidate;
                        existingCandidate.Collaborator = collaborator;
                        candidateDictionary.Add(candidate.CandidateId, existingCandidate);
                    }

                    return existingCandidate;
                },
                new { CandidateId = candidateId },
                splitOn: "collaborator_id",
                commandType: CommandType.StoredProcedure
            );
            return result.FirstOrDefault();
        }

        public async Task<IEnumerable<Candidate>> GetAllAsync()
        {
            using var connection = _connectionFactory.CreateConnection();

            var candidateDictionary = new Dictionary<Guid, Candidate>();

            var result = await connection.QueryAsync<Candidate, Collaborator, Candidate>(
                "sp_GetAllCandidates",

                (candidate, collaborator) =>
                {
                    if (!candidateDictionary.TryGetValue(candidate.CandidateId, out var existingCandidate))
                    {
                        existingCandidate = candidate;
                        existingCandidate.Collaborator = collaborator;
                        candidateDictionary.Add(candidate.CandidateId, existingCandidate);
                    }
                    return existingCandidate;
                },
                splitOn: "collaborator_id",
                commandType: CommandType.StoredProcedure
            );
            return candidateDictionary.Values;
        }

        public async Task<IEnumerable<Candidate>> GetByCollaboratorIdAsync(Guid collaboratorId)
        {
            using var connection = _connectionFactory.CreateConnection();

            var candidateDictionary = new Dictionary<Guid, Candidate>();

            var result = await connection.QueryAsync<Candidate, Collaborator, Candidate>(
                "sp_GetCandidatesByCollaboratorId",
                (candidate, collaborator) =>
                {
                    if (!candidateDictionary.TryGetValue(candidate.CandidateId, out var existingCandidate))
                    {
                        existingCandidate = candidate;
                        existingCandidate.Collaborator = collaborator;
                        candidateDictionary.Add(candidate.CandidateId, existingCandidate);
                    }

                    return existingCandidate;
                },
                new { CollaboratorId = collaboratorId },
                splitOn: "collaborator_id",
                commandType: CommandType.StoredProcedure
            );

            return candidateDictionary.Values;
        }

        public async Task<Guid> CreateAsync(Candidate candidate)
        {
            // Generate a new UUID if not provided
            if (candidate.CandidateId == Guid.Empty)
            {
                candidate.CandidateId = Guid.NewGuid();
            }

            candidate.CreatedAt = DateTime.UtcNow;
             var document = candidate.Documents?.FirstOrDefault();
            using var connection = _connectionFactory.CreateConnection();

            // Ensure DateOfBirth only includes date part, not time
            DateTime? dateOfBirth = candidate.DateOfBirth.HasValue
                ? new DateTime(candidate.DateOfBirth.Value.Year, candidate.DateOfBirth.Value.Month, candidate.DateOfBirth.Value.Day)
                : null;

            var parameters = new
            {
                candidate.CandidateId,
                candidate.FullName,
                candidate.PhoneNumber,
                candidate.Email,
                candidate.EducationLevel,
                candidate.WorkExperience,
                candidate.Skills,
                DateOfBirth = dateOfBirth,
                candidate.Gender,
                candidate.Address,
                candidate.Ward,
                candidate.District,
                candidate.Province,
                candidate.ProfilePicture,
                candidate.FullBodyPicture,
                candidate.HeightCm,
                candidate.WeightKg,
                candidate.Level,
                candidate.Source,
                candidate.CollaboratorId,
                candidate.CitizenId,
                candidate.CitizenIdAddress,
                candidate.CitizenIdIssueDate,
                candidate.CitizenIdIssuePlace,
                candidate.CreatedAt,

                DoccumentId = document?.DocumentId ?? Guid.NewGuid(),
                DocumentType = document?.DocumentType ?? "CV",
                FilePath = document?.FilePath ?? string.Empty,
                FileType = document?.FileType ?? "pdf"
            };

            await connection.ExecuteAsync(
                "sp_CreateCandidate",
                parameters,
                commandType: CommandType.StoredProcedure
            );

            return candidate.CandidateId;
        }
        public async Task<Guid> CreateAsync(Candidate candidate, CandidateDocument document)
        {
            // Generate a new UUID if not provided
            if (candidate.CandidateId == Guid.Empty)
            {
                candidate.CandidateId = Guid.NewGuid();
            }
            if (document.DocumentId == Guid.Empty)
            {
                document.DocumentId = Guid.NewGuid();
            }

            candidate.CreatedAt = DateTime.UtcNow;

            using var connection = _connectionFactory.CreateConnection();

            // Ensure DateOfBirth only includes date part, not time
            DateTime? dateOfBirth = candidate.DateOfBirth.HasValue
                ? new DateTime(candidate.DateOfBirth.Value.Year, candidate.DateOfBirth.Value.Month, candidate.DateOfBirth.Value.Day)
                : null;

            var candidateParameters = new
            {
                candidate.CandidateId,
                candidate.FullName,
                candidate.PhoneNumber,
                candidate.Email,
                candidate.EducationLevel,
                candidate.WorkExperience,
                candidate.Skills,
                DateOfBirth = dateOfBirth,
                candidate.Gender,
                candidate.Address,
                candidate.Ward,
                candidate.District,
                candidate.Province,
                candidate.ProfilePicture,
                candidate.FullBodyPicture,
                candidate.HeightCm,
                candidate.WeightKg,
                candidate.Level,
                candidate.Source,
                candidate.CollaboratorId,
                candidate.CitizenId,
                candidate.CitizenIdAddress,
                candidate.CitizenIdIssueDate,
                candidate.CitizenIdIssuePlace,
                candidate.CreatedAt
            };

            var documentParameters = new
            {
                document.DocumentId,
                candidate.CandidateId,
                document.DocumentType,
                document.FilePath,
                document.FileType,
                document.UploadedAt
            };

            await connection.ExecuteAsync(
                "sp_CreateCandidate",
                new { candidateParameters, documentParameters },
                commandType: CommandType.StoredProcedure
            );

            return candidate.CandidateId;
        }

        public async Task<bool> UpdateAsync(Candidate candidate)
        {
            candidate.UpdatedAt = DateTime.UtcNow;

            using var connection = _connectionFactory.CreateConnection();

            // Ensure DateOfBirth only includes date part, not time
            DateTime? dateOfBirth = candidate.DateOfBirth.HasValue
                ? new DateTime(candidate.DateOfBirth.Value.Year, candidate.DateOfBirth.Value.Month, candidate.DateOfBirth.Value.Day)
                : null;

            var parameters = new
            {
                candidate.CandidateId,
                candidate.FullName,
                candidate.PhoneNumber,
                candidate.Email,
                candidate.EducationLevel,
                candidate.WorkExperience,
                candidate.Skills,
                DateOfBirth = dateOfBirth,
                candidate.Gender,
                candidate.Address,
                candidate.ProfilePicture,
                candidate.FullBodyPicture,
                candidate.HeightCm,
                candidate.WeightKg,
                candidate.Level,
                candidate.Source,
                candidate.CollaboratorId,
                candidate.CitizenId,
                candidate.CitizenIdAddress,
                candidate.CitizenIdIssueDate,
                candidate.CitizenIdIssuePlace,
                candidate.UpdatedAt
            };

            var result = await connection.QuerySingleAsync<int>(
                "sp_UpdateCandidate",
                parameters,
                commandType: CommandType.StoredProcedure
            );

            return result > 0;
        }

        public async Task<bool> DeleteAsync(Guid candidateId)
        {
            using var connection = _connectionFactory.CreateConnection();

            var result = await connection.QuerySingleAsync<int>(
                "sp_DeleteCandidate",
                new { CandidateId = candidateId },
                commandType: CommandType.StoredProcedure
            );

            return result > 0;
        }

        public async Task<Candidate?> GetByEmailAsync(string? email)
        {
            // Return null if email is null or empty
            if (string.IsNullOrEmpty(email))
                return null;

            using var connection = _connectionFactory.CreateConnection();

            var candidateDictionary = new Dictionary<Guid, Candidate>();

            var result = await connection.QueryAsync<Candidate, Collaborator, Candidate>(
                "sp_GetCandidateByEmail",
                (candidate, collaborator) =>
                {
                    if (!candidateDictionary.TryGetValue(candidate.CandidateId, out var existingCandidate))
                    {
                        existingCandidate = candidate;
                        existingCandidate.Collaborator = collaborator;
                        candidateDictionary.Add(candidate.CandidateId, existingCandidate);
                    }

                    return existingCandidate;
                },
                new { Email = email },
                splitOn: "collaborator_id",
                commandType: CommandType.StoredProcedure
            );

            return result.FirstOrDefault();
        }

        public async Task<Candidate?> GetByCitizenIdAsync(string? citizenId)
        {
            // Return null if citizen_id is null or empty
            if (string.IsNullOrEmpty(citizenId))
                return null;

            using var connection = _connectionFactory.CreateConnection();

            var candidateDictionary = new Dictionary<Guid, Candidate>();

            var result = await connection.QueryAsync<Candidate, Collaborator, Candidate>(
                "sp_GetCandidateByCitizenId",
                (candidate, collaborator) =>
                {
                    if (!candidateDictionary.TryGetValue(candidate.CandidateId, out var existingCandidate))
                    {
                        existingCandidate = candidate;
                        existingCandidate.Collaborator = collaborator;
                        candidateDictionary.Add(candidate.CandidateId, existingCandidate);
                    }

                    return existingCandidate;
                },
                new { CitizenId = citizenId },
                splitOn: "collaborator_id",
                commandType: CommandType.StoredProcedure
            );

            return result.FirstOrDefault();
        }

        public async Task<Candidate?> GetByPhoneNumberAsync(string phoneNumber)
        {
            using var connection = _connectionFactory.CreateConnection();

            var candidateDictionary = new Dictionary<Guid, Candidate>();

            var result = await connection.QueryAsync<Candidate, Collaborator, Candidate>(
                "sp_GetCandidateByPhoneNumber",
                (candidate, collaborator) =>
                {
                    if (!candidateDictionary.TryGetValue(candidate.CandidateId, out var existingCandidate))
                    {
                        existingCandidate = candidate;
                        existingCandidate.Collaborator = collaborator;
                        candidateDictionary.Add(candidate.CandidateId, existingCandidate);
                    }

                    return existingCandidate;
                },
                new { PhoneNumber = phoneNumber },
                splitOn: "collaborator_id",
                commandType: CommandType.StoredProcedure
            );

            return result.FirstOrDefault();
        }

        public async Task<dynamic?> GetDetailByIdAsync(Guid candidateId)
        {
            using var connection = _connectionFactory.CreateConnection();

            var parameters = new DynamicParameters();
            parameters.Add("@CandidateId", candidateId, DbType.Guid);

            var result = await connection.QueryAsync<dynamic>(
                "sp_GetCandidateDetailById",
                parameters,
                commandType: CommandType.StoredProcedure
            );

            return result.FirstOrDefault();
        }

        public async Task<IEnumerable<dynamic>> GetAllDetailsAsync()
        {
            using var connection = _connectionFactory.CreateConnection();

            return await connection.QueryAsync<dynamic>(
                "sp_GetAllCandidateDetails",
                commandType: CommandType.StoredProcedure
            );
        }

        public async Task<IEnumerable<dynamic>> GetDetailsByCollaboratorIdAsync(Guid collaboratorId)
        {
            using var connection = _connectionFactory.CreateConnection();

            var parameters = new DynamicParameters();
            parameters.Add("@CollaboratorId", collaboratorId, DbType.Guid);

            return await connection.QueryAsync<dynamic>(
                "sp_GetCandidateDetailsByCollaboratorId",
                parameters,
                commandType: CommandType.StoredProcedure
            );
        }
    }
}
