-- Stored Procedures for CollaboratorViolation operations

-- Get CollaboratorViolation by ID
IF EXISTS (SELECT * FROM sys.objects WHERE type = 'P' AND name = 'sp_GetCollaboratorViolationById')
    DROP PROCEDURE sp_GetCollaboratorViolationById
GO

CREATE PROCEDURE sp_GetCollaboratorViolationById
    @ViolationId UNIQUEIDENTIFIER
AS
BEGIN
    SELECT v.*, 
           ctv.full_name as collaborator_name,
           u.full_name as handler_name
    FROM collaborator_violations v
    INNER JOIN collaborators ctv ON v.collaborator_id = ctv.collaborator_id
    LEFT JOIN users u ON v.handled_by = u.user_id
    WHERE v.violation_id = @ViolationId
END
GO

-- Get collaborator_violations by CollaboratorId
IF EXISTS (SELECT * FROM sys.objects WHERE type = 'P' AND name = 'sp_GetCollaboratorViolationsByCollaboratorId')
    DROP PROCEDURE sp_GetCollaboratorViolationsByCollaboratorId
GO

CREATE PROCEDURE sp_GetCollaboratorViolationsByCollaboratorId
    @CollaboratorId UNIQUEIDENTIFIER
AS
BEGIN
    SELECT v.*, 
           ctv.full_name as collaborator_name,
           u.full_name as handler_name
    FROM collaborator_violations v
    INNER JOIN collaborators ctv ON v.collaborator_id = ctv.collaborator_id
    LEFT JOIN users u ON v.handled_by = u.user_id
    WHERE v.collaborator_id = @CollaboratorId
    ORDER BY v.created_at DESC
END
GO

-- Get collaborator_violations by Type
IF EXISTS (SELECT * FROM sys.objects WHERE type = 'P' AND name = 'sp_GetCollaboratorViolationsByType')
    DROP PROCEDURE sp_GetCollaboratorViolationsByType
GO

CREATE PROCEDURE sp_GetCollaboratorViolationsByType
    @ViolationType VARCHAR(50)
AS
BEGIN
    SELECT v.*, 
           ctv.full_name as collaborator_name,
           u.full_name as handler_name
    FROM collaborator_violations v
    INNER JOIN collaborators ctv ON v.collaborator_id = ctv.collaborator_id
    LEFT JOIN users u ON v.handled_by = u.user_id
    WHERE v.violation_type = @ViolationType
    ORDER BY v.created_at DESC
END
GO

-- Get All collaborator_violations
IF EXISTS (SELECT * FROM sys.objects WHERE type = 'P' AND name = 'sp_GetAllCollaboratorViolations')
    DROP PROCEDURE sp_GetAllCollaboratorViolations
GO

CREATE PROCEDURE sp_GetAllCollaboratorViolations
AS
BEGIN
    SELECT v.*, 
           ctv.full_name as collaborator_name,
           u.full_name as handler_name
    FROM collaborator_violations v
    INNER JOIN collaborators ctv ON v.collaborator_id = ctv.collaborator_id
    LEFT JOIN users u ON v.handled_by = u.user_id
    ORDER BY v.created_at DESC
END
GO

-- Create CollaboratorViolation
IF EXISTS (SELECT * FROM sys.objects WHERE type = 'P' AND name = 'sp_CreateCollaboratorViolation')
    DROP PROCEDURE sp_CreateCollaboratorViolation
GO

CREATE PROCEDURE sp_CreateCollaboratorViolation
    @ViolationId UNIQUEIDENTIFIER,
    @CollaboratorId UNIQUEIDENTIFIER,
    @ViolationType VARCHAR(50),
    @Description NVARCHAR(MAX) = NULL,
    @CreatedAt DATETIME,
    @HandledBy UNIQUEIDENTIFIER = NULL,
    @HandledAt DATETIME = NULL
AS
BEGIN
    INSERT INTO collaborator_violations (
        violation_id, collaborator_id, violation_type, description, 
        created_at, handled_by, handled_at
    )
    VALUES (
        @ViolationId, @CollaboratorId, @ViolationType, @Description, 
        @CreatedAt, @HandledBy, @HandledAt
    )
END
GO

-- Update CollaboratorViolation
IF EXISTS (SELECT * FROM sys.objects WHERE type = 'P' AND name = 'sp_UpdateCollaboratorViolation')
    DROP PROCEDURE sp_UpdateCollaboratorViolation
GO

CREATE PROCEDURE sp_UpdateCollaboratorViolation
    @ViolationId UNIQUEIDENTIFIER,
    @ViolationType VARCHAR(50) = NULL,
    @Description NVARCHAR(MAX) = NULL,
    @HandledBy UNIQUEIDENTIFIER = NULL,
    @HandledAt DATETIME = NULL
AS
BEGIN
    UPDATE collaborator_violations
    SET violation_type = ISNULL(@ViolationType, violation_type),
        description = ISNULL(@Description, description),
        handled_by = @HandledBy,
        handled_at = @HandledAt
    WHERE violation_id = @ViolationId
END
GO

-- Delete CollaboratorViolation
IF EXISTS (SELECT * FROM sys.objects WHERE type = 'P' AND name = 'sp_DeleteCollaboratorViolation')
    DROP PROCEDURE sp_DeleteCollaboratorViolation
GO

CREATE PROCEDURE sp_DeleteCollaboratorViolation
    @ViolationId UNIQUEIDENTIFIER
AS
BEGIN
    DELETE FROM collaborator_violations
    WHERE violation_id = @ViolationId
END
GO
