using CollaboratorsGS.Domain.Entities;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace CollaboratorsGS.Domain.Repositories
{
    public interface IAuditLogRepository
    {
        Task<Guid> CreateAsync(AuditLogs auditLog);
        Task<IEnumerable<AuditLogs>> GetByUserIdAsync(Guid userId);
        Task<IEnumerable<AuditLogs>> GetByEntityTypeAndIdAsync(string entityType, Guid entityId);
        Task<IEnumerable<AuditLogs>> GetRecentActivitiesByUserIdAsync(Guid userId, int limit);
    }
}