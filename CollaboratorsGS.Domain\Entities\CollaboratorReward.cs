
namespace CollaboratorsGS.Domain.Entities
{
    public class CollaboratorReward
    {
        public Guid RewardId { get; set; }
        public Guid CollaboratorId { get; set; }
        public Guid ApplicationId { get; set; }
        public string RewardType { get; set; } = string.Empty;
        public decimal Amount { get; set; }
        public Guid LevelId { get; set; }
        public DateTime RewardDate { get; set; }
        public DateTime ScheduledPaymentDate { get; set; }
        public string Status { get; set; } = string.Empty;

        // Navigation properties
        public Collaborator? Collaborator { get; set; }
        public CandidateApplication? Application { get; set; }
        public CollaboratorLevel? Level { get; set; }
        public ICollection<CollaboratorRewardHistory>? PaymentHistory { get; set; }
    }
}
