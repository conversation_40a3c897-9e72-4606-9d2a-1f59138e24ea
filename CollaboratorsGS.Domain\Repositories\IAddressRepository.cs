using CollaboratorsGS.Domain.Entities;

namespace CollaboratorsGS.Domain.Repositories
{
    public interface IAddressRepository
    {
        Task<IEnumerable<Province>> GetAllProvincesAsync(bool isRestructure);
        Task<IEnumerable<District>> GetDistrictsByProvinceCodeAsync(string queryCode);
        Task<IEnumerable<Ward>> GetWardsByDistrictCodeAsync(string queryCode, bool isRestructure);
    }
}
