namespace CollaboratorsGS.Application.DTOs.AuditLog
{
    public class RecentActivityDto
    {
        public Guid LogId { get; set; }
        public string Action { get; set; } = string.Empty;
        public string EntityType { get; set; } = string.Empty;
        public Guid EntityId { get; set; }
        public string Title { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public string TimeAgo { get; set; } = string.Empty;
        public DateTime ActionDate { get; set; }
    }
}
