using CollaboratorsGS.Domain.Entities;

namespace CollaboratorsGS.Domain.Repositories
{
    public interface ICollaboratorKpiTargetRepository
    {
        Task<CollaboratorKpiTarget?> GetByIdAsync(Guid targetId);
        Task<IEnumerable<CollaboratorKpiTarget>> GetAllAsync();
        Task<IEnumerable<CollaboratorKpiTarget>> GetByCollaboratorIdAsync(Guid collaboratorId);
        Task<IEnumerable<CollaboratorKpiTarget>> GetByPeriodAsync(string period);
        Task<Guid> CreateAsync(CollaboratorKpiTarget target);
        Task<bool> UpdateAsync(CollaboratorKpiTarget target);
        Task<bool> DeleteAsync(Guid targetId);
    }
}
