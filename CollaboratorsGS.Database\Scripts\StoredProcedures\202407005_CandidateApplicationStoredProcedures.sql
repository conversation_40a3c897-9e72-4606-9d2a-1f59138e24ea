-- Stored Procedures for CandidateApplication operations

-- Get CandidateApplication by ID
IF EXISTS (SELECT * FROM sys.objects WHERE type = 'P' AND name = 'sp_GetCandidateApplicationById')
    DROP PROCEDURE sp_GetCandidateApplicationById
GO

CREATE PROCEDURE sp_GetCandidateApplicationById
    @ApplicationId UNIQUEIDENTIFIER
AS
BEGIN
    SELECT ca.*, c.full_name as candidate_name, rp.title
    FROM candidate_applications ca
    INNER JOIN candidates c ON ca.candidate_id = c.candidate_id
    INNER JOIN recruitment_postings rp ON ca.posting_id = rp.posting_id
    WHERE ca.application_id = @ApplicationId
END
GO

-- Get All candidate_applications
IF EXISTS (SELECT * FROM sys.objects WHERE type = 'P' AND name = 'sp_GetAllCandidateApplications')
    DROP PROCEDURE sp_GetAllCandidateApplications
GO

CREATE PROCEDURE sp_GetAllCandidateApplications
AS
BEGIN
    SELECT ca.*, c.full_name as candidate_name, rp.title
    FROM candidate_applications ca
    INNER JOIN candidates c ON ca.candidate_id = c.candidate_id
    INNER JOIN recruitment_postings rp ON ca.posting_id = rp.posting_id
    ORDER BY ca.application_date DESC
END
GO

-- Get candidate_applications by Candidate
IF EXISTS (SELECT * FROM sys.objects WHERE type = 'P' AND name = 'sp_GetCandidateApplicationsByCandidate')
    DROP PROCEDURE sp_GetCandidateApplicationsByCandidate
GO

CREATE PROCEDURE sp_GetCandidateApplicationsByCandidate
    @CandidateId UNIQUEIDENTIFIER
AS
BEGIN
    SELECT ca.*, c.full_name as candidate_name, rp.title
    FROM candidate_applications ca
    INNER JOIN candidates c ON ca.candidate_id = c.candidate_id
    INNER JOIN recruitment_postings rp ON ca.posting_id = rp.posting_id
    WHERE ca.candidate_id = @CandidateId
    ORDER BY ca.application_date DESC
END
GO

-- Get candidate_applications by Posting
IF EXISTS (SELECT * FROM sys.objects WHERE type = 'P' AND name = 'sp_GetCandidateApplicationsByPosting')
    DROP PROCEDURE sp_GetCandidateApplicationsByPosting
GO

CREATE PROCEDURE sp_GetCandidateApplicationsByPosting
    @PostingId UNIQUEIDENTIFIER
AS
BEGIN
    SELECT ca.*, c.full_name as candidate_name, rp.title
    FROM candidate_applications ca
    INNER JOIN candidates c ON ca.candidate_id = c.candidate_id
    INNER JOIN recruitment_postings rp ON ca.posting_id = rp.posting_id
    WHERE ca.posting_id = @PostingId
    ORDER BY ca.application_date DESC
END
GO

-- Get candidate_applications by Status
IF EXISTS (SELECT * FROM sys.objects WHERE type = 'P' AND name = 'sp_GetCandidateApplicationsByStatus')
    DROP PROCEDURE sp_GetCandidateApplicationsByStatus
GO

CREATE PROCEDURE sp_GetCandidateApplicationsByStatus
    @Status VARCHAR(50)
AS
BEGIN
    SELECT ca.*, c.full_name as candidate_name, rp.title
    FROM candidate_applications ca
    INNER JOIN candidates c ON ca.candidate_id = c.candidate_id
    INNER JOIN recruitment_postings rp ON ca.posting_id = rp.posting_id
    WHERE ca.status = @Status
    ORDER BY ca.application_date DESC
END
GO

-- Create CandidateApplication
IF EXISTS (SELECT * FROM sys.objects WHERE type = 'P' AND name = 'sp_CreateCandidateApplication')
    DROP PROCEDURE sp_CreateCandidateApplication
GO

CREATE PROCEDURE sp_CreateCandidateApplication
    @ApplicationId UNIQUEIDENTIFIER,
    @CandidateId UNIQUEIDENTIFIER,
    @PostingId UNIQUEIDENTIFIER,
    @ApplicationDate DATE,
    @Status VARCHAR(50)
AS
BEGIN
    INSERT INTO candidate_applications (application_id, candidate_id, posting_id, application_date, status)
    VALUES (@ApplicationId, @CandidateId, @PostingId, @ApplicationDate, @Status)
END
GO

-- Update CandidateApplication
IF EXISTS (SELECT * FROM sys.objects WHERE type = 'P' AND name = 'sp_UpdateCandidateApplication')
    DROP PROCEDURE sp_UpdateCandidateApplication
GO

CREATE PROCEDURE sp_UpdateCandidateApplication
    @ApplicationId UNIQUEIDENTIFIER,
    @Status VARCHAR(50),
    @InterviewRound1Result VARCHAR(50) = NULL,
    @InterviewRound1Date DATE = NULL,
    @InterviewRound2Result VARCHAR(50) = NULL,
    @InterviewRound2Date DATE = NULL,
    @OnboardDate DATE = NULL,
    @WarrantyEndDate DATE = NULL
AS
BEGIN
    UPDATE candidate_applications
    SET status = @status,
        interview_round1_result = @InterviewRound1Result,
        interview_round1_date = @InterviewRound1Date,
        interview_round2_result = @InterviewRound2Result,
        interview_round2_date = @InterviewRound2Date,
        onboard_date = @OnboardDate,
        warranty_end_date = @WarrantyEndDate,
        updated_at = GETDATE()
    WHERE application_id = @ApplicationId
END
GO

-- Delete CandidateApplication
IF EXISTS (SELECT * FROM sys.objects WHERE type = 'P' AND name = 'sp_DeleteCandidateApplication')
    DROP PROCEDURE sp_DeleteCandidateApplication
GO

CREATE PROCEDURE sp_DeleteCandidateApplication
    @ApplicationId UNIQUEIDENTIFIER
AS
BEGIN
    DELETE FROM candidate_applications
    WHERE application_id = @ApplicationId
END
GO
