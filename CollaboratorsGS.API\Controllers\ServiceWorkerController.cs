using CollaboratorsGS.Infrastructure.Configurations;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Options;
using System.Text;

namespace CollaboratorsGS.API.Controllers
{
    [Route("firebase-messaging-sw.js")]
    [ApiController]
    public class ServiceWorkerController : ControllerBase
    {
        private readonly FirebaseConfig _firebaseConfig;
        private readonly ILogger<ServiceWorkerController> _logger;

        public ServiceWorkerController(
            IOptions<FirebaseConfig> firebaseConfig,
            ILogger<ServiceWorkerController> logger)
        {
            _firebaseConfig = firebaseConfig.Value;
            _logger = logger;
        }

        [HttpGet]
        public IActionResult GetServiceWorker()
        {
            try
            {
                // Create the service worker JavaScript content with embedded configuration
                var serviceWorkerJs = GenerateServiceWorkerJs();
                
                // Return as JavaScript file
                return Content(serviceWorkerJs, "application/javascript");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error generating service worker");
                return StatusCode(500, "Error generating service worker");
            }
        }

        private string GenerateServiceWorkerJs()
        {
            var sb = new StringBuilder();
            
            // Add Firebase SDK imports
            sb.AppendLine("// Give the service worker access to Firebase Messaging.");
            sb.AppendLine("// Note that you can only use Firebase Messaging here. Other Firebase libraries");
            sb.AppendLine("// are not available in the service worker.");
            sb.AppendLine("importScripts('https://www.gstatic.com/firebasejs/9.6.1/firebase-app-compat.js');");
            sb.AppendLine("importScripts('https://www.gstatic.com/firebasejs/9.6.1/firebase-messaging-compat.js');");
            sb.AppendLine();
            
            // Add Firebase configuration from appsettings.json
            sb.AppendLine("// Initialize the Firebase app in the service worker by passing in");
            sb.AppendLine("// your app's Firebase config object from appsettings.json");
            sb.AppendLine("const firebaseConfig = {");
            sb.AppendLine($"  apiKey: \"{_firebaseConfig.ApiKey}\",");
            sb.AppendLine($"  authDomain: \"{_firebaseConfig.AuthDomain}\",");
            sb.AppendLine($"  projectId: \"{_firebaseConfig.ProjectId}\",");
            sb.AppendLine($"  storageBucket: \"{_firebaseConfig.StorageBucket}\",");
            sb.AppendLine($"  messagingSenderId: \"{_firebaseConfig.MessagingSenderId}\",");
            sb.AppendLine($"  appId: \"{_firebaseConfig.AppId}\",");
            sb.AppendLine($"  measurementId: \"{_firebaseConfig.MeasurementId}\"");
            sb.AppendLine("};");
            sb.AppendLine();
            
            // Initialize Firebase
            sb.AppendLine("// Initialize Firebase");
            sb.AppendLine("firebase.initializeApp(firebaseConfig);");
            sb.AppendLine();
            
            // Add messaging functionality
            sb.AppendLine("// Retrieve an instance of Firebase Messaging so that it can handle background messages.");
            sb.AppendLine("const messaging = firebase.messaging();");
            sb.AppendLine();
            
            // Handle background messages
            sb.AppendLine("// Handle background messages");
            sb.AppendLine("messaging.onBackgroundMessage((payload) => {");
            sb.AppendLine("  console.log('[firebase-messaging-sw.js] Received background message ', payload);");
            sb.AppendLine("  ");
            sb.AppendLine("  // Customize notification here");
            sb.AppendLine("  const notificationTitle = payload.notification.title;");
            sb.AppendLine("  const notificationOptions = {");
            sb.AppendLine("    body: payload.notification.body,");
            sb.AppendLine("    icon: '/logo.png',");
            sb.AppendLine("    badge: '/badge.png',");
            sb.AppendLine("    data: payload.data");
            sb.AppendLine("  };");
            sb.AppendLine();
            sb.AppendLine("  if (payload.notification.image) {");
            sb.AppendLine("    notificationOptions.image = payload.notification.image;");
            sb.AppendLine("  }");
            sb.AppendLine();
            sb.AppendLine("  self.registration.showNotification(notificationTitle, notificationOptions);");
            sb.AppendLine("});");
            sb.AppendLine();
            
            // Handle notification click
            sb.AppendLine("// Handle notification click");
            sb.AppendLine("self.addEventListener('notificationclick', (event) => {");
            sb.AppendLine("  console.log('[firebase-messaging-sw.js] Notification click: ', event);");
            sb.AppendLine("  ");
            sb.AppendLine("  event.notification.close();");
            sb.AppendLine("  ");
            sb.AppendLine("  // This looks to see if the current is already open and focuses if it is");
            sb.AppendLine("  event.waitUntil(");
            sb.AppendLine("    clients.matchAll({ type: 'window', includeUncontrolled: true })");
            sb.AppendLine("      .then((clientList) => {");
            sb.AppendLine("        // Get data from notification");
            sb.AppendLine("        const clickAction = event.notification.data && event.notification.data.click_action;");
            sb.AppendLine("        ");
            sb.AppendLine("        // Try to use click_action URL if available");
            sb.AppendLine("        const urlToOpen = clickAction || self.location.origin;");
            sb.AppendLine("        ");
            sb.AppendLine("        // Look for an existing window to use");
            sb.AppendLine("        for (let i = 0; i < clientList.length; i++) {");
            sb.AppendLine("          const client = clientList[i];");
            sb.AppendLine("          if (client.url === urlToOpen && 'focus' in client) {");
            sb.AppendLine("            return client.focus();");
            sb.AppendLine("          }");
            sb.AppendLine("        }");
            sb.AppendLine("        ");
            sb.AppendLine("        // If no existing window, open a new one");
            sb.AppendLine("        if (clients.openWindow) {");
            sb.AppendLine("          return clients.openWindow(urlToOpen);");
            sb.AppendLine("        }");
            sb.AppendLine("      })");
            sb.AppendLine("  );");
            sb.AppendLine("});");
            
            return sb.ToString();
        }
    }
}
