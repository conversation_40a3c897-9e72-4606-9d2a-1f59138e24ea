using CollaboratorsGS.Domain.Entities;

namespace CollaboratorsGS.Domain.Repositories
{
    public interface ICollaboratorViolationRepository
    {
        Task<CollaboratorViolation?> GetByIdAsync(Guid violationId);
        Task<IEnumerable<CollaboratorViolation>> GetAllAsync();
        Task<IEnumerable<CollaboratorViolation>> GetByCtvIdAsync(Guid ctvId);
        Task<IEnumerable<CollaboratorViolation>> GetByTypeAsync(string violationType);
        Task<Guid> CreateAsync(CollaboratorViolation violation);
        Task<bool> UpdateAsync(CollaboratorViolation violation);
        Task<bool> DeleteAsync(Guid violationId);
    }
}
