using CollaboratorsGS.Application.DTOs.CollaboratorKpiTarget;

namespace CollaboratorsGS.Application.Interfaces
{
    public interface ICollaboratorKpiTargetService
    {
        Task<CollaboratorKpiTargetDto?> GetByIdAsync(Guid targetId);
        Task<IEnumerable<CollaboratorKpiTargetDto>> GetAllAsync();
        Task<IEnumerable<CollaboratorKpiTargetDto>> GetByCollaboratorIdAsync(Guid collaboratorId);
        Task<IEnumerable<CollaboratorKpiTargetDto>> GetByPeriodAsync(string period);
        Task<Guid> CreateCollaboratorKpiTargetAsync(CreateCollaboratorKpiTargetRequest request);
        Task<CollaboratorKpiTargetDto?> GetCreatedCollaboratorKpiTargetAsync(Guid targetId);
        Task<CollaboratorKpiTargetDto?> UpdateCollaboratorKpiTargetAsync(UpdateCollaboratorKpiTargetRequest request);
        Task<bool> DeleteCollaboratorKpiTargetAsync(Guid targetId);
    }
}
