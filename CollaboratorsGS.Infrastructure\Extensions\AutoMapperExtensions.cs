using AutoMapper;
using Microsoft.Extensions.DependencyInjection;

namespace CollaboratorsGS.Infrastructure.Extensions
{
    public static class AutoMapperExtensions
    {
        public static IServiceCollection AddAutoMapperProfiles(this IServiceCollection services)
        {
            // AutoMapper 13.0.1 uses a different approach
            services.AddSingleton(provider => new MapperConfiguration(cfg =>
            {
                cfg.AddProfile<Application.Mappings.MappingProfile>();
            }).CreateMapper());

            return services;
        }
    }
}
