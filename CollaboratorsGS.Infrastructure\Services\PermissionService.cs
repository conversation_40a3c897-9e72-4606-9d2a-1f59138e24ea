using AutoMapper;
using CollaboratorsGS.Application.DTOs;
using CollaboratorsGS.Application.Interfaces;
using CollaboratorsGS.Domain.Entities;
using CollaboratorsGS.Domain.Repositories;

namespace CollaboratorsGS.Infrastructure.Services
{
    public class PermissionService : IPermissionService
    {
        private readonly IPermissionRepository _permissionRepository;
        private readonly IMapper _mapper;

        public PermissionService(IPermissionRepository permissionRepository, IMapper mapper)
        {
            _permissionRepository = permissionRepository;
            _mapper = mapper;
        }

        public async Task<IEnumerable<PermissionDto>> GetAllPermissionsAsync()
        {
            var permissions = await _permissionRepository.GetAllAsync();
            return _mapper.Map<IEnumerable<PermissionDto>>(permissions);
        }

        public async Task<PermissionDto?> GetPermissionByIdAsync(Guid permissionId)
        {
            var permission = await _permissionRepository.GetByIdAsync(permissionId);
            return permission != null ? _mapper.Map<PermissionDto>(permission) : null;
        }

        public async Task<Guid> CreatePermissionAsync(CreatePermissionRequest request)
        {
            var permission = new Permission
            {
                PermissionName = request.PermissionName,
                Description = request.Description
            };

            return await _permissionRepository.CreateAsync(permission);
        }

        public async Task<bool> UpdatePermissionAsync(PermissionDto permissionDto)
        {
            var permission = await _permissionRepository.GetByIdAsync(permissionDto.PermissionId);
            if (permission == null)
                return false;

            permission.PermissionName = permissionDto.PermissionName;
            permission.Description = permissionDto.Description;

            return await _permissionRepository.UpdateAsync(permission);
        }

        public async Task<bool> DeletePermissionAsync(Guid permissionId)
        {
            return await _permissionRepository.DeleteAsync(permissionId);
        }
    }
}
