using CollaboratorsGS.Application.DTOs.CandidateApplication;

namespace CollaboratorsGS.Application.Interfaces
{
    public interface ICandidateApplicationService
    {
        Task<CandidateApplicationDto?> GetByIdAsync(Guid applicationId);
        Task<IEnumerable<CandidateApplicationDto>> GetAllAsync();
        Task<IEnumerable<CandidateApplicationDto>> GetByCandidateAsync(Guid candidateId);
        Task<IEnumerable<CandidateApplicationDto>> GetByPostingAsync(Guid postingId);
        Task<IEnumerable<CandidateApplicationDto>> GetByStatusAsync(string status);
        Task<Guid> CreateApplicationAsync(CreateCandidateApplicationRequest request);
        Task<bool> UpdateApplicationAsync(UpdateCandidateApplicationRequest request);
        Task<bool> DeleteApplicationAsync(Guid applicationId);

        // New methods for candidate applied positions and history
        Task<IEnumerable<CandidateAppliedHistoryDto>> GetCandidateAppliedHistoriesAsync(Guid candidateId, Guid? postingId = null);
        Task<IEnumerable<CandidateApplicationDetailDto>> GetCandidateApplicationDetailAsync(Guid candidateId, Guid? postingId = null);
    }
}
