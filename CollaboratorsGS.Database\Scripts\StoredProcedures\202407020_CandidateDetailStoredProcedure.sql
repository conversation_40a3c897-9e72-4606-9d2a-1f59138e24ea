-- Stored Procedure for getting Candidate details with application and posting information

-- Get Candidate Detail by ID
IF EXISTS (SELECT * FROM sys.objects WHERE type = 'P' AND name = 'sp_GetCandidateDetailById')
    DROP PROCEDURE sp_GetCandidateDetailById
GO

CREATE PROCEDURE sp_GetCandidateDetailById
    @CandidateId UNIQUEIDENTIFIER
AS
BEGIN
    SELECT
        c.candidate_id,
        c.full_name,
        c.phone_number,
        c.email,
        c.date_of_birth,
        c.gender,
        c.address,
        c.profile_picture,
        c.level,
        c.source,
        c.collaborator_id,
        c.created_at,
        c.updated_at,
        ctv.full_name AS collaborator_name,
        ca.application_date,
        ca.status AS application_status,
        ca.interview_round1_date,
        ca.interview_round1_result,
        ca.interview_round2_date,
        ca.interview_round2_result,
        ca.onboard_date,
        ca.posting_id,
        rp.title
        -- p.position_name,
        -- d.department_name
    FROM candidates c
    LEFT JOIN collaborators ctv ON c.collaborator_id = ctv.collaborator_id
    LEFT JOIN candidate_applications ca ON c.candidate_id = ca.candidate_id
    LEFT JOIN recruitment_postings rp ON ca.posting_id = rp.posting_id
    -- LEFT JOIN positions p ON rp.position_id = p.position_id
    -- LEFT JOIN departments d ON rp.department_id = d.department_id
    WHERE c.candidate_id = @CandidateId
END
GO


-- Get All Candidate Details
IF EXISTS (SELECT * FROM sys.objects WHERE type = 'P' AND name = 'sp_GetAllCandidateDetails')
    DROP PROCEDURE sp_GetAllCandidateDetails
GO

CREATE PROCEDURE sp_GetAllCandidateDetails
AS
BEGIN
    SELECT
        c.candidate_id,
        c.full_name,
        c.phone_number,
        c.email,
        c.date_of_birth,
        c.gender,
        c.address,
        c.profile_picture,
        c.level,
        c.source,
        c.collaborator_id,
        c.created_at,
        c.updated_at,
        ctv.full_name AS collaborator_name,
        ca.application_date,
        ca.status AS application_status,
        ca.interview_round1_date,
        ca.interview_round1_result,
        ca.interview_round2_date,
        ca.interview_round2_result,
        ca.onboard_date,
        ca.posting_id,
        rp.title
        -- p.position_name,
        -- d.department_name
    FROM candidates c
    LEFT JOIN collaborators ctv ON c.collaborator_id = ctv.collaborator_id
    LEFT JOIN candidate_applications ca ON c.candidate_id = ca.candidate_id
    LEFT JOIN recruitment_postings rp ON ca.posting_id = rp.posting_id
    -- LEFT JOIN positions p ON rp.position_id = p.position_id
    -- LEFT JOIN departments d ON rp.department_id = d.department_id
END
GO

-- Get Candidate Details by Collaborator ID
IF EXISTS (SELECT * FROM sys.objects WHERE type = 'P' AND name = 'sp_GetCandidateDetailsByCollaboratorId')
    DROP PROCEDURE sp_GetCandidateDetailsByCollaboratorId
GO

CREATE PROCEDURE sp_GetCandidateDetailsByCollaboratorId
    @CollaboratorId UNIQUEIDENTIFIER
AS
BEGIN
    SELECT
        c.candidate_id,
        c.full_name,
        c.phone_number,
        c.email,
        c.date_of_birth,
        c.gender,
        c.address,
        c.profile_picture,
        c.level,
        c.source,
        c.collaborator_id,
        c.created_at,
        c.updated_at,
        ctv.full_name AS collaborator_name,
        ca.application_date,
        ca.status AS application_status,
        ca.interview_round1_date,
        ca.interview_round1_result,
        ca.interview_round2_date,
        ca.interview_round2_result,
        ca.onboard_date,
        ca.posting_id,
        rp.title
        -- p.position_name,
        -- d.department_name
    FROM candidates c
    LEFT JOIN collaborators ctv ON c.collaborator_id = ctv.collaborator_id
    LEFT JOIN candidate_applications ca ON c.candidate_id = ca.candidate_id
    LEFT JOIN recruitment_postings rp ON ca.posting_id = rp.posting_id
    -- LEFT JOIN positions p ON rp.position_id = p.position_id
    -- LEFT JOIN departments d ON rp.department_id = d.department_id
    WHERE c.collaborator_id = @CollaboratorId
END
GO

