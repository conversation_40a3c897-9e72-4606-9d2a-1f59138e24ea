using CollaboratorsGS.Domain.Entities;

namespace CollaboratorsGS.Domain.Repositories
{
    public interface IPermissionRepository
    {
        Task<Permission?> GetByIdAsync(Guid permissionId);
        Task<Permission?> GetByNameAsync(string permissionName);
        Task<IEnumerable<Permission>> GetAllAsync();
        Task<Guid> CreateAsync(Permission permission);
        Task<bool> UpdateAsync(Permission permission);
        Task<bool> DeleteAsync(Guid permissionId);
    }
}
