-- Stored Procedures for CollaboratorReport operations

-- Get CollaboratorReport by ID
IF EXISTS (SELECT * FROM sys.objects WHERE type = 'P' AND name = 'sp_GetCollaboratorReportById')
    DROP PROCEDURE sp_GetCollaboratorReportById
GO

CREATE PROCEDURE sp_GetCollaboratorReportById
    @ReportId UNIQUEIDENTIFIER
AS
BEGIN
    SELECT r.*, ctv.full_name as collaborator_name
    FROM collaborator_reports r
    INNER JOIN collaborators ctv ON r.collaborator_id = ctv.collaborator_id

    WHERE r.collaborator_reports_id = @ReportId
END
GO

-- Get collaborator_reports by CollaboratorId
IF EXISTS (SELECT * FROM sys.objects WHERE type = 'P' AND name = 'sp_GetCollaboratorReportsByCollaboratorId')
    DROP PROCEDURE sp_GetCollaboratorReportsByCollaboratorId
GO

CREATE PROCEDURE sp_GetCollaboratorReportsByCollaboratorId
    @CollaboratorId UNIQUEIDENTIFIER
AS
BEGIN
    SELECT r.*, ctv.full_name as collaborator_name
    FROM collaborator_reports r
    INNER JOIN collaborators ctv ON r.collaborator_id = ctv.collaborator_id
    WHERE r.collaborator_id = @CollaboratorId
    ORDER BY r.report_period DESC
END
GO

-- Get Collaborator Dashboard Report by CollaboratorId
IF EXISTS (SELECT * FROM sys.objects WHERE type = 'P' AND name = 'sp_GetCollaboratorDashboardReport')
    DROP PROCEDURE sp_GetCollaboratorDashboardReport
GO

CREATE PROCEDURE sp_GetCollaboratorDashboardReport
       @CollaboratorId UNIQUEIDENTIFIER
AS
BEGIN
    DECLARE @CurrentPeriod NVARCHAR(7)
    SET @CurrentPeriod = FORMAT(GETDATE(), 'yyyy-MM')

    -- Get collaborator basic info and current level
    SELECT
        c.collaborator_id as CollaboratorId,
        c.full_name as CollaboratorName,
        c.level_id as LevelId,
        cl.level_name as LevelName,
        cl.min_kpi_threshold as MinKpiThreshold,
        cl.commission_rate as CommissionRate,
        cl.round1_bonus as Round1Bonus,
        cl.round2_bonus as Round2Bonus,
        cl.onboard_bonus as OnboardBonus,
        c.created_at as CreatedAt,
        c.status as Status
    FROM collaborators c
        INNER JOIN collaborator_levels cl ON c.level_id = cl.level_id
    WHERE c.collaborator_id = @CollaboratorId

    -- Get current period KPI data
    SELECT
        ck.collaborator_kpis_id as KpiId,
        ck.collaborator_id as CollaboratorId,
        ck.period as Period,
        COUNT(c.candidate_id) as TotalCandidatesImported,
        COUNT(CASE WHEN ca.interview_round1_result = 'Passed' THEN 1 END) as TotalCandidatesPassedRound1,
        COUNT(CASE WHEN ca.interview_round2_result = 'Passed' THEN 1 END) as TotalCandidatesPassedRound2,
        COUNT(ca.onboard_date) as TotalCandidatesOnboarded,
        COUNT(CASE WHEN ca.interview_round1_result = 'Failed' OR ca.interview_round2_result = 'Failed' THEN 1 END) as TotalCandidatesFailed,
        ck.total_candidates_onboarded_warranty as TotalCandidatesOnboardedWarranty,
        ck.success_rate as SuccessRate,
        ck.calculated_at as CalculatedAt
    FROM collaborator_kpis ck
        LEFT JOIN candidates c ON ck.collaborator_id = c.collaborator_id
        LEFT JOIN candidate_applications ca ON c.candidate_id = ca.candidate_id
    WHERE ck.collaborator_id = @CollaboratorId
    GROUP BY
        ck.collaborator_kpis_id,
        ck.collaborator_id,
        ck.period,
        ck.total_candidates_passed_round1,
        ck.total_candidates_passed_round2,
        ck.total_candidates_onboarded,
        ck.total_candidates_failed,
        ck.total_candidates_onboarded_warranty,
        ck.success_rate,
        ck.calculated_at;

    -- Get current period KPI targets
    SELECT
        ckt.collaborator_targets_id as TargetId,
        ckt.collaborator_id as CollaboratorId,
        ckt.period as Period,
        ckt.target_candidates_imported as TargetCandidatesImported,
        ckt.target_candidates_passed_round1 as TargetCandidatesPassedRound1,
        ckt.target_candidates_onboarded as TargetCandidatesOnboarded,
        ckt.created_at as CreatedAt,
        ckt.updated_at as UpdatedAt
    FROM collaborator_kpi_targets ckt
    WHERE ckt.collaborator_id = @CollaboratorId

    -- Get reward summary (paid and pending)
    SELECT
        SUM(CASE WHEN cr.status = 'Paid' THEN cr.amount ELSE 0 END) as TotalPaid,
        SUM(CASE WHEN cr.status = 'Pending' THEN cr.amount ELSE 0 END) as TotalPending,
        COUNT(CASE WHEN cr.status = 'Paid' THEN 1 END) as CountPaid,
        COUNT(CASE WHEN cr.status = 'Pending' THEN 1 END) as CountPending
    FROM collaborator_rewards cr
    WHERE cr.collaborator_id = @CollaboratorId
        AND FORMAT(cr.reward_date, 'yyyy-MM') = @CurrentPeriod

    -- Get historical KPI data for chart (last 6 months)
    SELECT
        ck.period as Period,
        ck.total_candidates_imported as TotalCandidatesImported,
        ck.total_candidates_passed_round1 as TotalCandidatesPassedRound1,
        ck.total_candidates_passed_round2 as TotalCandidatesPassedRound2,
        ck.total_candidates_onboarded as TotalCandidatesOnboarded,
        ck.total_candidates_failed as TotalCandidatesFailed,
        ck.total_candidates_onboarded_warranty as TotalCandidatesOnboardedWarranty
    FROM collaborator_kpis ck
    WHERE ck.collaborator_id = @CollaboratorId
        AND ck.period >= FORMAT(DATEADD(MONTH, -5, GETDATE()), 'yyyy-MM')
    ORDER BY ck.period ASC

    -- Get next level info (if exists)
    SELECT TOP 1
        cl.level_id as LevelId,
        cl.level_name as LevelName,
        cl.min_kpi_threshold as MinKpiThreshold,
        cl.commission_rate as CommissionRate
    FROM collaborator_levels cl
    WHERE cl.min_kpi_threshold > (
        SELECT ISNULL(cl2.min_kpi_threshold, 0)
    FROM collaborators c2
        INNER JOIN collaborator_levels cl2 ON c2.level_id = cl2.level_id
    WHERE c2.collaborator_id = @CollaboratorId
    )
    ORDER BY cl.min_kpi_threshold ASC

    SELECT
        c.candidate_id as CandidateId,
        c.full_name as FullName,
        c.profile_picture as ProfilePicture,
        ca.application_date as ApplicationDate,
        rp.position as Position,
        ca.status as Status,
        ISNULL(reward_summary.total_amount, 0) as RewardAmount,
        CASE
            WHEN reward_summary.total_amount IS NULL THEN 'No Reward'
            WHEN reward_summary.has_pending = 1 THEN 'Pending'
            ELSE 'Paid'
        END as RewardStatus,
        rp.title as JobTitle
    FROM candidates c
    INNER JOIN candidate_applications ca ON c.candidate_id = ca.candidate_id
    INNER JOIN recruitment_postings rp ON ca.posting_id = rp.posting_id
    LEFT JOIN (
        SELECT
            cr.application_id,
            SUM(cr.amount) as total_amount,
            CASE WHEN COUNT(CASE WHEN cr.status IN ('Pending', 'Processing') THEN 1 END) > 0 THEN 1 ELSE 0 END as has_pending
        FROM collaborator_rewards cr
        WHERE cr.reward_type IN ('PassV1', 'PassV2', 'Onboard')
        GROUP BY cr.application_id
    ) reward_summary ON ca.application_id = reward_summary.application_id
    WHERE c.collaborator_id = @CollaboratorId
    AND FORMAT(ca.application_date, 'yyyy-MM') = @CurrentPeriod
    ORDER BY ca.application_date DESC
END
GO

-- Get collaborator_reports by Period
IF EXISTS (SELECT * FROM sys.objects WHERE type = 'P' AND name = 'sp_GetCollaboratorReportsByPeriod')
    DROP PROCEDURE sp_GetCollaboratorReportsByPeriod
GO

CREATE PROCEDURE sp_GetCollaboratorReportsByPeriod
    @ReportPeriod VARCHAR(7)
AS
BEGIN
    SELECT r.*, ctv.full_name as collaborator_name
    FROM collaborator_reports r
    INNER JOIN collaborators ctv ON r.collaborator_id = ctv.collaborator_id
    WHERE r.report_period = @ReportPeriod
    ORDER BY ctv.full_name
END
GO

-- Get All collaborator_reports
IF EXISTS (SELECT * FROM sys.objects WHERE type = 'P' AND name = 'sp_GetAllCollaboratorReports')
    DROP PROCEDURE sp_GetAllCollaboratorReports
GO

CREATE PROCEDURE sp_GetAllCollaboratorReports
AS
BEGIN
    SELECT r.*, ctv.full_name as collaborator_name
    FROM collaborator_reports r
    INNER JOIN collaborators ctv ON r.collaborator_id = ctv.collaborator_id
    ORDER BY r.report_period DESC, ctv.full_name
END
GO

-- Create CollaboratorReport
IF EXISTS (SELECT * FROM sys.objects WHERE type = 'P' AND name = 'sp_CreateCollaboratorReport')
    DROP PROCEDURE sp_CreateCollaboratorReport
GO

CREATE PROCEDURE sp_CreateCollaboratorReport
    @ReportId UNIQUEIDENTIFIER,
    @CollaboratorId UNIQUEIDENTIFIER,
    @ReportPeriod VARCHAR(7),
    @TotalCandidates INT = 0,
    @TotalPayment DECIMAL(18, 2) = 0,
    @ReportDate DATE,
    @Data NVARCHAR(MAX) = NULL
AS
BEGIN
    INSERT INTO collaborator_reports (
        collaborator_reports_id, collaborator_id, report_period, total_candidates,
        total_payment, report_date, data
    )
    VALUES (
        @ReportId, @CollaboratorId, @ReportPeriod, @TotalCandidates,
        @TotalPayment, @ReportDate, @Data
    )
END
GO


-- Update CollaboratorReport
IF EXISTS (SELECT * FROM sys.objects WHERE type = 'P' AND name = 'sp_UpdateCollaboratorReport')
    DROP PROCEDURE sp_UpdateCollaboratorReport
GO

CREATE PROCEDURE sp_UpdateCollaboratorReport
    @ReportId UNIQUEIDENTIFIER,
    @TotalCandidates INT = NULL,
    @TotalPayment DECIMAL(18, 2) = NULL,
    @Data NVARCHAR(MAX) = NULL
AS
BEGIN
    UPDATE collaborator_reports
    SET total_candidates = ISNULL(@TotalCandidates, total_candidates),
        total_payment = ISNULL(@TotalPayment, total_payment),
        data = ISNULL(@Data, data)
    WHERE collaborator_reports_id = @ReportId
END
GO

-- Delete CollaboratorReport
IF EXISTS (SELECT * FROM sys.objects WHERE type = 'P' AND name = 'sp_DeleteCollaboratorReport')
    DROP PROCEDURE sp_DeleteCollaboratorReport
GO

CREATE PROCEDURE sp_DeleteCollaboratorReport
    @ReportId UNIQUEIDENTIFIER
AS
BEGIN
    DELETE FROM collaborator_reports
    WHERE collaborator_reports_id = @ReportId
END
GO




