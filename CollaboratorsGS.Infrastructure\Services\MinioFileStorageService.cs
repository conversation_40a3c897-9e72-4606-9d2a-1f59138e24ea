using CollaboratorsGS.Application.Interfaces;
using CollaboratorsGS.Infrastructure.Configurations;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Options;
using Minio;
using Minio.DataModel.Args;

namespace CollaboratorsGS.Infrastructure.Services
{
    public class MinioFileStorageService : IFileStorageService
    {
        private readonly FileStorageOptions _options;
        private readonly IMinioClient _minioClient;

        public MinioFileStorageService(IOptions<FileStorageOptions> options)
        {
            _options = options.Value;

            _minioClient = new MinioClient()
                .WithEndpoint(_options.MinioEndpoint)
                .WithCredentials(_options.MinioAccessKey, _options.MinioSecretKey)
                .WithSSL(_options.MinioWithSSL)
                .Build();
        }

        public async Task<string> UploadFileAsync(IFormFile file, string? fileName = null)
        {
            if (file == null || file.Length == 0)
                throw new ArgumentException("File is empty or null", nameof(file));

            try
            {
                if (string.IsNullOrEmpty(fileName))
                {
                    var extension = Path.GetExtension(file.FileName);
                    fileName = $"{Guid.NewGuid()}{extension}";
                }

                var bucketExistsArgs = new BucketExistsArgs()
                    .WithBucket(_options.MinioBucketName);

                bool bucketExists = await _minioClient.BucketExistsAsync(bucketExistsArgs);
                if (!bucketExists)
                {
                    var makeBucketArgs = new MakeBucketArgs()
                        .WithBucket(_options.MinioBucketName);

                    await _minioClient.MakeBucketAsync(makeBucketArgs);
                }

                using (var stream = file.OpenReadStream())
                {
                    var putObjectArgs = new PutObjectArgs()
                        .WithBucket(_options.MinioBucketName)
                        .WithObject(fileName)
                        .WithStreamData(stream)
                        .WithObjectSize(file.Length)
                        .WithContentType(file.ContentType);

                    await _minioClient.PutObjectAsync(putObjectArgs);
                }

                return fileName;
            }
            catch (Exception ex)
            {
                throw new Exception($"Error uploading file to Minio: {ex.Message}", ex);
            }
        }

        public async Task<bool> DeleteFileAsync(string fileName)
        {
            try
            {
                var removeObjectArgs = new RemoveObjectArgs()
                    .WithBucket(_options.MinioBucketName)
                    .WithObject(fileName);

                await _minioClient.RemoveObjectAsync(removeObjectArgs);
                return true;
            }
            catch
            {
                return false;
            }
        }

        public async Task<string> GetTemporaryUrlAsync(string fileName, int expiryMinutes = 60)
        {
            try
            {
                var presignedGetObjectArgs = new PresignedGetObjectArgs()
                    .WithBucket(_options.MinioBucketName)
                    .WithObject(fileName)
                    .WithExpiry(expiryMinutes * 60);

                return await _minioClient.PresignedGetObjectAsync(presignedGetObjectArgs);
            }
            catch
            {
                return $"http://{_options.MinioEndpoint}/{_options.MinioBucketName}/{fileName}";
            }
        }
    }
}
