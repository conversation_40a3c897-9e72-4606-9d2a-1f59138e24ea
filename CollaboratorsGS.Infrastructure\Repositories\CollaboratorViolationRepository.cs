using CollaboratorsGS.Domain.Entities;
using CollaboratorsGS.Domain.Repositories;
using CollaboratorsGS.Infrastructure.Data;
using Dapper;
using System.Data;

namespace CollaboratorsGS.Infrastructure.Repositories
{
    public class CollaboratorViolationRepository : ICollaboratorViolationRepository
    {
        private readonly IConnectionFactory _connectionFactory;

        public CollaboratorViolationRepository(IConnectionFactory connectionFactory)
        {
            _connectionFactory = connectionFactory;
        }

        public async Task<CollaboratorViolation?> GetByIdAsync(Guid violationId)
        {
            using var connection = _connectionFactory.CreateConnection();

            var parameters = new DynamicParameters();
            parameters.Add("@ViolationId", violationId, DbType.Guid);

            return await connection.QuerySingleOrDefaultAsync<CollaboratorViolation>(
                "sp_GetCollaboratorViolationById",
                parameters,
                commandType: CommandType.StoredProcedure);
        }

        public async Task<IEnumerable<CollaboratorViolation>> GetAllAsync()
        {
            using var connection = _connectionFactory.CreateConnection();

            return await connection.QueryAsync<CollaboratorViolation>(
                "sp_GetAllCollaboratorViolations",
                commandType: CommandType.StoredProcedure);
        }

        public async Task<IEnumerable<CollaboratorViolation>> GetByCtvIdAsync(Guid ctvId)
        {
            using var connection = _connectionFactory.CreateConnection();

            var parameters = new DynamicParameters();
            parameters.Add("@CtvId", ctvId, DbType.Guid);

            return await connection.QueryAsync<CollaboratorViolation>(
                "sp_GetCollaboratorViolationsByCtvId",
                parameters,
                commandType: CommandType.StoredProcedure);
        }

        public async Task<IEnumerable<CollaboratorViolation>> GetByTypeAsync(string violationType)
        {
            using var connection = _connectionFactory.CreateConnection();

            var parameters = new DynamicParameters();
            parameters.Add("@ViolationType", violationType, DbType.String);

            return await connection.QueryAsync<CollaboratorViolation>(
                "sp_GetCollaboratorViolationsByType",
                parameters,
                commandType: CommandType.StoredProcedure);
        }

        public async Task<Guid> CreateAsync(CollaboratorViolation violation)
        {
            // Generate a new UUID if not provided
            if (violation.ViolationId == Guid.Empty)
            {
                violation.ViolationId = Guid.NewGuid();
            }

            violation.CreatedAt = DateTime.UtcNow;

            using var connection = _connectionFactory.CreateConnection();

            var parameters = new DynamicParameters();
            parameters.Add("@ViolationId", violation.ViolationId, DbType.Guid);
            parameters.Add("@CtvId", violation.CollaboratorId, DbType.Guid);
            parameters.Add("@ViolationType", violation.ViolationType, DbType.String);
            parameters.Add("@Description", violation.Description, DbType.String);
            parameters.Add("@CreatedAt", violation.CreatedAt, DbType.DateTime);
            parameters.Add("@HandledBy", violation.HandledBy, DbType.Guid);
            parameters.Add("@HandledAt", violation.HandledAt, DbType.DateTime);

            await connection.ExecuteAsync(
                "sp_CreateCollaboratorViolation",
                parameters,
                commandType: CommandType.StoredProcedure);

            return violation.ViolationId;
        }

        public async Task<bool> UpdateAsync(CollaboratorViolation violation)
        {
            using var connection = _connectionFactory.CreateConnection();

            var parameters = new DynamicParameters();
            parameters.Add("@ViolationId", violation.ViolationId, DbType.Guid);
            parameters.Add("@ViolationType", violation.ViolationType, DbType.String);
            parameters.Add("@Description", violation.Description, DbType.String);
            parameters.Add("@HandledBy", violation.HandledBy, DbType.Guid);
            parameters.Add("@HandledAt", violation.HandledAt, DbType.DateTime);

            var rowsAffected = await connection.ExecuteAsync(
                "sp_UpdateCollaboratorViolation",
                parameters,
                commandType: CommandType.StoredProcedure);

            return rowsAffected > 0;
        }

        public async Task<bool> DeleteAsync(Guid violationId)
        {
            using var connection = _connectionFactory.CreateConnection();

            var parameters = new DynamicParameters();
            parameters.Add("@ViolationId", violationId, DbType.Guid);

            var rowsAffected = await connection.ExecuteAsync(
                "sp_DeleteCollaboratorViolation",
                parameters,
                commandType: CommandType.StoredProcedure);

            return rowsAffected > 0;
        }
    }
}
