using CollaboratorsGS.Application.DTOs.CollaboratorViolation;

namespace CollaboratorsGS.Application.Interfaces
{
    public interface ICollaboratorViolationService
    {
        Task<IEnumerable<CollaboratorViolationDto>> GetAllAsync();
        Task<CollaboratorViolationDto?> GetByIdAsync(Guid violationId);
        Task<IEnumerable<CollaboratorViolationDto>> GetByCollaboratorIdAsync(Guid collaboratorId);
        Task<IEnumerable<CollaboratorViolationDto>> GetByTypeAsync(string violationType);
        Task<Guid> CreateViolationAsync(CreateCollaboratorViolationRequest request);
        Task<bool> UpdateViolationAsync(UpdateCollaboratorViolationRequest request);
        Task<bool> DeleteViolationAsync(Guid violationId);
    }
}
