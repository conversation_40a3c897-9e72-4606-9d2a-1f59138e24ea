using CollaboratorsGS.Application.DTOs.CollaboratorKpi;

namespace CollaboratorsGS.Application.Interfaces
{
    public interface ICollaboratorKpiService
    {
        Task<CollaboratorKpiDto?> GetByIdAsync(Guid kpiId);
        Task<IEnumerable<CollaboratorKpiDto>> GetAllAsync();
        Task<IEnumerable<CollaboratorKpiDto>> GetByCollaboratorIdAsync(Guid collaboratorId);
        Task<IEnumerable<CollaboratorKpiDto>> GetByPeriodAsync(string period);
        Task<Guid> CreateCollaboratorKpiAsync(CreateCollaboratorKpiRequest request);
        Task<CollaboratorKpiDto?> GetCreatedCollaboratorKpiAsync(Guid kpiId);
        Task<CollaboratorKpiDto?> UpdateCollaboratorKpiAsync(UpdateCollaboratorKpiRequest request);
        Task<bool> DeleteCollaboratorKpiAsync(Guid kpiId);
        Task<bool> CalculateKpiAsync(Guid collaboratorId, string period);

        Task<CollaboratorKpiReport> GetKpiSummaryByCollaboratorIdAsync(Guid collaboratorId);
        Task<CollaboratorKpiDetailDto> GetKpiDetailByUserIdAsync(Guid userId);
    }
}
