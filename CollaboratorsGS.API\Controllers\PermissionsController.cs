using CollaboratorsGS.Application.Constants;
using CollaboratorsGS.Application.DTOs;
using CollaboratorsGS.Application.Interfaces;
using CollaboratorsGS.Infrastructure.Utilities;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace CollaboratorsGS.API.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    [Authorize(Roles = RolesUser.Admin)]
    public class PermissionsController : ControllerBase
    {
        private readonly IPermissionService _permissionService;

        public PermissionsController(IPermissionService permissionService)
        {
            _permissionService = permissionService;
        }

        [HttpGet]
        public async Task<IActionResult> GetAll()
        {
            var permissions = await _permissionService.GetAllPermissionsAsync();
            return Ok(ApiResponse<object>.SuccessResponse(
                MessageCodes.SC2000,
                "Get all permissions successfully",
                permissions));
        }

        [HttpGet("{id}")]
        public async Task<IActionResult> GetById(Guid id)
        {
            var permission = await _permissionService.GetPermissionByIdAsync(id);
            
            if (permission == null)
                return NotFound(ApiResponse<object>.ErrorResponse(
                    MessageCodes.ER4004,
                    "Permission not found",
                    404));
            
            return Ok(ApiResponse<object>.SuccessResponse(
                MessageCodes.SC2000,
                "Get permission by id successfully",
                permission));
        }

        [HttpPost]
        public async Task<IActionResult> Create([FromBody] CreatePermissionRequest request)
        {
            var permissionId = await _permissionService.CreatePermissionAsync(request);
            return CreatedAtAction(nameof(GetById), new { id = permissionId , PermissionId = permissionId }, 
            ApiResponse<object>.SuccessResponse(
                MessageCodes.SC2001,
                "Permission created successfully",
                201));
        }

        [HttpPut("{id}")]
        public async Task<IActionResult> Update(Guid id, [FromBody] PermissionDto permissionDto)
        {
            if (id != permissionDto.PermissionId)
                return BadRequest(ApiResponse<object>.ErrorResponse(
                    MessageCodes.ER4001,
                    "ID mismatch",
                    400,
                    new List<ErrorDetail>
                    {
                        new ErrorDetail
                        {
                            Field = "id",
                            ErrorCode = MessageCodes.ER4001,
                            Message = "Permission ID in URL does not match request body"
                        }
                    }));
            
            var result = await _permissionService.UpdatePermissionAsync(permissionDto);
            
            if (!result)
                return NotFound(ApiResponse<object>.ErrorResponse(
                    MessageCodes.ER4004,
                    "Permission not found",
                    404));
            
            return Ok(ApiResponse<object>.SuccessResponse(
                MessageCodes.SC2002,
                "Permission updated successfully"));
        }

        [HttpDelete("{id}")]
        public async Task<IActionResult> Delete(Guid id)
        {
            var result = await _permissionService.DeletePermissionAsync(id);
            
            if (!result)
                return NotFound(ApiResponse<object>.ErrorResponse(
                    MessageCodes.ER4004,
                    "Permission not found",
                    404));
            
            return Ok(ApiResponse<object>.SuccessResponse(
                MessageCodes.SC2003,
                "Permission deleted successfully"));
        }
    }
}
