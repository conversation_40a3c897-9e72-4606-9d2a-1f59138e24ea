using System.Text;
using Dapper;

public static class DapperParamHelper
{
    public static DynamicParameters ToSnakeCaseParams<T>(T obj)
    {
        var parameters = new DynamicParameters();
        foreach (var prop in typeof(T).GetProperties())
        {
            var snakeName = ToSnakeCase(prop.Name);
            var value = prop.GetValue(obj);
            parameters.Add(snakeName, value);
        }
        return parameters;
    }

    public static string ToSnakeCase(string input)
    {
        if (string.IsNullOrEmpty(input))
            return input;

        var sb = new StringBuilder();
        for (int i = 0; i < input.Length; i++)
        {
            var c = input[i];
            if (char.IsUpper(c))
            {
                if (i > 0 && (char.<PERSON><PERSON>ower(input[i - 1]) || (i + 1 < input.Length && char.IsLower(input[i + 1]))))
                {
                    sb.Append('_');
                }
                sb.Append(char.ToLowerInvariant(c));
            }
            else
            {
                sb.Append(c);
            }
        }

        return sb.ToString();
    }

}
