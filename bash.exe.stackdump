Stack trace:
Frame         Function      Args
0007FFFF8E90  00021005FEBA (000210285F48, 00021026AB6E, 000000000000, 0007FFFF7D90) msys-2.0.dll+0x1FEBA
0007FFFF8E90  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFF9168) msys-2.0.dll+0x67F9
0007FFFF8E90  000210046832 (000210285FF9, 0007FFFF8D48, 000000000000, 000000000000) msys-2.0.dll+0x6832
0007FFFF8E90  000210068F86 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28F86
0007FFFF8E90  0002100690B4 (0007FFFF8EA0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x290B4
0007FFFF9170  00021006A49D (0007FFFF8EA0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A49D
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFBF69A0000 ntdll.dll
7FFBF59C0000 KERNEL32.DLL
7FFBF40F0000 KERNELBASE.dll
7FFBF5280000 USER32.dll
7FFBF4700000 win32u.dll
000210040000 msys-2.0.dll
7FFBF5450000 GDI32.dll
7FFBF3C40000 gdi32full.dll
7FFBF3B90000 msvcp_win.dll
7FFBF3D80000 ucrtbase.dll
7FFBF5F20000 advapi32.dll
7FFBF47E0000 msvcrt.dll
7FFBF5910000 sechost.dll
7FFBF5160000 RPCRT4.dll
7FFBF30C0000 CRYPTBASE.DLL
7FFBF3ED0000 bcryptPrimitives.dll
7FFBF5120000 IMM32.DLL
