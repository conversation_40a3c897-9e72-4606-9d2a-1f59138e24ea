-- Stored Procedures for RecruitmentPosting operations (Updated for new structure)

-- Get RecruitmentPosting by ID
IF EXISTS (SELECT * FROM sys.objects WHERE type = 'P' AND name = 'sp_GetRecruitmentPostingById')
    DROP PROCEDURE sp_GetRecruitmentPostingById
GO

CREATE PROCEDURE sp_GetRecruitmentPostingById
    @posting_id UNIQUEIDENTIFIER
AS
BEGIN
    SELECT
        posting_id,
        refer_code,
        title,
        project,
        level,
        position,
        salary_from,
        salary_to,
        commission,
        commission_warranty_months,
        working_location,
        working_time,
        view_count,
        referral_count,
        is_urgent,
        is_hot,
        is_saved,
        status,
        job_detail_json,
        created_at,
        updated_at,
        expired_at
    FROM recruitment_postings
    WHERE posting_id = @posting_id
END
GO

-- Get RecruitmentPosting by ID with detail (same as above since we use JSON)
IF EXISTS (SELECT * FROM sys.objects WHERE type = 'P' AND name = 'sp_GetRecruitmentPostingByIdWithDetail')
    DROP PROCEDURE sp_GetRecruitmentPostingByIdWithDetail
GO

CREATE PROCEDURE sp_GetRecruitmentPostingByIdWithDetail
    @posting_id UNIQUEIDENTIFIER
AS
BEGIN
    SELECT
        posting_id,
        refer_code,
        title,
        project,
        level,
        position,
        salary_from,
        salary_to,
        commission,
        commission_warranty_months,
        working_location,
        working_time,
        view_count,
        referral_count,
        is_urgent,
        is_hot,
        is_saved,
        status,
        job_detail_json,
        created_at,
        updated_at,
        expired_at
    FROM recruitment_postings
    WHERE posting_id = @posting_id
END
GO

-- Get All Active recruitment_postings
IF EXISTS (SELECT * FROM sys.objects WHERE type = 'P' AND name = 'sp_GetAllRecruitmentPostings')
    DROP PROCEDURE sp_GetAllRecruitmentPostings
GO

CREATE PROCEDURE sp_GetAllRecruitmentPostings
AS
BEGIN
    SELECT
        posting_id,
        refer_code,
        title,
        project,
        level,
        position,
        salary_from,
        salary_to,
        commission,
        commission_warranty_months,
        working_location,
        working_time,
        view_count,
        referral_count,
        is_urgent,
        is_hot,
        is_saved,
        status,
        job_detail_json,
        created_at,
        updated_at,
        expired_at
    FROM recruitment_postings
    WHERE status = 1
    ORDER BY created_at DESC
END
GO

-- Get recruitment_postings by Refer Code
IF EXISTS (SELECT * FROM sys.objects WHERE type = 'P' AND name = 'sp_GetRecruitmentPostingsByReferCode')
    DROP PROCEDURE sp_GetRecruitmentPostingsByReferCode
GO

CREATE PROCEDURE sp_GetRecruitmentPostingsByReferCode
    @refer_code NVARCHAR(50)
AS
BEGIN
    SELECT
        posting_id,
        refer_code,
        title,
        project,
        level,
        position,
        salary_from,
        salary_to,
        commission,
        commission_warranty_months,
        working_location,
        working_time,
        view_count,
        referral_count,
        is_urgent,
        is_hot,
        is_saved,
        status,
        job_detail_json,
        created_at,
        updated_at,
        expired_at
    FROM recruitment_postings
    WHERE refer_code = @refer_code
    ORDER BY created_at DESC
END
GO

-- Search recruitment_postings with Pagination
IF EXISTS (SELECT * FROM sys.objects WHERE type = 'P' AND name = 'sp_SearchRecruitmentPostings')
    DROP PROCEDURE sp_SearchRecruitmentPostings
GO

CREATE PROCEDURE sp_SearchRecruitmentPostings
    @keyword NVARCHAR(255) = NULL,
    @level NVARCHAR(50) = NULL,
    @working_location NVARCHAR(255) = NULL,
    @salary_from INT = NULL,
    @salary_to INT = NULL,
    @is_urgent BIT = NULL,
    @is_hot BIT = NULL,
    @status TINYINT = 1,
    @page INT = 1,
    @page_size INT = 10,
    @sort_by NVARCHAR(50) = 'created_at',
    @sort_order NVARCHAR(10) = 'desc',
    @total_count INT OUTPUT
AS
BEGIN
    SET NOCOUNT ON;

    DECLARE @sql NVARCHAR(MAX);
    DECLARE @where_clause NVARCHAR(MAX) = '';
    DECLARE @order_clause NVARCHAR(100);
    DECLARE @offset INT = (@page - 1) * @page_size;

    -- Build WHERE clause
    SET @where_clause = 'WHERE status = ' + CAST(@status AS NVARCHAR(5));

    IF @keyword IS NOT NULL AND @keyword != ''
        SET @where_clause = @where_clause + ' AND (title LIKE ''%' + @keyword + '%'' OR working_location LIKE ''%' + @keyword + '%'' OR job_detail_json LIKE ''%' + @keyword + '%'')';

    IF @level IS NOT NULL AND @level != ''
        SET @where_clause = @where_clause + ' AND level = ''' + @level + '''';

    IF @working_location IS NOT NULL AND @working_location != ''
        SET @where_clause = @where_clause + ' AND working_location LIKE ''%' + @working_location + '%''';

    IF @salary_from IS NOT NULL
        SET @where_clause = @where_clause + ' AND salary_from >= ' + CAST(@salary_from AS NVARCHAR(20));

    IF @salary_to IS NOT NULL
        SET @where_clause = @where_clause + ' AND salary_to <= ' + CAST(@salary_to AS NVARCHAR(20));

    IF @is_urgent IS NOT NULL
        SET @where_clause = @where_clause + ' AND is_urgent = ' + CAST(@is_urgent AS NVARCHAR(1));

    IF @is_hot IS NOT NULL
        SET @where_clause = @where_clause + ' AND is_hot = ' + CAST(@is_hot AS NVARCHAR(1));

    -- Build ORDER BY clause
    IF @sort_by NOT IN ('created_at', 'salary_from', 'view_count', 'title')
        SET @sort_by = 'created_at';

    IF @sort_order NOT IN ('asc', 'desc')
        SET @sort_order = 'desc';

    SET @order_clause = 'ORDER BY ' + @sort_by + ' ' + @sort_order;

    -- Get total count
    SET @sql = 'SELECT @total_count = COUNT(*) FROM recruitment_postings ' + @where_clause;
    EXEC sp_executesql @sql, N'@total_count INT OUTPUT', @total_count OUTPUT;

    -- Get paginated data
    SET @sql = '
    SELECT
        posting_id, refer_code, title, project, level, position,
        salary_from, salary_to, commission, commission_warranty_months,
        working_location, working_time, view_count, referral_count,
        is_urgent, is_hot, is_saved, status, job_detail_json,
        created_at, updated_at, expired_at
    FROM recruitment_postings ' + @where_clause + ' ' + @order_clause + '
    OFFSET ' + CAST(@offset AS NVARCHAR(20)) + ' ROWS
    FETCH NEXT ' + CAST(@page_size AS NVARCHAR(20)) + ' ROWS ONLY';

    EXEC sp_executesql @sql;
END
GO

-- Create RecruitmentPosting
IF EXISTS (SELECT * FROM sys.objects WHERE type = 'P' AND name = 'sp_CreateRecruitmentPosting')
    DROP PROCEDURE sp_CreateRecruitmentPosting
GO

CREATE PROCEDURE sp_CreateRecruitmentPosting
    @posting_id UNIQUEIDENTIFIER,
    @refer_code NVARCHAR(50),
    @title NVARCHAR(255),
    @project NVARCHAR(255) = NULL,
    @level NVARCHAR(50) = NULL,
    @position NVARCHAR(100) = NULL,
    @salary_from INT = NULL,
    @salary_to INT = NULL,
    @commission INT = NULL,
    @commission_warranty_months INT = NULL,
    @working_location NVARCHAR(255) = NULL,
    @working_time NVARCHAR(100) = NULL,
    @view_count INT = 0,
    @referral_count INT = 0,
    @is_urgent BIT = 0,
    @is_hot BIT = 0,
    @is_saved BIT = 0,
    @status TINYINT = 1,
    @job_detail_json NVARCHAR(MAX) = NULL,
    @created_at DATETIME = NULL,
    @updated_at DATETIME = NULL,
    @expired_at DATETIME = NULL
AS
BEGIN
    INSERT INTO recruitment_postings (
        posting_id, refer_code, title, project, level, position,
        salary_from, salary_to, commission, commission_warranty_months,
        working_location, working_time, view_count, referral_count,
        is_urgent, is_hot, is_saved, status, job_detail_json,
        created_at, updated_at, expired_at
    ) VALUES (
        @posting_id, @refer_code, @title, @project, @level, @position,
        @salary_from, @salary_to, @commission, @commission_warranty_months,
        @working_location, @working_time, @view_count, @referral_count,
        @is_urgent, @is_hot, @is_saved, @status, @job_detail_json,
        GETDATE(), @updated_at, @expired_at
    )
END
GO

-- Update RecruitmentPosting
IF EXISTS (SELECT * FROM sys.objects WHERE type = 'P' AND name = 'sp_UpdateRecruitmentPosting')
    DROP PROCEDURE sp_UpdateRecruitmentPosting
GO

CREATE PROCEDURE sp_UpdateRecruitmentPosting
    @posting_id UNIQUEIDENTIFIER,
    @refer_code NVARCHAR(50),
    @title NVARCHAR(255),
    @project NVARCHAR(255) = NULL,
    @level NVARCHAR(50) = NULL,
    @position NVARCHAR(100) = NULL,
    @salary_from INT = NULL,
    @salary_to INT = NULL,
    @commission INT = NULL,
    @commission_warranty_months INT = NULL,
    @working_location NVARCHAR(255) = NULL,
    @working_time NVARCHAR(100) = NULL,
    @is_urgent BIT = 0,
    @is_hot BIT = 0,
    @is_saved BIT = 0,
    @status TINYINT = 1,
    @job_detail_json NVARCHAR(MAX) = NULL,
    @updated_at DATETIME = NULL,
    @expired_at DATETIME = NULL
AS
BEGIN
    UPDATE recruitment_postings SET
        refer_code = @refer_code,
        title = @title,
        project = @project,
        level = @level,
        position = @position,
        salary_from = @salary_from,
        salary_to = @salary_to,
        commission = @commission,
        commission_warranty_months = @commission_warranty_months,
        working_location = @working_location,
        working_time = @working_time,
        is_urgent = @is_urgent,
        is_hot = @is_hot,
        is_saved = @is_saved,
        status = @status,
        job_detail_json = @job_detail_json,
        updated_at = GETDATE(),
        expired_at = @expired_at
    WHERE posting_id = @posting_id
END
GO

-- Delete RecruitmentPosting
IF EXISTS (SELECT * FROM sys.objects WHERE type = 'P' AND name = 'sp_DeleteRecruitmentPosting')
    DROP PROCEDURE sp_DeleteRecruitmentPosting
GO

CREATE PROCEDURE sp_DeleteRecruitmentPosting
    @posting_id UNIQUEIDENTIFIER
AS
BEGIN
    DELETE FROM recruitment_postings
    WHERE posting_id = @posting_id
END
GO

-- Increment View Count
IF EXISTS (SELECT * FROM sys.objects WHERE type = 'P' AND name = 'sp_IncrementRecruitmentPostingViewCount')
    DROP PROCEDURE sp_IncrementRecruitmentPostingViewCount
GO

CREATE PROCEDURE sp_IncrementRecruitmentPostingViewCount
    @posting_id UNIQUEIDENTIFIER
AS
BEGIN
    UPDATE recruitment_postings
    SET view_count = view_count + 1
    WHERE posting_id = @posting_id
END
GO

-- Increment Referral Count
IF EXISTS (SELECT * FROM sys.objects WHERE type = 'P' AND name = 'sp_IncrementRecruitmentPostingReferralCount')
    DROP PROCEDURE sp_IncrementRecruitmentPostingReferralCount
GO

CREATE PROCEDURE sp_IncrementRecruitmentPostingReferralCount
    @posting_id UNIQUEIDENTIFIER
AS
BEGIN
    UPDATE recruitment_postings
    SET referral_count = referral_count + 1
    WHERE posting_id = @posting_id
END
GO
