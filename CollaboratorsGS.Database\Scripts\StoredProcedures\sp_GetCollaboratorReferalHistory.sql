-- Stored Procedure to get collaborator referral history by userId
IF EXISTS (SELECT * FROM sys.objects WHERE type = 'P' AND name = 'sp_GetCollaboratorReferalHistory')
    DROP PROCEDURE sp_GetCollaboratorReferalHistory
GO

CREATE PROCEDURE sp_GetCollaboratorReferalHistory
    @UserId UNIQUEIDENTIFIER
AS
BEGIN
    SELECT 
        c.candidate_id,
        c.full_name as full_name_candidate,
        c.phone_number,
        ca.application_date,
        cd.file_path as profile_picture_url,
        cd.document_type,
        cd.file_type,
        ca.status
    FROM candidates c
    INNER JOIN collaborators ctv ON c.collaborator_id = ctv.collaborator_id
    LEFT JOIN candidate_applications ca ON c.candidate_id = ca.candidate_id
    LEFT JOIN candidate_documents cd ON c.candidate_id = cd.candidate_id 
        AND cd.document_type IN ('pdf', 'jpg', 'png','docx')
    WHERE ctv.user_id = @UserId
    ORDER BY ca.application_date DESC, c.created_at DESC
END
GO
