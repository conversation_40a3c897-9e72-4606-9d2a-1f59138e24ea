using System;
using System.IO;
using System.Linq;
using System.Security.Claims;
using System.Threading.Tasks;
using System.Collections.Generic;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using CollaboratorsGS.Domain.Entities;
using CollaboratorsGS.Domain.Repositories;

namespace CollaboratorsGS.Infrastructure.Middlewares
{
    public class RequestLoggingMiddleware
    {
        private readonly RequestDelegate _next;
        private readonly ILogger<RequestLoggingMiddleware> _logger;
        private readonly IAuditLogRepository _auditLogRepository;

        public RequestLoggingMiddleware(
            RequestDelegate next, 
            ILogger<RequestLoggingMiddleware> logger,
            IAuditLogRepository auditLogRepository)
        {
            _next = next;
            _logger = logger;
            _auditLogRepository = auditLogRepository;
        }

        public async Task InvokeAsync(HttpContext context)
        {
            var requestPath = context.Request.Path;
            var requestMethod = context.Request.Method;
            var parameters = context.Request.Query;
            
            // Đọc request body nếu không phải GET
            string requestBody = string.Empty;
            if (!HttpMethods.IsGet(requestMethod))
            {
                requestBody = await ReadRequestBodyAsync(context.Request);
            }
            
            try
            {
                // Lấy thông tin user ID
                var userIdClaim = context.User.FindFirst(ClaimTypes.NameIdentifier) 
                    ?? context.User.FindFirst("sub")
                    ?? context.User.FindFirst("user_id")
                    ?? context.User.FindFirst("id");
                
                Guid? userId = null;
                if (userIdClaim != null && Guid.TryParse(userIdClaim.Value, out var parsedUserId))
                {
                    userId = parsedUserId;
                }
                
                // Lấy địa chỉ IP
                string publicIpAddress = GetPublicIpAddress(context);
                
                // Tiếp tục xử lý request
                await _next(context);
                
                // Sau khi request được xử lý, ghi log với status dựa trên status code
                // Chỉ ghi log cho các phương thức không phải GET
                if (userId.HasValue && !HttpMethods.IsGet(requestMethod))
                {
                    // Chuyển đổi thời gian từ UTC sang GMT+7 (Việt Nam)
                    DateTime vietnamTime = DateTime.UtcNow.AddHours(7);
                    
                    // Xác định status dựa trên status code
                    string status = context.Response.StatusCode >= 200 && context.Response.StatusCode < 400 
                        ? "SUCCESS" 
                        : "FAILED";
                    
                    // Tạo một Guid từ path để sử dụng cho EntityId
                    Guid entityId = Guid.Empty;

                    // Tạo một chuỗi chứa tất cả các parameters từ query string
#pragma warning disable CS8604 // Possible null reference argument.
                    string allParameters = string.Join(", ", parameters.Select(static p => $"{p.Key}={string.Join(",", p.Value)}"));
#pragma warning restore CS8604 // Possible null reference argument.

                    // Tạo một chuỗi chứa tất cả các parameters từ route
                    string routeParameters = string.Join(", ", context.Request.RouteValues.Select(p => $"{p.Key}={p.Value}"));

                    // Ghi log tất cả các parameters
                    _logger.LogDebug("Query Parameters: {QueryParams}", allParameters);
                    _logger.LogDebug("Route Parameters: {RouteParams}", routeParameters);

                    // Nếu vẫn muốn lấy entityId từ parameters (nếu có)
                    if (parameters.ContainsKey("id"))
                    {
                        string idValue = parameters["id"].FirstOrDefault() ?? string.Empty;
                        _logger.LogDebug("ID Parameter: {IdValue}", idValue);
                        
                        if (Guid.TryParse(idValue, out var idGuid))
                        {
                            entityId = idGuid;
                        }
                    }
                    // Hoặc từ route values
                    else if (context.Request.RouteValues.ContainsKey("id"))
                    {
                        string? idValue = context.Request.RouteValues["id"]?.ToString();
                        _logger.LogDebug("ID Route Value: {IdValue}", idValue);
                        
                        if (!string.IsNullOrEmpty(idValue) && Guid.TryParse(idValue, out var idGuid))
                        {
                            entityId = idGuid;
                        }
                    }
                    
                    // Lấy route parameters
                    var routeParams = context.Request.RouteValues;
                    string routeParamsString = string.Join(", ", routeParams.Select(p => $"{p.Key}={p.Value}"));

                    var auditLog = new AuditLogs
                    {
                        LogId = Guid.NewGuid(),
                        UserId = userId.Value,
                        Action = requestMethod,
                        EntityType = "Request",
                        EntityId = entityId,
                        ActionDate = vietnamTime,
                        IpAddress = publicIpAddress,
                        Status = status,
                        NewValue = $"Path: {requestPath}, StatusCode: {context.Response.StatusCode}, " +
                                   $"QueryParams: {routeParamsString}, " +
                                   $"RequestBody: {requestBody}",
                    };

                    await _auditLogRepository.CreateAsync(auditLog);
                    _logger.LogInformation("Request logged for user {UserId} from IP {IpAddress} at {LocalTime} with status {Status}", 
                        userId, publicIpAddress, vietnamTime, status);
                }
            }
            catch (Exception ex) when (LogError(ex, "Error in RequestLoggingMiddleware"))
            {
                throw;
            }
        }

        private async Task LogRequestAsync(HttpContext context)
        {
            try
            {
                // Try to get user ID from claims
                var userIdClaim = context.User.FindFirst(ClaimTypes.NameIdentifier) 
                    ?? context.User.FindFirst("sub")
                    ?? context.User.FindFirst("user_id")
                    ?? context.User.FindFirst("id");
                
                if (userIdClaim != null && Guid.TryParse(userIdClaim.Value, out var userId))
                {
                    // Get client's public IP address
                    string publicIpAddress = GetPublicIpAddress(context);
                    
                    // Chuyển đổi thời gian từ UTC sang GMT+7 (Việt Nam)
                    DateTime vietnamTime = DateTime.UtcNow.AddHours(7);
                    
                    var auditLog = new AuditLogs 
                    {
                        LogId = Guid.NewGuid(),
                        UserId = userId,
                        Action = context.Request.Method,
                        EntityType = "Request",
                        EntityId = Guid.NewGuid(),
                        ActionDate = vietnamTime, 
                        IpAddress = publicIpAddress,
                        Status = "Logged",
                        NewValue = $"Path: {context.Request.Path}"
                    };

                    await _auditLogRepository.CreateAsync(auditLog);
                    _logger.LogInformation("Request logged for user {UserId} from IP {IpAddress} at {LocalTime}", 
                        userId, publicIpAddress, vietnamTime);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to log request");
            }
        }

        private string GetPublicIpAddress(HttpContext context)
        {
            // Danh sách các header có thể chứa địa chỉ IP công cộng
            var headerNames = new[]
            {
                "X-Forwarded-For",      // Tiêu chuẩn phổ biến nhất
                "Forwarded",            // Tiêu chuẩn RFC 7239
                "X-Real-IP",            // Sử dụng bởi Nginx
                "CF-Connecting-IP",     // Cloudflare
                "True-Client-IP",       // Akamai, Cloudflare
                "X-Client-IP",          // Amazon AWS, Cloudflare
                "X-Cluster-Client-IP",  // Rackspace, Riverbed, Akamai
                "Fastly-Client-IP",     // Fastly CDN
                "X-Originating-IP"      // Microsoft
            };

            // Kiểm tra từng header theo thứ tự ưu tiên
            foreach (var headerName in headerNames)
            {
                if (context.Request.Headers.TryGetValue(headerName, out var headerValue) && 
                    !string.IsNullOrEmpty(headerValue))
                {
                    // X-Forwarded-For có thể chứa danh sách các IP, lấy IP đầu tiên
                    var ip = headerValue.ToString().Split(',')[0].Trim();
                    
                    // Đảm bảo lấy IPv4 nếu có
                    ip = EnsureIPv4Format(ip);
                    
                    // Kiểm tra xem có phải là địa chỉ IP hợp lệ không
                    if (IsValidIpAddress(ip) && !IsPrivateIpAddress(ip))
                    {
                        return ip;
                    }
                }
            }

            // Nếu không tìm thấy trong header, sử dụng RemoteIpAddress
            var remoteIp = context.Connection.RemoteIpAddress?.ToString();

            // Đảm bảo lấy IPv4
#pragma warning disable CS8604 // Possible null reference argument.
            remoteIp = EnsureIPv4Format(remoteIp);
#pragma warning restore CS8604 // Possible null reference argument.

            // Nếu là địa chỉ private, ghi log và trả về
            if (!string.IsNullOrEmpty(remoteIp) && IsPrivateIpAddress(remoteIp))
            {
                _logger.LogInformation("Client is using a private IP address: {IpAddress}", remoteIp);
            }
            
            return remoteIp ?? "Unknown";
        }

        private string EnsureIPv4Format(string ipAddress)
        {
            if (string.IsNullOrEmpty(ipAddress))
                return ipAddress;
                
            // Nếu là IPv6 được ánh xạ từ IPv4 (::ffff:***********)
            if (ipAddress.Contains("::ffff:"))
            {
                ipAddress = ipAddress.Split(':').Last();
            }
            
            // Nếu là IPv6 khác, thử chuyển đổi nếu có thể
            if (ipAddress.Contains(":") && System.Net.IPAddress.TryParse(ipAddress, out var parsedIp))
            {
                if (parsedIp.AddressFamily == System.Net.Sockets.AddressFamily.InterNetworkV6)
                {
                    // Nếu là IPv4-mapped IPv6, chuyển về IPv4
                    var ipv4Bytes = parsedIp.GetAddressBytes();
                    if (ipv4Bytes.Length >= 16)
                    {
                        try {
                            var ipv4 = new System.Net.IPAddress(new byte[] { 
                                ipv4Bytes[12], ipv4Bytes[13], ipv4Bytes[14], ipv4Bytes[15] 
                            });
                            return ipv4.ToString();
                        }
                        catch {
                            // Nếu không thể chuyển đổi, giữ nguyên địa chỉ
                        }
                    }
                }
            }
            
            return ipAddress;
        }

        private bool IsValidIpAddress(string ipAddress)
        {
            return System.Net.IPAddress.TryParse(ipAddress, out _);
        }

        private bool IsPrivateIpAddress(string ipAddress)
        {
            if (System.Net.IPAddress.TryParse(ipAddress, out var ip))
            {
                byte[] bytes = ip.GetAddressBytes();
                
                // Kiểm tra các dải địa chỉ IP private theo RFC 1918
                // 10.0.0.0/8
                if (bytes[0] == 10)
                    return true;
                
                // **********/12
                if (bytes[0] == 172 && bytes[1] >= 16 && bytes[1] <= 31)
                    return true;
                
                // ***********/16
                if (bytes[0] == 192 && bytes[1] == 168)
                    return true;
                
                // *********/8 (localhost)
                if (bytes[0] == 127)
                    return true;
                
                // ***********/16 (APIPA)
                if (bytes[0] == 169 && bytes[1] == 254)
                    return true;
            }
            
            return false;
        }

        private async Task<string> ReadRequestBodyAsync(HttpRequest request)
        {
            // Lưu vị trí hiện tại của stream
            request.EnableBuffering();
            
            try
            {
                // Đặt lại vị trí stream về đầu
                request.Body.Position = 0;
                
                // Đọc stream vào một chuỗi
                using var reader = new StreamReader(
                    request.Body,
                    encoding: System.Text.Encoding.UTF8,
                    detectEncodingFromByteOrderMarks: false,
                    leaveOpen: true);
                
                var body = await reader.ReadToEndAsync();
                
                // Log body để debug
                _logger.LogDebug("Request body: {Body}", body);
                
                // Đặt lại vị trí stream để middleware khác có thể đọc
                request.Body.Position = 0;
                
                // Nếu là JSON, thử phân tích thành đối tượng
                if (request.ContentType != null && request.ContentType.Contains("application/json"))
                {
                    try
                    {
                        // Phân tích JSON thành đối tượng động
                        var jsonObj = System.Text.Json.JsonSerializer.Deserialize<Dictionary<string, object>>(body);
                        if (jsonObj != null)
                        {
                            // Log JSON object để debug
                            _logger.LogDebug("JSON object: {JsonObj}", jsonObj);
                            
                            // Chuyển đổi thành chuỗi key-value
                            return string.Join(", ", jsonObj.Select(kv => $"{kv.Key}={kv.Value}"));
                        }
                    }
                    catch (Exception ex)
                    {
                        // Log lỗi để debug
                        _logger.LogDebug("Error parsing JSON: {Error}", ex.Message);
                        // Nếu không thể phân tích JSON, trả về body gốc
                    }
                }
                
                return body;
            }
            catch (Exception ex)
            {
                // Log lỗi để debug
                _logger.LogDebug("Error reading request body: {Error}", ex.Message);
                // Nếu có lỗi, đảm bảo stream được đặt lại
                request.Body.Position = 0;
                return string.Empty;
            }
        }

        // Phương thức để lấy thông tin chi tiết về lỗi
        private string GetDetailedErrorMessage(Exception ex)
        {
            var errorDetails = new System.Text.StringBuilder();
            
            // Thêm message của exception chính
            errorDetails.AppendLine($"Error: {ex.Message}");
            
            // Thêm stack trace
            errorDetails.AppendLine($"StackTrace: {ex.StackTrace}");
            
            // Thêm thông tin về inner exception nếu có
            var innerEx = ex.InnerException;
            int depth = 0;
            while (innerEx != null && depth < 5) // Giới hạn độ sâu để tránh vòng lặp vô hạn
            {
                errorDetails.AppendLine($"InnerException[{depth}]: {innerEx.Message}");
                errorDetails.AppendLine($"InnerStackTrace[{depth}]: {innerEx.StackTrace}");
                innerEx = innerEx.InnerException;
                depth++;
            }
            
            // Thêm thông tin về các exception cụ thể
            if (ex is System.Net.Http.HttpRequestException httpEx)
            {
                errorDetails.AppendLine($"HttpStatusCode: {httpEx.StatusCode}");
            }
            else if (ex is System.IO.IOException ioEx)
            {
                errorDetails.AppendLine($"IOError: {ioEx.HResult}");
            }
            else if (ex is System.Data.SqlClient.SqlException sqlEx)
            {
                errorDetails.AppendLine($"SqlError: {sqlEx.Number}, Procedure: {sqlEx.Procedure}");
            }
            
            return errorDetails.ToString();
        }

        private bool LogError(Exception ex, string message)
        {
            _logger.LogError(ex, message);
            return true;
        }
    }
}





















