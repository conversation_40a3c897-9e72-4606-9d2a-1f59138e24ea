using System;
using System.ComponentModel.DataAnnotations;

namespace CollaboratorsGS.Application.DTOs.Position
{
    public class CreatePositionRequest
    {
        [Required]
        [StringLength(255)]
        public string PositionName { get; set; } = string.Empty;
        
        [Required]
        public Guid DepartmentId { get; set; }
        
        public string? Description { get; set; }
    }
}
