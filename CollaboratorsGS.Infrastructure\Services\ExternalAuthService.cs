using CollaboratorsGS.Application.Interfaces;
using Google.Apis.Auth;
using Microsoft.Extensions.Configuration;

namespace CollaboratorsGS.Infrastructure.Services
{
    public class ExternalAuthService : IExternalAuthService
    {
        private readonly IConfiguration _configuration;

        public ExternalAuthService(IConfiguration configuration)
        {
            _configuration = configuration;
        }

        public async Task<(bool isValid, string? email, string? name, string? externalId)> ValidateGoogleTokenAsync(string idToken)
        {
            try
            {
                var settings = new GoogleJsonWebSignature.ValidationSettings
                {
                    Audience = new[] { _configuration["Authentication:Google:ClientId"] ?? "" }
                };

                var payload = await GoogleJsonWebSignature.ValidateAsync(idToken, settings);
                
                return (true, payload.Email, payload.Name, payload.Subject);
            }
            catch (Exception)
            {
                return (false, null, null, null);
            }
        }
    }
}
