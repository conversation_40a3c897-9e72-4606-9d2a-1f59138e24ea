using System;

namespace CollaboratorsGS.Application.DTOs.CollaboratorViolation
{
    public class CollaboratorViolationDto
    {
        public Guid ViolationId { get; set; }
        public Guid CtvId { get; set; }
        public string CollaboratorName { get; set; } = string.Empty;
        public string ViolationType { get; set; } = string.Empty;
        public string? Description { get; set; }
        public DateTime CreatedAt { get; set; }
        public Guid? HandledBy { get; set; }
        public string? HandlerName { get; set; }
        public DateTime? HandledAt { get; set; }
    }
}
