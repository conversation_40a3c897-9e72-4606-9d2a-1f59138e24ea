using System;
using System.ComponentModel.DataAnnotations;

namespace CollaboratorsGS.Application.DTOs.Department
{
    public class UpdateDepartmentRequest
    {
        [Required]
        public Guid DepartmentId { get; set; }
        
        [Required]
        [StringLength(255)]
        public string DepartmentName { get; set; } = string.Empty;
        
        public Guid? ManagerId { get; set; }
    }
}
