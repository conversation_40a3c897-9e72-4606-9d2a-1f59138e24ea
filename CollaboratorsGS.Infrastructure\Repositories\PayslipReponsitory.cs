using CollaboratorsGS.Domain.Entities;
using CollaboratorsGS.Domain.Repositories;
using CollaboratorsGS.Infrastructure.Data;
using Dapper;
using Microsoft.Extensions.Configuration;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace CollaboratorsGS.Infrastructure.Repositories
{
    public class PayslipRepository : IPayslipRepository
    {
        private readonly string _connectionString; // Replace with your actual connection string
        private readonly IConnectionFactory _connectionFactory;
        public PayslipRepository(IConnectionFactory connectionFactory , IConfiguration configuration)
        {
            _connectionFactory = connectionFactory;
            _connectionString = configuration.GetConnectionString("ECMConnection");
        }

        public async Task<Payslip> GetPayslipAsync(int Month, int Year, int user_id, int approveview, int payroll_id)
        {
            using (var connection = new SqlConnection(_connectionString)) //---ConnectionString
            {
                var paramaters = new DynamicParameters();
                paramaters.Add("@Month", Month);
                paramaters.Add("@Year", Year);
                paramaters.Add("@EmployeeId", user_id);
                paramaters.Add("@payrollid", payroll_id);
                paramaters.Add("@approveview", approveview);
                DataTable dt1 = new DataTable();
                var val = connection.ExecuteReaderAsync("[dbo].[Get_payslipconfigbyId_v2]", paramaters, commandType: CommandType.StoredProcedure);
                dt1.Load(val.Result);
                val.Dispose();

                var timesheetparamaters = new DynamicParameters();
                timesheetparamaters.Add("@Month", Month);
                timesheetparamaters.Add("@Year", Year);
                timesheetparamaters.Add("@EmployeeId", user_id);
                timesheetparamaters.Add("@payrollid", payroll_id);
                timesheetparamaters.Add("@approveview", approveview);
                var result = await connection.QueryAsync<PayslipDetail>("Get_payslipTimeSheetId_V2", timesheetparamaters, null, null, System.Data.CommandType.StoredProcedure);

                var salaryparamaters = new DynamicParameters();
                salaryparamaters.Add("@Month", Month);
                salaryparamaters.Add("@Year", Year);
                salaryparamaters.Add("@EmployeeId", user_id);
                salaryparamaters.Add("@payrollid", payroll_id);
                salaryparamaters.Add("@approveview", approveview);
                var val_salary = await connection.QueryAsync<Salarydetail>("[dbo].[Get_payslipdetailbyId_V2]", salaryparamaters, commandType: CommandType.StoredProcedure);

                return new Payslip { year = Year, month = Month, PayslipConfig = dt1, TimeSheet = result.ToList(), salary = val_salary.ToList() };
            }
        }
    }
}
