-- Stored Procedures for Position operations

-- Get Position by ID
IF EXISTS (SELECT * FROM sys.objects WHERE type = 'P' AND name = 'sp_GetPositionById')
    DROP PROCEDURE sp_GetPositionById
GO

CREATE PROCEDURE sp_GetPositionById
    @position_id UNIQUEIDENTIFIER
AS
BEGIN
    SELECT p.*, d.department_name
    FROM positions p
    INNER JOIN departments d ON p.department_id = d.department_id
    WHERE p.position_id = @position_id
END
GO

-- Get All Positions
IF EXISTS (SELECT * FROM sys.objects WHERE type = 'P' AND name = 'sp_GetAllPositions')
    DROP PROCEDURE sp_GetAllPositions
GO

CREATE PROCEDURE sp_GetAllPositions
AS
BEGIN
    SELECT p.*, d.department_name
    FROM positions p
    INNER JOIN departments d ON p.department_id = d.department_id
    ORDER BY p.position_name
END
GO

-- Get Positions by Department
IF EXISTS (SELECT * FROM sys.objects WHERE type = 'P' AND name = 'sp_GetPositionsByDepartment')
    DROP PROCEDURE sp_GetPositionsByDepartment
GO

CREATE PROCEDURE sp_GetPositionsByDepartment
    @department_id UNIQUEIDENTIFIER
AS
BEGIN
    SELECT p.*, d.department_name
    FROM positions p
    INNER JOIN departments d ON p.department_id = d.department_id
    WHERE p.department_id = @department_id
    ORDER BY p.position_name
END
GO

-- Create Position
IF EXISTS (SELECT * FROM sys.objects WHERE type = 'P' AND name = 'sp_CreatePosition')
    DROP PROCEDURE sp_CreatePosition
GO

CREATE PROCEDURE sp_CreatePosition
    @position_id UNIQUEIDENTIFIER,
    @position_name VARCHAR(255),
    @department_id UNIQUEIDENTIFIER,
    @description VARCHAR(255)
AS
BEGIN
    INSERT INTO positions (position_id, position_name, department_id, description)
    VALUES (@position_id, @position_name, @department_id, @description)
END
GO

-- Update Position
IF EXISTS (SELECT * FROM sys.objects WHERE type = 'P' AND name = 'sp_UpdatePosition')
    DROP PROCEDURE sp_UpdatePosition
GO

CREATE PROCEDURE sp_UpdatePosition
    @position_id UNIQUEIDENTIFIER,
    @position_name VARCHAR(255),
    @department_id UNIQUEIDENTIFIER,
    @description VARCHAR(255)
AS
BEGIN
    UPDATE positions
    SET position_name = @position_name,
        department_id = @department_id,
        description = @description
    WHERE position_id = @position_id
END
GO

-- Delete Position
IF EXISTS (SELECT * FROM sys.objects WHERE type = 'P' AND name = 'sp_DeletePosition')
    DROP PROCEDURE sp_DeletePosition
GO

CREATE PROCEDURE sp_DeletePosition
    @position_id UNIQUEIDENTIFIER
AS
BEGIN
    DELETE FROM positions
    WHERE position_id = @position_id
END
GO
