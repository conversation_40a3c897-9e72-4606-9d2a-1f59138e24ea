using System;
using System.ComponentModel.DataAnnotations;

namespace CollaboratorsGS.Application.DTOs.CollaboratorReward
{
    public class CreateCollaboratorRewardRequest
    {
        [Required]
        public Guid CollaboratorId { get; set; }
        
        [Required]
        public Guid ApplicationId { get; set; }
        
        [Required]
        [StringLength(50, ErrorMessage = "Reward type cannot exceed 50 characters")]
        public string RewardType { get; set; } = string.Empty;
        
        [Required]
        [Range(0.01, double.MaxValue, ErrorMessage = "Amount must be greater than 0")]
        public decimal Amount { get; set; }
        
        [Required]
        public Guid LevelId { get; set; }
        
        [Required]
        public DateTime ScheduledPaymentDate { get; set; }
    }
}
