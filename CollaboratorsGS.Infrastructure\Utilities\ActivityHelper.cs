namespace CollaboratorsGS.Infrastructure.Utilities
{
    public static class ActivityHelper
    {
        public static string GetTimeAgo(DateTime dateTime)
        {
            var timeSpan = DateTime.UtcNow - dateTime;
            
            if (timeSpan.TotalMinutes < 1) return "Vừa xong";
            if (timeSpan.TotalMinutes < 60) return $"{(int)timeSpan.TotalMinutes} phút trước";
            if (timeSpan.TotalHours < 24) return $"{(int)timeSpan.TotalHours} giờ trước";
            if (timeSpan.TotalDays < 7) return $"{(int)timeSpan.TotalDays} ngày trước";
            
            return dateTime.ToString("dd/MM/yyyy");
        }



        public static string GetActivityTitle(string action, string entityType, string? entityName = null)
        {
            // Skip HTTP request logs
            if (entityType.Equals("Request", StringComparison.OrdinalIgnoreCase))
            {
                return "Hoạt động hệ thống";
            }

            var actionText = action.ToLower() switch
            {
                "create" => "đã được tạo",
                "update" => "đã được cập nhật",
                "delete" => "đã được xóa",
                "approve" => "đã được phê duyệt",
                "reject" => "đã bị từ chối",
                "interview" => "đã được phỏng vấn",
                "onboard" => "đã được onboard",
                "pay" => "đã được thanh toán",
                _ => "đã được thay đổi"
            };

            var entityText = entityType.ToLower() switch
            {
                "candidate" => "Ứng viên",
                "recruitmentposting" => "Tin tuyển dụng",
                "collaboratorreward" => "Hoa hồng",
                "candidateapplication" => "Đơn ứng tuyển",
                "collaborator" => "Cộng tác viên",
                _ => "Mục"
            };

            // If we have entity name, include it in the title
            if (!string.IsNullOrEmpty(entityName))
            {
                // Truncate long names to keep title readable
                var displayName = entityName.Length > 30 ? $"{entityName[..27]}..." : entityName;
                return $"{entityText} {displayName} {actionText}";
            }

            return $"{entityText} {actionText}";
        }

        public static string GetActivityDescription(string action, string entityType)
        {
            // Skip HTTP request logs
            if (entityType.Equals("Request", StringComparison.OrdinalIgnoreCase))
            {
                return "Hoạt động hệ thống được ghi lại";
            }

            return (action.ToLower(), entityType.ToLower()) switch
            {
                ("create", "candidate") => "Ứng viên mới đã được thêm vào hệ thống",
                ("update", "candidate") => "Thông tin ứng viên đã được cập nhật",
                ("create", "recruitmentposting") => "Tin tuyển dụng mới đã được đăng tải",
                ("update", "recruitmentposting") => "Thông tin tin tuyển dụng đã được cập nhật",
                ("delete", "recruitmentposting") => "Tin tuyển dụng đã được gỡ bỏ",
                ("create", "collaboratorreward") => "Hoa hồng mới đã được tạo",
                ("update", "collaboratorreward") => "Thông tin hoa hồng đã được cập nhật",
                ("pay", "collaboratorreward") => "Hoa hồng đã được thanh toán",
                ("create", "candidateapplication") => "Đơn ứng tuyển mới đã được tạo",
                ("update", "candidateapplication") => "Trạng thái đơn ứng tuyển đã được cập nhật",
                ("interview", "candidateapplication") => "Ứng viên đã được phỏng vấn",
                ("approve", "candidateapplication") => "Đơn ứng tuyển đã được phê duyệt",
                ("reject", "candidateapplication") => "Đơn ứng tuyển đã bị từ chối",
                ("onboard", "candidateapplication") => "Ứng viên đã được onboard thành công",
                _ => "Hoạt động đã được thực hiện"
            };
        }
    }
}
