using Microsoft.AspNetCore.Mvc.ModelBinding;
using CollaboratorsGS.Application.DTOs.Common;

namespace CollaboratorsGS.API.ModelBinders
{
    public class DateTimeModelBinder : IModelBinder
    {
        public Task BindModelAsync(ModelBindingContext bindingContext)
        {
            ArgumentNullException.ThrowIfNull(bindingContext);

            var value = bindingContext.ValueProvider.GetValue(bindingContext.ModelName);

            if (value == ValueProviderResult.None)
            {
                return Task.CompletedTask;
            }

            bindingContext.ModelState.SetModelValue(bindingContext.ModelName, value);

            var stringValue = value.FirstValue;

            if (string.IsNullOrEmpty(stringValue))
            {
                bindingContext.Result = ModelBindingResult.Success(null);
                return Task.CompletedTask;
            }

            // Try to parse date using DateHelper
            var parsedDate = DateHelper.ParseDateString(stringValue);
            if (parsedDate.HasValue)
            {
                bindingContext.Result = ModelBindingResult.Success(parsedDate.Value);
                return Task.CompletedTask;
            }

            // If parsing fails, try default parsing
            if (DateTime.TryParse(stringValue, out DateTime defaultResult))
            {
                bindingContext.Result = ModelBindingResult.Success(defaultResult);
                return Task.CompletedTask;
            }

            bindingContext.ModelState.TryAddModelError(bindingContext.ModelName, "Invalid date format. Expected format: dd/MM/yyyy");
            bindingContext.Result = ModelBindingResult.Failed();

            return Task.CompletedTask;
        }
    }

    public class DateTimeModelBinderProvider : IModelBinderProvider
    {
        public IModelBinder? GetBinder(ModelBinderProviderContext context)
        {
            if (context.Metadata.ModelType == typeof(DateTime) || context.Metadata.ModelType == typeof(DateTime?))
            {
                return new DateTimeModelBinder();
            }

            return null;
        }
    }
}
