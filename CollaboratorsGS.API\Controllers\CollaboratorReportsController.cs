using CollaboratorsGS.Application.Constants;
using CollaboratorsGS.Application.DTOs;
using CollaboratorsGS.Application.DTOs.CollaboratorReport;
using CollaboratorsGS.Application.Interfaces;
using CollaboratorsGS.Infrastructure.Utilities;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace CollaboratorsGS.API.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    [Authorize]
    public class CollaboratorReportsController : ControllerBase
    {
        private readonly ICollaboratorReportService _reportService;
        private readonly ICollaboratorDashboardService _dashboardService;
        private readonly ILogger<CollaboratorReportsController> _logger;

        public CollaboratorReportsController(
            ICollaboratorReportService reportService,
            ICollaboratorDashboardService dashboardService,
            ILogger<CollaboratorReportsController> logger)
        {
            _reportService = reportService;
            _dashboardService = dashboardService;
            _logger = logger;
        }

        // GET: api/CtvReports
        [HttpGet]
        [Authorize(Roles = RolesUser.AdminManager)]
        public async Task<IActionResult> GetAll()
        {
            try
            {
                var reports = await _reportService.GetAllAsync();
                return Ok(ApiResponse<object>.SuccessResponse(
                    MessageCodes.SC2000,
                    "Get all reports successfully",
                    reports));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting all reports");
                return StatusCode(500, ApiResponse<object>.ErrorResponse(
                    MessageCodes.ER5000,
                    "Internal server error " + ex.Message,
                    500));
            }
        }

        // GET: api/CtvReports/{id}
        [HttpGet("{reportId}")]
        public async Task<IActionResult> GetById(Guid reportId)
        {
            try
            {
                var report = await _reportService.GetByIdAsync(reportId);

                if (report == null)
                    return NotFound(ApiResponse<object>.ErrorResponse(
                        MessageCodes.ER4004,
                        "Report not found",
                        404));

                return Ok(ApiResponse<object>.SuccessResponse(
                    MessageCodes.SC2000,
                    "Get report by reportId successfully",
                    report));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting report with ID {ReportId}", reportId);
                return StatusCode(500, ApiResponse<object>.ErrorResponse(
                    MessageCodes.ER5000,
                    "Internal server error",
                    500));
            }
        }

        // GET: api/CtvReports/collaborator/{ctvId}
        [HttpGet("collaborator/{collaboratorId}")]
        public async Task<IActionResult> GetByCollaborator(Guid collaboratorId)
        {
            try
            {
                var reports = await _reportService.GetByCollaboratorIdAsync(collaboratorId);
                return Ok(ApiResponse<object>.SuccessResponse(
                    MessageCodes.SC2000,
                    "Get reports by collaborator successfully",
                    reports));
            }
            catch (InvalidOperationException ex)
            {
                _logger.LogWarning(ex, "Invalid operation: {Message}", ex.Message);
                return BadRequest(ApiResponse<object>.ErrorResponse(
                    MessageCodes.ER4005,
                    ex.Message,
                    400,
                    new List<ErrorDetail>
                    {
                        new ErrorDetail
                        {
                            Field = "collaboratorId",
                            ErrorCode = MessageCodes.ER4005,
                            Message = ex.Message
                        }
                    }));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting reports for collaborator {collaboratorId}", collaboratorId);
                return StatusCode(500, ApiResponse<object>.ErrorResponse(
                    MessageCodes.ER5000,
                    "Internal server error",
                    500));
            }
        }

        // GET: api/CtvReports/period/{period}
        [HttpGet("period/{period}")]
        [Authorize(Roles = RolesUser.AdminManager)]
        public async Task<IActionResult> GetByPeriod(string period)
        {
            try
            {
                var reports = await _reportService.GetByPeriodAsync(period);
                return Ok(ApiResponse<object>.SuccessResponse(
                    MessageCodes.SC2000,
                    "Get reports by period successfully",
                    reports));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting reports for period {Period}", period);
                return StatusCode(500, ApiResponse<object>.ErrorResponse(
                    MessageCodes.ER5000,
                    "Internal server error",
                    500));
            }
        }

        // POST: api/CtvReports
        [HttpPost]
        [Authorize(Roles = RolesUser.AdminManager)]
        public async Task<IActionResult> Create([FromBody] CreateCollaboratorReportRequest request)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return ValidationHelper.CreateValidationErrorResponse(ModelState);
                }
                var reportId = await _reportService.CreateCollaboratorReportAsync(request);
                return CreatedAtAction(nameof(GetById), new { ReportId = reportId },
                    ApiResponse<object>.SuccessResponse(
                        MessageCodes.SC2001,
                        "Report created successfully",
                        new { ReportId = reportId },
                        201));
            }
            catch (InvalidOperationException ex)
            {
                _logger.LogWarning(ex, "Invalid operation: {Message}", ex.Message);
                return BadRequest(ApiResponse<object>.ErrorResponse(
                    MessageCodes.ER4005,
                    ex.Message,
                    400,
                    new List<ErrorDetail>
                    {
                        new ErrorDetail
                        {
                            Field = "request",
                            ErrorCode = MessageCodes.ER4005,
                            Message = ex.Message
                        }
                    }));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating report");
                return StatusCode(500, ApiResponse<object>.ErrorResponse(
                    MessageCodes.ER5000,
                    "Internal server error",
                    500));
            }
        }

        // PUT: api/CtvReports/{id}
        [HttpPut("{reportId}")]
        [Authorize(Roles = RolesUser.AdminManager)]
        public async Task<IActionResult> Update(Guid reportId, [FromBody] UpdateCollaboratorReportRequest request)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return ValidationHelper.CreateValidationErrorResponse(ModelState);
                }
                if (reportId != request.ReportId)
                    return BadRequest(ApiResponse<object>.ErrorResponse(
                        MessageCodes.ER4001,
                        "ID mismatch",
                        400,
                        new List<ErrorDetail>
                        {
                            new ErrorDetail
                            {
                                Field = "reportId",
                                ErrorCode = MessageCodes.ER4001,
                                Message = "Report ID in URL does not match request body"
                            }
                        }));

                var result = await _reportService.UpdateCollaboratorReportAsync(request);

                if (result == null)
                    return NotFound(ApiResponse<object>.ErrorResponse(
                        MessageCodes.ER4004,
                        "Report not found",
                        404));

                return Ok(ApiResponse<object>.SuccessResponse(
                    MessageCodes.SC2002,
                    "Report updated successfully",
                    result));
            }
            catch (InvalidOperationException ex)
            {
                _logger.LogWarning(ex, "Invalid operation: {Message}", ex.Message);
                return BadRequest(ApiResponse<object>.ErrorResponse(
                    MessageCodes.ER4005,
                    ex.Message,
                    400,
                    new List<ErrorDetail>
                    {
                        new ErrorDetail
                        {
                            Field = "request",
                            ErrorCode = MessageCodes.ER4005,
                            Message = ex.Message
                        }
                    }));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating report with ID {ReportId}", reportId);
                return StatusCode(500, ApiResponse<object>.ErrorResponse(
                    MessageCodes.ER5000,
                    "Internal server error",
                    500));
            }
        }

        // DELETE: api/CtvReports/{id}
        [HttpDelete("{reportId}")]
        [Authorize(Roles = RolesUser.Admin)]
        public async Task<IActionResult> Delete(Guid reportId)
        {
            try
            {
                var result = await _reportService.DeleteCollaboratorReportAsync(reportId);

                if (!result)
                    return NotFound(ApiResponse<object>.ErrorResponse(
                        MessageCodes.ER4004,
                        "Report not found",
                        404));

                return Ok(ApiResponse<object>.SuccessResponse(
                    MessageCodes.SC2003,
                    "Report deleted successfully",
                    true));
            }
            catch (InvalidOperationException ex)
            {
                _logger.LogWarning(ex, "Invalid operation: {Message}", ex.Message);
                return BadRequest(ApiResponse<object>.ErrorResponse(
                    MessageCodes.ER4005,
                    ex.Message,
                    400,
                    new List<ErrorDetail>
                    {
                        new ErrorDetail
                        {
                            Field = "reportId",
                            ErrorCode = MessageCodes.ER4005,
                            Message = ex.Message
                        }
                    }));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting report with ID {ReportId}", reportId);
                return StatusCode(500, ApiResponse<object>.ErrorResponse(
                    MessageCodes.ER5000,
                    "Internal server error",
                    500));
            }
        }

        // GET: api/CollaboratorReports/dashboard/{collaboratorId}
        [HttpGet("dashboard/{collaboratorId}")]
        [Authorize(Roles = RolesUser.AdminManagerRecruiterCollaborator)]
        public async Task<IActionResult> GetDashboardReport(Guid collaboratorId)
        {
            try
            {
                var dashboardReport = await _dashboardService.GetDashboardReportAsync(collaboratorId);

                if (dashboardReport == null)
                    return NotFound(ApiResponse<object>.ErrorResponse(
                        MessageCodes.ER4004,
                        $"Collaborator with ID {collaboratorId} not found",
                        404));

                return Ok(ApiResponse<object>.SuccessResponse(
                    MessageCodes.SC2000,
                    "Get dashboard report successfully",
                    dashboardReport));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting dashboard report for collaborator {CollaboratorId}", collaboratorId);
                return StatusCode(500, ApiResponse<object>.ErrorResponse(
                    MessageCodes.ER5000,
                    "Internal server error " + ex.Message,
                    500));
            }
        }
    }
}
